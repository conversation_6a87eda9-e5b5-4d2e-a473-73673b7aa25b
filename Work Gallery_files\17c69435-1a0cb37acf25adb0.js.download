"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8375],{91285:(e,r,t)=>{t.d(r,{Rv:()=>r6,ZY:()=>rD});var o,n,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol("+e+")"};function a(){}var l="undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0;function u(e){return"object"==typeof e&&null!==e||"function"==typeof e}var s=Promise,c=Promise.prototype.then,d=Promise.resolve.bind(s),f=Promise.reject.bind(s);function b(e){return new s(e)}function p(e,r,t){return c.call(e,r,t)}function _(e,r,t){p(p(e,r,t),void 0,a)}function h(e,r){_(e,void 0,r)}function v(e){p(e,void 0,a)}var y=function(){var e=l&&l.queueMicrotask;if("function"==typeof e)return e;var r=d(void 0);return function(e){return p(r,e)}}();function m(e,r,t){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function g(e,r,t){try{var o;return o=m(e,r,t),d(o)}catch(e){return f(e)}}var S=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,o=t+1,n=e._elements,i=n[t];return 16384===o&&(r=e._next,o=0),--this._size,this._cursor=o,e!==r&&(this._front=r),n[t]=void 0,i},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,o=t._elements;(r!==o.length||void 0!==t._next)&&(r!==o.length||(o=(t=t._next)._elements,r=0,0!==o.length));)e(o[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}();function w(e,r){var t,o,n;e._ownerReadableStream=r,r._reader=e,"readable"===r._state?q(e):"closed"===r._state?(q(t=e),E(t)):(o=e,n=r._storedError,q(o),C(o,n))}function R(e,r){return rV(e._ownerReadableStream,r)}function T(e){"readable"===e._ownerReadableStream._state?C(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){q(e),C(e,r)}(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function P(e){return TypeError("Cannot "+e+" a stream using a released reader")}function q(e){e._closedPromise=b(function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t})}function C(e,r){void 0!==e._closedPromise_reject&&(v(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function E(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var O=i("[[AbortSteps]]"),j=i("[[ErrorSteps]]"),k=i("[[CancelSteps]]"),W=i("[[PullSteps]]"),A=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},z=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function I(e,r){if(void 0!==e&&"object"!=typeof e&&"function"!=typeof e)throw TypeError(r+" is not an object.")}function B(e,r){if("function"!=typeof e)throw TypeError(r+" is not a function.")}function F(e,r){if(("object"!=typeof e||null===e)&&"function"!=typeof e)throw TypeError(r+" is not an object.")}function L(e,r,t){if(void 0===e)throw TypeError("Parameter "+r+" is required in '"+t+"'.")}function M(e,r,t){if(void 0===e)throw TypeError(r+" is required in '"+t+"'.")}function D(e){return Number(e)}function N(e,r){var t,o,n=Number.MAX_SAFE_INTEGER,i=Number(e);if(!A(i=0===(t=i)?0:t))throw TypeError(r+" is not a finite number");if((i=0===(o=z(i))?0:o)<0||i>n)throw TypeError(r+" is outside the accepted range of 0 to "+n+", inclusive");return A(i)&&0!==i?i:0}function x(e,r){if(!rY(e))throw TypeError(r+" is not a ReadableStream.")}function Q(e){return new G(e)}function Y(e,r){e._reader._readRequests.push(r)}function H(e,r,t){var o=e._reader._readRequests.shift();t?o._closeSteps():o._chunkSteps(r)}function V(e){return e._reader._readRequests.length}function U(e){var r=e._reader;return void 0!==r&&!!X(r)}var G=function(){function e(e){if(L(e,1,"ReadableStreamDefaultReader"),x(e,"First parameter"),rH(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");w(this,e),this._readRequests=new S}return Object.defineProperty(e.prototype,"closed",{get:function(){return X(this)?this._closedPromise:f(J("closed"))},enumerable:!1,configurable:!0}),e.prototype.cancel=function(e){return(void 0===e&&(e=void 0),X(this))?void 0===this._ownerReadableStream?f(P("cancel")):R(this,e):f(J("cancel"))},e.prototype.read=function(){if(!X(this))return f(J("read"));if(void 0===this._ownerReadableStream)return f(P("read from"));var e,r,t=b(function(t,o){e=t,r=o});return Z(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},e.prototype.releaseLock=function(){if(!X(this))throw J("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");T(this)}},e}();function X(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof G}function Z(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[W](r)}function J(e){return TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}Object.defineProperties(G.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(G.prototype,i.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0}),"symbol"==typeof i.asyncIterator&&((o={})[i.asyncIterator]=function(){return this},Object.defineProperty(n=o,i.asyncIterator,{enumerable:!1}));var K=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?p(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise?p(this._ongoingPromise,t,t):t()},e.prototype._nextSteps=function(){var e,r,t=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var o=this._reader;if(void 0===o._ownerReadableStream)return f(P("iterate"));var n=b(function(t,o){e=t,r=o});return Z(o,{_chunkSteps:function(r){t._ongoingPromise=void 0,y(function(){return e({value:r,done:!1})})},_closeSteps:function(){t._ongoingPromise=void 0,t._isFinished=!0,T(o),e({value:void 0,done:!0})},_errorSteps:function(e){t._ongoingPromise=void 0,t._isFinished=!0,T(o),r(e)}}),n},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(void 0===r._ownerReadableStream)return f(P("finish iterating"));if(!this._preventCancel){var t=R(r,e);return T(r),p(t,function(){return{value:e,done:!0}},void 0)}return T(r),d({value:e,done:!0})},e}(),$={next:function(){return ee(this)?this._asyncIteratorImpl.next():f(er("next"))},return:function(e){return ee(this)?this._asyncIteratorImpl.return(e):f(er("return"))}};function ee(e){if(!u(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof K}catch(e){return!1}}function er(e){return TypeError("ReadableStreamAsyncIterator."+e+" can only be used on a ReadableSteamAsyncIterator")}void 0!==n&&Object.setPrototypeOf($,n);var et=Number.isNaN||function(e){return e!=e};function eo(e){return e.slice()}function en(e,r,t,o,n){new Uint8Array(e).set(new Uint8Array(t,o,n),r)}function ei(e,r,t){if(e.slice)return e.slice(r,t);var o=t-r,n=new ArrayBuffer(o);return en(n,0,e,r,o),n}function ea(e){return new Uint8Array(ei(e.buffer,e.byteOffset,e.byteOffset+e.byteLength))}function el(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function eu(e,r,t){if(!(!("number"!=typeof t||et(t))&&!(t<0)&&1)||t===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function es(e){e._queue=new S,e._queueTotalSize=0}var ec=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"view",{get:function(){if(!eb(this))throw eI("view");return this._view},enumerable:!1,configurable:!0}),e.prototype.respond=function(e){var r;if(!eb(this))throw eI("respond");if(L(e,1,"respond"),e=N(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");this._view.buffer,eW(this._associatedReadableByteStreamController,e)},e.prototype.respondWithNewView=function(e){var r;if(!eb(this))throw eI("respondWithNewView");if(L(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");e.buffer,eA(this._associatedReadableByteStreamController,e)},e}();Object.defineProperties(ec.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(ec.prototype,i.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var ed=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"byobRequest",{get:function(){if(!ef(this))throw eB("byobRequest");return ej(this)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!ef(this))throw eB("desiredSize");return ek(this)},enumerable:!1,configurable:!0}),e.prototype.close=function(){if(!ef(this))throw eB("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");eC(this)},e.prototype.enqueue=function(e){if(!ef(this))throw eB("enqueue");if(L(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw TypeError("The stream (in "+r+" state) is not in the readable state and cannot be enqueued to");eE(this,e)},e.prototype.error=function(e){if(void 0===e&&(e=void 0),!ef(this))throw eB("error");eO(this,e)},e.prototype[k]=function(e){e_(this),es(this);var r=this._cancelAlgorithm(e);return eq(this),r},e.prototype[W]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0){var t=this._queue.shift();this._queueTotalSize-=t.byteLength,eS(this);var o=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);e._chunkSteps(o);return}var n=this._autoAllocateChunkSize;if(void 0!==n){var i=void 0;try{i=new ArrayBuffer(n)}catch(r){e._errorSteps(r);return}var a={buffer:i,bufferByteLength:n,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a)}Y(r,e),ep(this)},e}();function ef(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ed}function eb(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ec}function ep(e){var r,t;if("readable"===(t=(r=e)._controlledReadableByteStream)._state&&!r._closeRequested&&r._started&&(U(t)&&V(t)>0||eM(t)&&eL(t)>0||ek(r)>0)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,_(e._pullAlgorithm(),function(){e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ep(e))},function(r){eO(e,r)})}}function e_(e){ew(e),e._pendingPullIntos=new S}function eh(e,r){var t,o,n,i,a=!1;"closed"===e._state&&(a=!0);var l=ev(r);"default"===r.readerType?H(e,l,a):(t=e,o=l,n=a,i=t._reader._readIntoRequests.shift(),n?i._closeSteps(o):i._chunkSteps(o))}function ev(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function ey(e,r,t,o){e._queue.push({buffer:r,byteOffset:t,byteLength:o}),e._queueTotalSize+=o}function em(e,r){var t=r.elementSize,o=r.bytesFilled-r.bytesFilled%t,n=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),i=r.bytesFilled+n,a=i-i%t,l=n,u=!1;a>o&&(l=a-r.bytesFilled,u=!0);for(var s=e._queue;l>0;){var c=s.peek(),d=Math.min(l,c.byteLength),f=r.byteOffset+r.bytesFilled;en(r.buffer,f,c.buffer,c.byteOffset,d),c.byteLength===d?s.shift():(c.byteOffset+=d,c.byteLength-=d),e._queueTotalSize-=d,eg(e,d,r),l-=d}return u}function eg(e,r,t){t.bytesFilled+=r}function eS(e){0===e._queueTotalSize&&e._closeRequested?(eq(e),rU(e._controlledReadableByteStream)):ep(e)}function ew(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function eR(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var r=e._pendingPullIntos.peek();em(e,r)&&(eP(e),eh(e._controlledReadableByteStream,r))}}function eT(e,r){var t=e._pendingPullIntos.peek();if(ew(e),"closed"===e._controlledReadableByteStream._state){var o=e._controlledReadableByteStream;if(eM(o))for(;eL(o)>0;)eh(o,eP(e))}else if(eg(e,r,t),!(t.bytesFilled<t.elementSize)){eP(e);var n=t.bytesFilled%t.elementSize;if(n>0){var i=t.byteOffset+t.bytesFilled,a=ei(t.buffer,i-n,i);ey(e,a,0,a.byteLength)}t.bytesFilled-=n,eh(e._controlledReadableByteStream,t),eR(e)}ep(e)}function eP(e){return e._pendingPullIntos.shift()}function eq(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function eC(e){var r=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===r._state){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){var t=TypeError("Insufficient bytes to fill elements in the given buffer");throw eO(e,t),t}eq(e),rU(r)}}function eE(e,r){var t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){var o=r.buffer,n=r.byteOffset,i=r.byteLength;if(e._pendingPullIntos.length>0){var a,l=e._pendingPullIntos.peek();l.buffer,l.buffer=l.buffer}ew(e),U(t)?0===V(t)?ey(e,o,n,i):(e._pendingPullIntos.length>0&&eP(e),H(t,new Uint8Array(o,n,i),!1)):eM(t)?(ey(e,o,n,i),eR(e)):ey(e,o,n,i),ep(e)}}function eO(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(e_(e),es(e),eq(e),rG(t,r))}function ej(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r,t,o,n=e._pendingPullIntos.peek(),i=new Uint8Array(n.buffer,n.byteOffset+n.bytesFilled,n.byteLength-n.bytesFilled),a=Object.create(ec.prototype);r=a,t=e,o=i,r._associatedReadableByteStreamController=t,r._view=o,e._byobRequest=a}return e._byobRequest}function ek(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function eW(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw RangeError("bytesWritten out of range")}t.buffer=t.buffer,eT(e,r)}function eA(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw RangeError("The region specified by view is larger than byobRequest");var o=r.byteLength;t.buffer=r.buffer,eT(e,o)}function ez(e,r,t,o,n,i,a){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,es(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=i,r._pullAlgorithm=o,r._cancelAlgorithm=n,r._autoAllocateChunkSize=a,r._pendingPullIntos=new S,e._readableStreamController=r,_(d(t()),function(){r._started=!0,ep(r)},function(e){eO(r,e)})}function eI(e){return TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function eB(e){return TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}Object.defineProperties(ed.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(ed.prototype,i.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function eF(e,r){e._reader._readIntoRequests.push(r)}function eL(e){return e._reader._readIntoRequests.length}function eM(e){var r=e._reader;return void 0!==r&&!!eN(r)}var eD=function(){function e(e){if(L(e,1,"ReadableStreamBYOBReader"),x(e,"First parameter"),rH(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!ef(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");w(this,e),this._readIntoRequests=new S}return Object.defineProperty(e.prototype,"closed",{get:function(){return eN(this)?this._closedPromise:f(eQ("closed"))},enumerable:!1,configurable:!0}),e.prototype.cancel=function(e){return(void 0===e&&(e=void 0),eN(this))?void 0===this._ownerReadableStream?f(P("cancel")):R(this,e):f(eQ("cancel"))},e.prototype.read=function(e){if(!eN(this))return f(eQ("read"));if(!ArrayBuffer.isView(e))return f(TypeError("view must be an array buffer view"));if(0===e.byteLength)return f(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return f(TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return f(P("read from"));var r,t,o,n=b(function(e,r){t=e,o=r});return ex(this,e,{_chunkSteps:function(e){return t({value:e,done:!1})},_closeSteps:function(e){return t({value:e,done:!0})},_errorSteps:function(e){return o(e)}}),n},e.prototype.releaseLock=function(){if(!eN(this))throw eQ("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");T(this)}},e}();function eN(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof eD}function ex(e,r,t){var o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?t._errorSteps(o._storedError):function(e,r,t){var o=e._controlledReadableByteStream,n=1;r.constructor!==DataView&&(n=r.constructor.BYTES_PER_ELEMENT);var i=r.constructor,a=r.buffer,l={buffer:a,bufferByteLength:a.byteLength,byteOffset:r.byteOffset,byteLength:r.byteLength,bytesFilled:0,elementSize:n,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(l),eF(o,t);return}if("closed"===o._state){var u=new i(l.buffer,l.byteOffset,0);t._closeSteps(u);return}if(e._queueTotalSize>0){if(em(e,l)){var s=ev(l);eS(e),t._chunkSteps(s);return}if(e._closeRequested){var c=TypeError("Insufficient bytes to fill elements in the given buffer");eO(e,c),t._errorSteps(c);return}}e._pendingPullIntos.push(l),eF(o,t),ep(e)}(o._readableStreamController,r,t)}function eQ(e){return TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function eY(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(et(t)||t<0)throw RangeError("Invalid highWaterMark");return t}function eH(e){var r=e.size;return r||function(){return 1}}function eV(e,r){I(e,r);var t,o=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===o?void 0:D(o),size:void 0===n?void 0:(B(t=n,r+" has member 'size' that"),function(e){return D(t(e))})}}function eU(e,r){if(!eJ(e))throw TypeError(r+" is not a WritableStream.")}Object.defineProperties(eD.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(eD.prototype,i.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var eG="function"==typeof AbortController,eX=function(){function e(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:F(e,"First parameter");var t,o,n,i,a,l,u,s,c,f,b,p,_,h,v,y,S,w,R,T,P,q,C,E,O=eV(r,"Second parameter"),j=(R="First parameter",I(w=e,R),T=null==w?void 0:w.abort,P=null==w?void 0:w.close,q=null==w?void 0:w.start,C=null==w?void 0:w.type,E=null==w?void 0:w.write,{abort:void 0===T?void 0:(f=T,b=w,B(f,R+" has member 'abort' that"),function(e){return g(f,b,[e])}),close:void 0===P?void 0:(p=P,_=w,B(p,R+" has member 'close' that"),function(){return g(p,_,[])}),start:void 0===q?void 0:(h=q,v=w,B(h,R+" has member 'start' that"),function(e){return m(h,v,[e])}),write:void 0===E?void 0:(y=E,S=w,B(y,R+" has member 'write' that"),function(e,r){return g(y,S,[e,r])}),type:C});if(eZ(this),void 0!==j.type)throw RangeError("Invalid type is specified");var k=eH(O);t=this,o=j,n=eY(O,1),i=k,a=Object.create(rn.prototype),l=function(){},u=function(){return d(void 0)},s=function(){return d(void 0)},c=function(){return d(void 0)},void 0!==o.start&&(l=function(){return o.start(a)}),void 0!==o.write&&(u=function(e){return o.write(e,a)}),void 0!==o.close&&(s=function(){return o.close()}),void 0!==o.abort&&(c=function(e){return o.abort(e)}),ra(t,a,l,u,s,c,n,i)}return Object.defineProperty(e.prototype,"locked",{get:function(){if(!eJ(this))throw rb("locked");return eK(this)},enumerable:!1,configurable:!0}),e.prototype.abort=function(e){return(void 0===e&&(e=void 0),eJ(this))?eK(this)?f(TypeError("Cannot abort a stream that already has a writer")):e$(this,e):f(rb("abort"))},e.prototype.close=function(){return eJ(this)?eK(this)?f(TypeError("Cannot close a stream that already has a writer")):e5(this)?f(TypeError("Cannot close an already-closing stream")):e0(this):f(rb("close"))},e.prototype.getWriter=function(){var e;if(!eJ(this))throw rb("getWriter");return e=this,new e4(e)},e}();function eZ(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new S,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function eJ(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof eX}function eK(e){return void 0!==e._writer}function e$(e,r){if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=r,null==(t=e._writableStreamController._abortController)||t.abort();var t,o=e._state;if("closed"===o||"errored"===o)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var n=!1;"erroring"===o&&(n=!0,r=void 0);var i=b(function(t,o){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:o,_reason:r,_wasAlreadyErroring:n}});return e._pendingAbortRequest._promise=i,n||e8(e,r),i}function e0(e){var r,t=e._state;if("closed"===t||"errored"===t)return f(TypeError("The stream (in "+t+" state) is not in the writable state and cannot be closed"));var o=b(function(r,t){e._closeRequest={_resolve:r,_reject:t}}),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===t&&rR(n),eu(r=e._writableStreamController,ro,0),rs(r),o}function e1(e,r){if("writable"===e._state)return void e8(e,r);e3(e)}function e8(e,r){var t,o=e._writableStreamController;e._state="erroring",e._storedError=r;var n=e._writer;void 0!==n&&re(n,r),void 0===(t=e)._inFlightWriteRequest&&void 0===t._inFlightCloseRequest&&o._started&&e3(e)}function e3(e){e._state="errored",e._writableStreamController[j]();var r=e._storedError;if(e._writeRequests.forEach(function(e){e._reject(r)}),e._writeRequests=new S,void 0===e._pendingAbortRequest)return void e6(e);var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring){t._reject(r),e6(e);return}_(e._writableStreamController[O](t._reason),function(){t._resolve(),e6(e)},function(r){t._reject(r),e6(e)})}function e5(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function e6(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&ry(r,e._storedError)}function e2(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?rg(t):rR(t)),e._backpressure=r}Object.defineProperties(eX.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(eX.prototype,i.toStringTag,{value:"WritableStream",configurable:!0});var e4=function(){function e(e){if(L(e,1,"WritableStreamDefaultWriter"),eU(e,"First parameter"),eK(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r=e._state;if("writable"===r){!e5(e)&&e._backpressure?rg(this):(t=this,rg(t),rR(t)),rv(this)}else if("erroring"===r)rS(this,e._storedError),rv(this);else if("closed"===r){o=this,rg(o),rR(o),n=this,rv(n),rm(n)}else{var t,o,n,i,a,l=e._storedError;rS(this,l),i=this,a=l,rv(i),ry(i,a)}}return Object.defineProperty(e.prototype,"closed",{get:function(){return e7(this)?this._closedPromise:f(r_("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"desiredSize",{get:function(){var e,r,t;if(!e7(this))throw r_("desiredSize");if(void 0===this._ownerWritableStream)throw rh("desiredSize");return e=this,"errored"===(t=(r=e._ownerWritableStream)._state)||"erroring"===t?null:"closed"===t?0:ru(r._writableStreamController)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ready",{get:function(){return e7(this)?this._readyPromise:f(r_("ready"))},enumerable:!1,configurable:!0}),e.prototype.abort=function(e){var r,t;if(void 0===e&&(e=void 0),!e7(this))return f(r_("abort"));if(void 0===this._ownerWritableStream)return f(rh("abort"));return r=this,t=e,e$(r._ownerWritableStream,t)},e.prototype.close=function(){if(!e7(this))return f(r_("close"));var e=this._ownerWritableStream;return void 0===e?f(rh("close")):e5(e)?f(TypeError("Cannot close an already-closing stream")):e9(this)},e.prototype.releaseLock=function(){if(!e7(this))throw r_("releaseLock");void 0!==this._ownerWritableStream&&rr(this)},e.prototype.write=function(e){return(void 0===e&&(e=void 0),e7(this))?void 0===this._ownerWritableStream?f(rh("write to")):rt(this,e):f(r_("write"))},e}();function e7(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof e4}function e9(e){return e0(e._ownerWritableStream)}function re(e,r){"pending"===e._readyPromiseState?rw(e,r):rS(e,r)}function rr(e){var r=e._ownerWritableStream,t=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");re(e,t),"pending"===e._closedPromiseState?ry(e,t):function(e,r){rv(e),ry(e,r)}(e,t),r._writer=void 0,e._ownerWritableStream=void 0}function rt(e,r){var t=e._ownerWritableStream,o=t._writableStreamController,n=function(e,r){try{return e._strategySizeAlgorithm(r)}catch(r){return rc(e,r),1}}(o,r);if(t!==e._ownerWritableStream)return f(rh("write to"));var i=t._state;if("errored"===i)return f(t._storedError);if(e5(t)||"closed"===i)return f(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return f(t._storedError);var a=b(function(e,r){t._writeRequests.push({_resolve:e,_reject:r})});return function(e,r,t){try{eu(e,r,t)}catch(r){rc(e,r);return}var o=e._controlledWritableStream;e5(o)||"writable"!==o._state||e2(o,function(e){return 0>=ru(e)}(e)),rs(e)}(o,r,n),a}Object.defineProperties(e4.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(e4.prototype,i.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var ro={},rn=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"abortReason",{get:function(){if(!ri(this))throw rp("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"signal",{get:function(){if(!ri(this))throw rp("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),e.prototype.error=function(e){if(void 0===e&&(e=void 0),!ri(this))throw rp("error");"writable"===this._controlledWritableStream._state&&rf(this,e)},e.prototype[O]=function(e){var r=this._abortAlgorithm(e);return rl(this),r},e.prototype[j]=function(){es(this)},e}();function ri(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof rn}function ra(e,r,t,o,n,i,a,l){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,es(r),r._abortReason=void 0,r._abortController=function(){if(eG)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=l,r._strategyHWM=a,r._writeAlgorithm=o,r._closeAlgorithm=n,r._abortAlgorithm=i,e2(e,0>=ru(r)),_(d(t()),function(){r._started=!0,rs(r)},function(t){r._started=!0,e1(e,t)})}function rl(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function ru(e){return e._strategyHWM-e._queueTotalSize}function rs(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest){if("erroring"===r._state)return void e3(r);if(0!==e._queue.length){var t=e._queue.peek().value;t===ro?function(e){var r=e._controlledWritableStream;r._inFlightCloseRequest=r._closeRequest,r._closeRequest=void 0,el(e);var t=e._closeAlgorithm();rl(e),_(t,function(){var e;r._inFlightCloseRequest._resolve(void 0),r._inFlightCloseRequest=void 0,"erroring"===r._state&&(r._storedError=void 0,void 0!==r._pendingAbortRequest&&(r._pendingAbortRequest._resolve(),r._pendingAbortRequest=void 0)),r._state="closed",void 0!==(e=r._writer)&&rm(e)},function(e){r._inFlightCloseRequest._reject(e),r._inFlightCloseRequest=void 0,void 0!==r._pendingAbortRequest&&(r._pendingAbortRequest._reject(e),r._pendingAbortRequest=void 0),e1(r,e)})}(e):function(e,r){var t=e._controlledWritableStream;t._inFlightWriteRequest=t._writeRequests.shift(),_(e._writeAlgorithm(r),function(){t._inFlightWriteRequest._resolve(void 0),t._inFlightWriteRequest=void 0;var r=t._state;el(e),e5(t)||"writable"!==r||e2(t,function(e){return 0>=ru(e)}(e)),rs(e)},function(r){"writable"===t._state&&rl(e),t._inFlightWriteRequest._reject(r),t._inFlightWriteRequest=void 0,e1(t,r)})}(e,t)}}}function rc(e,r){"writable"===e._controlledWritableStream._state&&rf(e,r)}function rd(e){return 0>=ru(e)}function rf(e,r){var t=e._controlledWritableStream;rl(e),e8(t,r)}function rb(e){return TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function rp(e){return TypeError("WritableStreamDefaultController.prototype."+e+" can only be used on a WritableStreamDefaultController")}function r_(e){return TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function rh(e){return TypeError("Cannot "+e+" a stream using a released writer")}function rv(e){e._closedPromise=b(function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"})}Object.defineProperties(rn.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(rn.prototype,i.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function ry(e,r){void 0!==e._closedPromise_reject&&(v(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function rm(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function rg(e){e._readyPromise=b(function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t}),e._readyPromiseState="pending"}function rS(e,r){rg(e),rw(e,r)}function rw(e,r){void 0!==e._readyPromise_reject&&(v(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function rR(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}var rT="undefined"!=typeof DOMException?DOMException:void 0,rP=!function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(rT)?function(){var e=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}():rT;function rq(e,r,t,o,n,i){var l=Q(e),u=new e4(r);e._disturbed=!0;var s=!1,c=d(void 0);return b(function(y,m){if(void 0!==i){if(g=function(){var t=new rP("Aborted","AbortError"),i=[];o||i.push(function(){return"writable"===r._state?e$(r,t):d(void 0)}),n||i.push(function(){return"readable"===e._state?rV(e,t):d(void 0)}),E(function(){return Promise.all(i.map(function(e){return e()}))},!0,t)},i.aborted)return void g();i.addEventListener("abort",g)}if(C(e,l._closedPromise,function(e){o?O(!0,e):E(function(){return e$(r,e)},!0,e)}),C(r,u._closedPromise,function(r){n?O(!0,r):E(function(){return rV(e,r)},!0,r)}),S=e,w=l._closedPromise,R=function(){t?O():E(function(){var e=u._ownerWritableStream,r=e._state;return e5(e)||"closed"===r?d(void 0):"errored"===r?f(e._storedError):e9(u)})},"closed"===S._state?R():_(w,R),e5(r)||"closed"===r._state){var g,S,w,R,P=TypeError("the destination writable stream closed before all data could be piped to it");n?O(!0,P):E(function(){return rV(e,P)},!0,P)}function q(){var e=c;return p(c,function(){return e!==c?q():void 0})}function C(e,r,t){"errored"===e._state?t(e._storedError):h(r,t)}function E(e,t,o){if(!s)if(s=!0,"writable"!==r._state||e5(r))n();else _(q(),n);function n(){_(e(),function(){return j(t,o)},function(e){return j(!0,e)})}}function O(e,t){if(!s)if(s=!0,"writable"!==r._state||e5(r))j(e,t);else _(q(),function(){return j(e,t)})}function j(e,r){rr(u),T(l),void 0!==i&&i.removeEventListener("abort",g),e?m(r):y(void 0)}v(b(function(e,r){!function t(o){o?e():p(s?d(!0):p(u._readyPromise,function(){return b(function(e,r){Z(l,{_chunkSteps:function(r){c=p(rt(u,r),void 0,a),e(!1)},_closeSteps:function(){return e(!0)},_errorSteps:r})})}),t,r)}(!1)}))})}var rC=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!rE(this))throw rL("desiredSize");return rI(this)},enumerable:!1,configurable:!0}),e.prototype.close=function(){if(!rE(this))throw rL("close");if(!rB(this))throw TypeError("The stream is not in a state that permits close");rW(this)},e.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!rE(this))throw rL("enqueue");if(!rB(this))throw TypeError("The stream is not in a state that permits enqueue");return rA(this,e)},e.prototype.error=function(e){if(void 0===e&&(e=void 0),!rE(this))throw rL("error");rz(this,e)},e.prototype[k]=function(e){es(this);var r=this._cancelAlgorithm(e);return rk(this),r},e.prototype[W]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=el(this);this._closeRequested&&0===this._queue.length?(rk(this),rU(r)):rO(this),e._chunkSteps(t)}else Y(r,e),rO(this)},e}();function rE(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof rC}function rO(e){if(rj(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,_(e._pullAlgorithm(),function(){e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,rO(e))},function(r){rz(e,r)})}}function rj(e){var r=e._controlledReadableStream;return!!rB(e)&&!!e._started&&!!(rH(r)&&V(r)>0||rI(e)>0)}function rk(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function rW(e){if(rB(e)){var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(rk(e),rU(r))}}function rA(e,r){if(rB(e)){var t=e._controlledReadableStream;if(rH(t)&&V(t)>0)H(t,r,!1);else{var o=void 0;try{o=e._strategySizeAlgorithm(r)}catch(r){throw rz(e,r),r}try{eu(e,r,o)}catch(r){throw rz(e,r),r}}rO(e)}}function rz(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(es(e),rk(e),rG(t,r))}function rI(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function rB(e){var r=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===r}function rF(e,r,t,o,n,i,a){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,es(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=a,r._strategyHWM=i,r._pullAlgorithm=o,r._cancelAlgorithm=n,e._readableStreamController=r,_(d(t()),function(){r._started=!0,rO(r)},function(e){rz(r,e)})}function rL(e){return TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function rM(e,r){I(e,r);var t=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(r+" is not an AbortSignal.")}(i,r+" has member 'signal' that"),{preventAbort:!!t,preventCancel:!!o,preventClose:!!n,signal:i}}Object.defineProperties(rC.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(rC.prototype,i.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var rD=function(){function e(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:F(e,"First parameter");var t=eV(r,"Second parameter"),o=(b="First parameter",I(f=e,b),p=null==f?void 0:f.autoAllocateChunkSize,_=null==f?void 0:f.cancel,h=null==f?void 0:f.pull,v=null==f?void 0:f.start,y=null==f?void 0:f.type,{autoAllocateChunkSize:void 0===p?void 0:N(p,b+" has member 'autoAllocateChunkSize' that"),cancel:void 0===_?void 0:(i=_,a=f,B(i,b+" has member 'cancel' that"),function(e){return g(i,a,[e])}),pull:void 0===h?void 0:(l=h,u=f,B(l,b+" has member 'pull' that"),function(e){return g(l,u,[e])}),start:void 0===v?void 0:(s=v,c=f,B(s,b+" has member 'start' that"),function(e){return m(s,c,[e])}),type:void 0===y?void 0:function(e,r){if("bytes"!=(e=""+e))throw TypeError(r+" '"+e+"' is not a valid enumeration value for ReadableStreamType");return e}(y,b+" has member 'type' that")});if(rQ(this),"bytes"===o.type){if(void 0!==t.size)throw RangeError("The strategy for a byte stream cannot have a size function");var n=eY(t,0);!function(e,r,t){var o=Object.create(ed.prototype),n=function(){},i=function(){return d(void 0)},a=function(){return d(void 0)};void 0!==r.start&&(n=function(){return r.start(o)}),void 0!==r.pull&&(i=function(){return r.pull(o)}),void 0!==r.cancel&&(a=function(e){return r.cancel(e)});var l=r.autoAllocateChunkSize;if(0===l)throw TypeError("autoAllocateChunkSize must be greater than 0");ez(e,o,n,i,a,t,l)}(this,o,n)}else{var i,a,l,u,s,c,f,b,p,_,h,v,y,S,w,R,T,P,q=eH(t),n=eY(t,1);S=n,w=Object.create(rC.prototype),R=function(){},T=function(){return d(void 0)},P=function(){return d(void 0)},void 0!==o.start&&(R=function(){return o.start(w)}),void 0!==o.pull&&(T=function(){return o.pull(w)}),void 0!==o.cancel&&(P=function(e){return o.cancel(e)}),rF(this,w,R,T,P,S,q)}}return Object.defineProperty(e.prototype,"locked",{get:function(){if(!rY(this))throw rX("locked");return rH(this)},enumerable:!1,configurable:!0}),e.prototype.cancel=function(e){return(void 0===e&&(e=void 0),rY(this))?rH(this)?f(TypeError("Cannot cancel a stream that already has a reader")):rV(this,e):f(rX("cancel"))},e.prototype.getReader=function(e){var r,t,o;if(void 0===e&&(e=void 0),!rY(this))throw rX("getReader");return void 0===(t="First parameter",I(r=e,t),{mode:void 0===(o=null==r?void 0:r.mode)?void 0:function(e,r){if("byob"!=(e=""+e))throw TypeError(r+" '"+e+"' is not a valid enumeration value for ReadableStreamReaderMode");return e}(o,t+" has member 'mode' that")}).mode?Q(this):new eD(this)},e.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!rY(this))throw rX("pipeThrough");L(e,1,"pipeThrough");var t,o,n,i=(I(e,t="First parameter"),M(o=null==e?void 0:e.readable,"readable","ReadableWritablePair"),x(o,t+" has member 'readable' that"),M(n=null==e?void 0:e.writable,"writable","ReadableWritablePair"),eU(n,t+" has member 'writable' that"),{readable:o,writable:n}),a=rM(r,"Second parameter");if(rH(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(eK(i.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return v(rq(this,i.writable,a.preventClose,a.preventAbort,a.preventCancel,a.signal)),i.readable},e.prototype.pipeTo=function(e,r){var t;if(void 0===r&&(r={}),!rY(this))return f(rX("pipeTo"));if(void 0===e)return f("Parameter 1 is required in 'pipeTo'.");if(!eJ(e))return f(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{t=rM(r,"Second parameter")}catch(e){return f(e)}return rH(this)?f(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):eK(e)?f(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):rq(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},e.prototype.tee=function(){if(!rY(this))throw rX("tee");var e=ef(this._readableStreamController)?function(e){var r,t,o,n,i,a=Q(e),l=!1,u=!1,s=!1,c=!1,f=!1,p=b(function(e){i=e});function _(e){h(e._closedPromise,function(r){e===a&&(eO(o._readableStreamController,r),eO(n._readableStreamController,r),c&&f||i(void 0))})}function v(){eN(a)&&(T(a),_(a=Q(e))),Z(a,{_chunkSteps:function(r){y(function(){u=!1,s=!1;var t=r;if(!c&&!f)try{t=ea(r)}catch(r){eO(o._readableStreamController,r),eO(n._readableStreamController,r),i(rV(e,r));return}c||eE(o._readableStreamController,r),f||eE(n._readableStreamController,t),l=!1,u?g():s&&S()})},_closeSteps:function(){l=!1,c||eC(o._readableStreamController),f||eC(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&eW(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&eW(n._readableStreamController,0),c&&f||i(void 0)},_errorSteps:function(){l=!1}})}function m(r,t){X(a)&&(T(a),_(a=new eD(e)));var d=t?n:o,b=t?o:n;ex(a,r,{_chunkSteps:function(r){y(function(){u=!1,s=!1;var o=t?f:c;if(t?c:f)o||eA(d._readableStreamController,r);else{var n=void 0;try{n=ea(r)}catch(r){eO(d._readableStreamController,r),eO(b._readableStreamController,r),i(rV(e,r));return}o||eA(d._readableStreamController,r),eE(b._readableStreamController,n)}l=!1,u?g():s&&S()})},_closeSteps:function(e){l=!1;var r=t?f:c,o=t?c:f;r||eC(d._readableStreamController),o||eC(b._readableStreamController),void 0!==e&&(r||eA(d._readableStreamController,e),!o&&b._readableStreamController._pendingPullIntos.length>0&&eW(b._readableStreamController,0)),r&&o||i(void 0)},_errorSteps:function(){l=!1}})}function g(){if(l)return u=!0,d(void 0);l=!0;var e=ej(o._readableStreamController);return null===e?v():m(e._view,!1),d(void 0)}function S(){if(l)return s=!0,d(void 0);l=!0;var e=ej(n._readableStreamController);return null===e?v():m(e._view,!0),d(void 0)}function w(){}return o=rx(w,g,function(o){if(c=!0,r=o,f){var n=rV(e,eo([r,t]));i(n)}return p}),n=rx(w,S,function(o){if(f=!0,t=o,c){var n=rV(e,eo([r,t]));i(n)}return p}),_(a),[o,n]}(this):function(e,r){var t,o,n,i,a,l=Q(e),u=!1,s=!1,c=!1,f=!1,p=b(function(e){a=e});function _(){return u?(s=!0,d(void 0)):(u=!0,Z(l,{_chunkSteps:function(e){y(function(){s=!1,c||rA(n._readableStreamController,e),f||rA(i._readableStreamController,e),u=!1,s&&_()})},_closeSteps:function(){u=!1,c||rW(n._readableStreamController),f||rW(i._readableStreamController),c&&f||a(void 0)},_errorSteps:function(){u=!1}}),d(void 0))}function v(){}return n=rN(v,_,function(r){if(c=!0,t=r,f){var n=rV(e,eo([t,o]));a(n)}return p}),i=rN(v,_,function(r){if(f=!0,o=r,c){var n=rV(e,eo([t,o]));a(n)}return p}),h(l._closedPromise,function(e){rz(n._readableStreamController,e),rz(i._readableStreamController,e),c&&f||a(void 0)}),[n,i]}(this);return eo(e)},e.prototype.values=function(e){var r,t,o,n,i;if(void 0===e&&(e=void 0),!rY(this))throw rX("values");return o=(t="First parameter",I(r=e,t),{preventCancel:!!(null==r?void 0:r.preventCancel)}).preventCancel,n=new K(Q(this),o),(i=Object.create($))._asyncIteratorImpl=n,i},e}();function rN(e,r,t,o,n){void 0===o&&(o=1),void 0===n&&(n=function(){return 1});var i=Object.create(rD.prototype);return rQ(i),rF(i,Object.create(rC.prototype),e,r,t,o,n),i}function rx(e,r,t){var o=Object.create(rD.prototype);return rQ(o),ez(o,Object.create(ed.prototype),e,r,t,0,void 0),o}function rQ(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function rY(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof rD}function rH(e){return void 0!==e._reader}function rV(e,r){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return f(e._storedError);rU(e);var t=e._reader;return void 0!==t&&eN(t)&&(t._readIntoRequests.forEach(function(e){e._closeSteps(void 0)}),t._readIntoRequests=new S),p(e._readableStreamController[k](r),a,void 0)}function rU(e){e._state="closed";var r=e._reader;void 0!==r&&(E(r),X(r)&&(r._readRequests.forEach(function(e){e._closeSteps()}),r._readRequests=new S))}function rG(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(C(t,r),X(t)?(t._readRequests.forEach(function(e){e._errorSteps(r)}),t._readRequests=new S):(t._readIntoRequests.forEach(function(e){e._errorSteps(r)}),t._readIntoRequests=new S))}function rX(e){return TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function rZ(e,r){I(e,r);var t=null==e?void 0:e.highWaterMark;return M(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:D(t)}}Object.defineProperties(rD.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(rD.prototype,i.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof i.asyncIterator&&Object.defineProperty(rD.prototype,i.asyncIterator,{value:rD.prototype.values,writable:!0,configurable:!0});var rJ=function(e){return e.byteLength};try{Object.defineProperty(rJ,"name",{value:"size",configurable:!0})}catch(e){}var rK=function(){function e(e){L(e,1,"ByteLengthQueuingStrategy"),e=rZ(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(e.prototype,"highWaterMark",{get:function(){if(!r0(this))throw r$("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){if(!r0(this))throw r$("size");return rJ},enumerable:!1,configurable:!0}),e}();function r$(e){return TypeError("ByteLengthQueuingStrategy.prototype."+e+" can only be used on a ByteLengthQueuingStrategy")}function r0(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof rK}Object.defineProperties(rK.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(rK.prototype,i.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var r1=function(){return 1};try{Object.defineProperty(r1,"name",{value:"size",configurable:!0})}catch(e){}var r8=function(){function e(e){L(e,1,"CountQueuingStrategy"),e=rZ(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(e.prototype,"highWaterMark",{get:function(){if(!r5(this))throw r3("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){if(!r5(this))throw r3("size");return r1},enumerable:!1,configurable:!0}),e}();function r3(e){return TypeError("CountQueuingStrategy.prototype."+e+" can only be used on a CountQueuingStrategy")}function r5(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof r8}Object.defineProperties(r8.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(r8.prototype,i.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var r6=function(){function e(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var o,n,i,a,l,u,s,c,_,h,v,y,S,w,R=eV(r,"Second parameter"),T=eV(t,"Third parameter"),P=(c="First parameter",I(s=e,c),_=null==s?void 0:s.flush,h=null==s?void 0:s.readableType,v=null==s?void 0:s.start,y=null==s?void 0:s.transform,S=null==s?void 0:s.writableType,{flush:void 0===_?void 0:(o=_,n=s,B(o,c+" has member 'flush' that"),function(e){return g(o,n,[e])}),readableType:h,start:void 0===v?void 0:(i=v,a=s,B(i,c+" has member 'start' that"),function(e){return m(i,a,[e])}),transform:void 0===y?void 0:(l=y,u=s,B(l,c+" has member 'transform' that"),function(e,r){return g(l,u,[e,r])}),writableType:S});if(void 0!==P.readableType)throw RangeError("Invalid readableType specified");if(void 0!==P.writableType)throw RangeError("Invalid writableType specified");var q=eY(T,0),C=eH(T),E=eY(R,1),O=eH(R);(function(e,r,t,o,n,i){var a,l,u;function s(){return r}void 0===(a=t)&&(a=1),void 0===(l=o)&&(l=function(){return 1}),eZ(u=Object.create(eX.prototype)),ra(u,Object.create(rn.prototype),s,function(r){var t,o,n;return t=e,o=r,n=t._transformStreamController,t._backpressure?p(t._backpressureChangePromise,function(){var e=t._writable;if("erroring"===e._state)throw e._storedError;return tn(n,o)},void 0):tn(n,o)},function(){var r,t,o,n;return t=(r=e)._readable,n=(o=r._transformStreamController)._flushAlgorithm(),tt(o),p(n,function(){if("errored"===t._state)throw t._storedError;rW(t._readableStreamController)},function(e){throw r4(r,e),t._storedError})},function(r){return r4(e,r),d(void 0)},a,l),e._writable=u,e._readable=rN(s,function(){var r;return r9(r=e,!1),r._backpressureChangePromise},function(r){return r7(e,r),d(void 0)},n,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,r9(e,!0),e._transformStreamController=void 0})(this,b(function(e){w=e}),E,O,q,C),function(e,r){var t,o,n=Object.create(te.prototype),i=function(e){try{var r;return to(n,e),r=void 0,d(r)}catch(e){return f(e)}},a=function(){return d(void 0)};void 0!==r.transform&&(i=function(e){return r.transform(e,n)}),void 0!==r.flush&&(a=function(){return r.flush(n)}),t=i,o=a,n._controlledTransformStream=e,e._transformStreamController=n,n._transformAlgorithm=t,n._flushAlgorithm=o}(this,P),void 0!==P.start?w(P.start(this._transformStreamController)):w(void 0)}return Object.defineProperty(e.prototype,"readable",{get:function(){if(!r2(this))throw ta("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"writable",{get:function(){if(!r2(this))throw ta("writable");return this._writable},enumerable:!1,configurable:!0}),e}();function r2(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof r6}function r4(e,r){rz(e._readable._readableStreamController,r),r7(e,r)}function r7(e,r){tt(e._transformStreamController),rc(e._writable._writableStreamController,r),e._backpressure&&r9(e,!1)}function r9(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=b(function(r){e._backpressureChangePromise_resolve=r}),e._backpressure=r}Object.defineProperties(r6.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(r6.prototype,i.toStringTag,{value:"TransformStream",configurable:!0});var te=function(){function e(){throw TypeError("Illegal constructor")}return Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!tr(this))throw ti("desiredSize");return rI(this._controlledTransformStream._readable._readableStreamController)},enumerable:!1,configurable:!0}),e.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!tr(this))throw ti("enqueue");to(this,e)},e.prototype.error=function(e){var r,t;if(void 0===e&&(e=void 0),!tr(this))throw ti("error");r=this,t=e,r4(r._controlledTransformStream,t)},e.prototype.terminate=function(){var e,r;if(!tr(this))throw ti("terminate");e=this,rW((r=e._controlledTransformStream)._readable._readableStreamController),r7(r,TypeError("TransformStream terminated"))},e}();function tr(e){return!!u(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof te}function tt(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function to(e,r){var t=e._controlledTransformStream,o=t._readable._readableStreamController;if(!rB(o))throw TypeError("Readable side is not in a state that permits enqueue");try{rA(o,r)}catch(e){throw r7(t,e),t._readable._storedError}!rj(o)!==t._backpressure&&r9(t,!0)}function tn(e,r){return p(e._transformAlgorithm(r),void 0,function(r){throw r4(e._controlledTransformStream,r),r})}function ti(e){return TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function ta(e){return TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}Object.defineProperties(te.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof i.toStringTag&&Object.defineProperty(te.prototype,i.toStringTag,{value:"TransformStreamDefaultController",configurable:!0})}}]);
//# sourceMappingURL=17c69435-1a0cb37acf25adb0.js.map