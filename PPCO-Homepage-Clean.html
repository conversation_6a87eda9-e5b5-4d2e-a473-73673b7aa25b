<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPCO Contracting - Always One Step Ahead</title>
    <meta name="description" content="Premier contracting in Saudi Arabia since 2006. Building the future with integrity and partnership.">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Space+Mono:wght@400;700&family=Syne:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* CSS Variables */
        :root {
            --primary-color: #273A73;
            --text-color: #333F70;
            --text-muted: #333f7080;
            --background-color: #ffffff;
            --accent-color: #344d99;
            --border-radius: 8px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --heading-font: 'Space Mono', monospace;
            --body-font: 'Syne', sans-serif;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--background-color);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            z-index: 1000;
            border-bottom: 1px solid rgba(39, 58, 115, 0.1);
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            height: 40px;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            transition: background-color 0.3s ease;
        }

        .nav-link:hover {
            background-color: rgba(39, 58, 115, 0.1);
        }

        /* Main Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Hero Section */
        .hero {
            margin-top: 80px;
            padding: 6rem 0;
            background: linear-gradient(135deg, rgba(39, 58, 115, 0.05) 0%, rgba(52, 77, 153, 0.05) 100%);
            text-align: center;
        }

        .hero h1 {
            font-family: var(--heading-font);
            font-size: 4rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
        }

        .hero .tagline {
            font-size: 1.5rem;
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.2rem;
            color: var(--text-muted);
            max-width: 600px;
            margin: 0 auto 3rem;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid var(--primary-color);
            font-size: 1.1rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--accent-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-secondary {
            background-color: transparent;
            color: var(--primary-color);
        }

        .btn-secondary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* Sections */
        .section {
            padding: 5rem 0;
        }

        .section h2 {
            font-family: var(--heading-font);
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            text-align: center;
        }

        .section-description {
            text-align: center;
            font-size: 1.2rem;
            color: var(--text-muted);
            max-width: 800px;
            margin: 0 auto 4rem;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 4rem;
        }

        .feature-card {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .feature-title {
            font-family: var(--heading-font);
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .feature-description {
            color: var(--text-muted);
        }

        /* Stats Section */
        .stats {
            background: var(--primary-color);
            color: white;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
        }

        .stat-item h3 {
            font-family: var(--heading-font);
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .stat-item p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* About Section */
        .about {
            background: rgba(39, 58, 115, 0.02);
        }

        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .about-text h3 {
            font-family: var(--heading-font);
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .about-text p {
            font-size: 1.1rem;
            color: var(--text-muted);
            margin-bottom: 1.5rem;
        }

        .about-image {
            width: 100%;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero .tagline {
                font-size: 1.2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .section h2 {
                font-size: 2rem;
            }

            .button-group {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .about-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Utility Classes */
        .text-center {
            text-align: center;
        }

        .mb-2 {
            margin-bottom: 2rem;
        }

        .mt-4 {
            margin-top: 4rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <img src="./PPCO Project Gallery_files/cropped-Untitled-design-1-1.png" alt="PPCO Logo" class="logo">
            <div class="nav-links">
                <a href="#about" class="nav-link">About</a>
                <a href="#services" class="nav-link">Services</a>
                <a href="PPCO-Project-Gallery-Clean.html" class="nav-link">Projects</a>
                <a href="#contact" class="nav-link">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>ALWAYS ONE STEP AHEAD</h1>
            <p class="tagline">Premier Contracting Excellence</p>
            <p>Building the future with integrity and partnership. Premier contracting in Saudi Arabia since 2006, delivering exceptional construction and engineering solutions across diverse sectors.</p>
            <div class="button-group">
                <a href="#contact" class="btn btn-primary">Contact Us</a>
                <a href="#" class="btn btn-secondary">Download Profile</a>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main>
        <!-- Services Section -->
        <section id="services" class="section">
            <div class="container">
                <h2>Our Services</h2>
                <p class="section-description">Comprehensive construction and engineering solutions tailored to meet the unique demands of each project.</p>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🏗️</div>
                        <h3 class="feature-title">Construction Management</h3>
                        <p class="feature-description">End-to-end project management ensuring timely delivery and quality execution of construction projects.</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">⚙️</div>
                        <h3 class="feature-title">Engineering Solutions</h3>
                        <p class="feature-description">Innovative engineering design and implementation for complex infrastructure and industrial projects.</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <h3 class="feature-title">Maintenance Services</h3>
                        <p class="feature-description">Comprehensive maintenance and facility management services to ensure optimal operational performance.</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🌱</div>
                        <h3 class="feature-title">Sustainable Development</h3>
                        <p class="feature-description">Eco-friendly construction practices and sustainable development solutions for future-ready projects.</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🏭</div>
                        <h3 class="feature-title">Industrial Projects</h3>
                        <p class="feature-description">Specialized construction services for oil & gas, petrochemical, and industrial facility development.</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🏢</div>
                        <h3 class="feature-title">Commercial Development</h3>
                        <p class="feature-description">Modern commercial and residential development projects with cutting-edge design and functionality.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="section stats">
            <div class="container">
                <h2 style="color: white;">Our Track Record</h2>
                <p class="section-description" style="color: rgba(255, 255, 255, 0.9);">Numbers that speak to our commitment and excellence in the construction industry.</p>

                <div class="stats-grid">
                    <div class="stat-item">
                        <h3>18+</h3>
                        <p>Years of Excellence</p>
                    </div>
                    <div class="stat-item">
                        <h3>500+</h3>
                        <p>Projects Completed</p>
                    </div>
                    <div class="stat-item">
                        <h3>50+</h3>
                        <p>Expert Team Members</p>
                    </div>
                    <div class="stat-item">
                        <h3>100%</h3>
                        <p>Client Satisfaction</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="section about">
            <div class="container">
                <h2>About PPCO</h2>
                <div class="about-content">
                    <div class="about-text">
                        <h3>Building Excellence Since 2006</h3>
                        <p>PPCO Contracting has been at the forefront of Saudi Arabia's construction industry for over 18 years. We specialize in delivering high-quality construction and engineering solutions across diverse sectors including NEOM, SPARK, and Aramco facilities.</p>

                        <p>Our commitment to excellence, innovation, and sustainability has made us a trusted partner for major development projects throughout the Kingdom. We combine traditional craftsmanship with modern technology to deliver projects that exceed expectations.</p>

                        <p>With a team of experienced professionals and a proven track record of successful project delivery, PPCO continues to be always one step ahead in the construction industry.</p>

                        <div class="button-group" style="justify-content: flex-start; margin-top: 2rem;">
                            <a href="PPCO-Project-Gallery-Clean.html" class="btn btn-primary">View Our Projects</a>
                        </div>
                    </div>
                    <div>
                        <img src="./PPCO Project Gallery_files/cropped-Untitled-design-1-1.png" alt="PPCO Company" class="about-image">
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="section">
            <div class="container">
                <h2>Get In Touch</h2>
                <p class="section-description">Ready to start your next project? Contact us today to discuss how PPCO can bring your vision to life.</p>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📧</div>
                        <h3 class="feature-title">Email Us</h3>
                        <p class="feature-description">Send us your project details and requirements for a detailed consultation.</p>
                        <a href="mailto:<EMAIL>" class="btn btn-primary" style="margin-top: 1rem;">Send Email</a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📞</div>
                        <h3 class="feature-title">Call Us</h3>
                        <p class="feature-description">Speak directly with our project consultants for immediate assistance.</p>
                        <a href="tel:+966123456789" class="btn btn-primary" style="margin-top: 1rem;">Call Now</a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📍</div>
                        <h3 class="feature-title">Visit Us</h3>
                        <p class="feature-description">Meet our team at our headquarters in Saudi Arabia for in-person consultations.</p>
                        <a href="#" class="btn btn-primary" style="margin-top: 1rem;">Get Directions</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer style="background-color: var(--primary-color); color: white; text-align: center; padding: 3rem 0;">
        <div class="container">
            <img src="./PPCO Project Gallery_files/cropped-Untitled-design-1-1.png" alt="PPCO Logo" style="height: 50px; margin-bottom: 1rem; filter: brightness(0) invert(1);">
            <h3 style="font-family: var(--heading-font); margin-bottom: 1rem;">ALWAYS ONE STEP AHEAD</h3>
            <p style="margin-bottom: 2rem; opacity: 0.9;">Premier contracting in Saudi Arabia since 2006</p>

            <div style="display: flex; justify-content: center; gap: 2rem; margin-bottom: 2rem; flex-wrap: wrap;">
                <a href="#about" style="color: white; text-decoration: none; opacity: 0.8; transition: opacity 0.3s ease;">About</a>
                <a href="#services" style="color: white; text-decoration: none; opacity: 0.8; transition: opacity 0.3s ease;">Services</a>
                <a href="PPCO-Project-Gallery-Clean.html" style="color: white; text-decoration: none; opacity: 0.8; transition: opacity 0.3s ease;">Projects</a>
                <a href="#contact" style="color: white; text-decoration: none; opacity: 0.8; transition: opacity 0.3s ease;">Contact</a>
            </div>

            <div style="border-top: 1px solid rgba(255, 255, 255, 0.2); padding-top: 2rem;">
                <p>&copy; 2024 PPCO Contracting. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Enhanced Functionality -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to navbar
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections for animation
        document.querySelectorAll('.section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });

        // Counter animation for stats
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-item h3');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent);
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target + (counter.textContent.includes('+') ? '+' : '') + (counter.textContent.includes('%') ? '%' : '');
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current) + (counter.textContent.includes('+') ? '+' : '') + (counter.textContent.includes('%') ? '%' : '');
                    }
                }, 20);
            });
        }

        // Trigger counter animation when stats section is visible
        const statsSection = document.querySelector('.stats');
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        if (statsSection) {
            statsObserver.observe(statsSection);
        }
    </script>
</body>
</html>
