"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4624],{93766:(e,t,i)=>{i.d(t,{Hu:()=>Y,JM:()=>K,Od:()=>R,Xi:()=>X,Z1:()=>ss,c:()=>N,gA:()=>ek,gs:()=>H,ju:()=>ti,mf:()=>se,qx:()=>F,zO:()=>G});var s=i(58423),n=i(55729),a=i(7913),o=i(43315),r=i(60318),l=i(36215),d=i(43385),h=i(48331),p=i(41136),c=i(10590),u=i(31831),g=i(63693),m=i(11906),f=i(17997),v=Object.defineProperty,b=Object.defineProperties,y=Object.getOwnPropertyDescriptor,S=Object.getOwnPropertyDescriptors,I=Object.getOwnPropertyNames,k=Object.getOwnPropertySymbols,w=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable,P=(e,t,i)=>t in e?v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,C=(e,t)=>{for(var i in t||(t={}))w.call(t,i)&&P(e,i,t[i]);if(k)for(var i of k(t))x.call(t,i)&&P(e,i,t[i]);return e},A=(e,t)=>b(e,S(t)),E=(e,t)=>{for(var i in t)v(e,i,{get:t[i],enumerable:!0})},B=(e,t,i)=>(P(e,"symbol"!=typeof t?t+"":t,i),i),z=(e,t,i)=>new Promise((s,n)=>{var a=e=>{try{r(i.next(e))}catch(e){n(e)}},o=e=>{try{r(i.throw(e))}catch(e){n(e)}},r=e=>e.done?s(e.value):Promise.resolve(e.value).then(a,o);r((i=i.apply(e,t)).next())}),M=class{constructor(){B(this,"onPinchStart"),B(this,"onPinchEnd"),B(this,"onPinch"),B(this,"onKeyDown"),B(this,"onKeyUp"),B(this,"onPointerMove"),B(this,"onPointerUp"),B(this,"onPan"),B(this,"onZoom"),B(this,"onPointerDown"),B(this,"onPointCanvas"),B(this,"onDoubleClickCanvas"),B(this,"onRightPointCanvas"),B(this,"onDragCanvas"),B(this,"onReleaseCanvas"),B(this,"onPointShape"),B(this,"onDoubleClickShape"),B(this,"onRightPointShape"),B(this,"onDragShape"),B(this,"onHoverShape"),B(this,"onUnhoverShape"),B(this,"onReleaseShape"),B(this,"onPointBounds"),B(this,"onDoubleClickBounds"),B(this,"onRightPointBounds"),B(this,"onDragBounds"),B(this,"onHoverBounds"),B(this,"onUnhoverBounds"),B(this,"onReleaseBounds"),B(this,"onPointBoundsHandle"),B(this,"onDoubleClickBoundsHandle"),B(this,"onRightPointBoundsHandle"),B(this,"onDragBoundsHandle"),B(this,"onHoverBoundsHandle"),B(this,"onUnhoverBoundsHandle"),B(this,"onReleaseBoundsHandle"),B(this,"onPointHandle"),B(this,"onDoubleClickHandle"),B(this,"onRightPointHandle"),B(this,"onDragHandle"),B(this,"onHoverHandle"),B(this,"onUnhoverHandle"),B(this,"onReleaseHandle"),B(this,"onShapeBlur"),B(this,"onShapeClone")}},D=(e=>(e.Idle="idle",e.Connecting="connecting",e.Connected="connected",e.Disconnected="disconnected",e))(D||{}),j=(e=>(e.Transform="transform",e.Translate="translate",e.TransformSingle="transformSingle",e.Brush="brush",e.Arrow="arrow",e.Draw="draw",e.Erase="erase",e.Rotate="rotate",e.Handle="handle",e.Grid="grid",e.Edit="edit",e))(j||{}),T=(e=>(e.Idle="idle",e.PointingHandle="pointingHandle",e.PointingBounds="pointingBounds",e.PointingBoundsHandle="pointingBoundsHandle",e.TranslatingLabel="translatingLabel",e.TranslatingHandle="translatingHandle",e.Translating="translating",e.Transforming="transforming",e.Rotating="rotating",e.Pinching="pinching",e.Brushing="brushing",e.Creating="creating",e.EditingText="editing-text",e))(T||{}),O=(e=>(e.Backward="backward",e.Forward="forward",e.ToFront="toFront",e.ToBack="toBack",e))(O||{}),F=(e=>(e.Top="top",e.CenterVertical="centerVertical",e.Bottom="bottom",e.Left="left",e.CenterHorizontal="centerHorizontal",e.Right="right",e))(F||{}),L=(e=>(e.Horizontal="horizontal",e.Vertical="vertical",e))(L||{}),R=(e=>(e.Horizontal="horizontal",e.Vertical="vertical",e))(R||{}),q=(e=>(e.Horizontal="horizontal",e.Vertical="vertical",e))(q||{}),H=(e=>(e.Sticky="sticky",e.Ellipse="ellipse",e.Rectangle="rectangle",e.Triangle="triangle",e.Draw="draw",e.Arrow="arrow",e.Line="line",e.Text="text",e.Group="group",e.Image="image",e.Video="video",e))(H||{}),U=(e=>(e.Arrow="arrow",e))(U||{}),K=(e=>(e.White="white",e.LightGray="lightGray",e.Gray="gray",e.Black="black",e.Green="green",e.Cyan="cyan",e.Blue="blue",e.Indigo="indigo",e.Violet="violet",e.Red="red",e.Orange="orange",e.Yellow="yellow",e))(K||{}),_=(e=>(e.Small="small",e.Medium="medium",e.Large="large",e))(_||{}),N=(e=>(e.Draw="draw",e.Solid="solid",e.Dashed="dashed",e.Dotted="dotted",e))(N||{}),W=(e=>(e.Small="small",e.Medium="medium",e.Large="large",e.ExtraLarge="extraLarge",e))(W||{}),Y=(e=>(e.Start="start",e.Middle="middle",e.End="end",e.Justify="justify",e))(Y||{}),$=(e=>(e.Script="script",e.Sans="sans",e.Serif="serif",e.Mono="mono",e))($||{}),G=(e=>(e.Image="image",e.Video="video",e))(G||{}),X=(e=>(e.PNG="png",e.JPG="jpeg",e.WEBP="webp",e.SVG="svg",e.JSON="json",e))(X||{}),V=(e=>(e.Transparent="transparent",e.Auto="auto",e.Light="light",e.Dark="dark",e))(V||{}),Z=new Map,J="tldraw-fonts",Q=`
@import url('https://fonts.googleapis.com/css2?family=Caveat+Brush&family=Source+Code+Pro&family=Source+Sans+Pro&family=Crimson+Pro&display=block');

@font-face {
  font-family: 'Recursive';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/recursive/v23/8vI-7wMr0mhh-RQChyHEH06TlXhq_gukbYrFMk1QuAIcyEwG_X-dpEfaE5YaERmK-CImKsvxvU-MXGX2fSqasNfUlTGZnI14ZeY.woff2)
    format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC,
    U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Recursive';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/recursive/v23/8vI-7wMr0mhh-RQChyHEH06TlXhq_gukbYrFMk1QuAIcyEwG_X-dpEfaE5YaERmK-CImKsvxvU-MXGX2fSqasNfUlTGZnI14ZeY.woff2)
    format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC,
    U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Recursive Mono';
  font-style: normal;
  font-weight: 420;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/recursive/v23/8vI-7wMr0mhh-RQChyHEH06TlXhq_gukbYrFMk1QuAIcyEwG_X-dpEfaE5YaERmK-CImqvTxvU-MXGX2fSqasNfUlTGZnI14ZeY.woff2)
    format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC,
    U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
`,ee=n.createContext({});function et(){return n.useContext(ee)}var ei=n.createContext({}),es={"style.menu.color":"Color","style.menu.fill":"Fill","style.menu.dash":"Dash","style.menu.size":"Size","style.menu.keep.open":"Keep Open","style.menu.font":"Font","style.menu.align":"Align",styles:"Styles","zoom.in":"Zoom In","zoom.out":"Zoom Out",to:"To","menu.tools":"Tools","menu.transform":"Transform","menu.file":"File","menu.edit":"Edit","menu.view":"View","menu.preferences":"Preferences","menu.sign.in":"Sign In","menu.sign.out":"Sign Out","become.a.sponsor":"Become a Sponsor","zoom.to.content":"Back to content","zoom.to.selection":"Zoom to Selection","zoom.to.fit":"Zoom to Fit","zoom.to":"Zoom to","preferences.dark.mode":"Dark Mode","preferences.focus.mode":"Focus Mode","preferences.debug.mode":"Debug Mode","preferences.show.grid":"Show Grid","preferences.use.cad.selection":"Use CAD Selection","preferences.keep.stylemenu.open":"Keep Style Menu Open","preferences.always.show.snaps":"Always Show Snaps","preferences.rotate.handles":"Rotate Handles","preferences.binding.handles":"Binding Handles","preferences.clone.handles":"Clone Handles",undo:"Undo",redo:"Redo",cut:"Cut",copy:"Copy",paste:"Paste","copy.as":"Copy As","export.as":"Export As","select.all":"Select All","select.none":"Select None",delete:"Delete","new.project":"New Project",open:"Open",save:"Save","save.as":"Save As","upload.media":"Upload Media","create.page":"Create Page","new.page":"New Page","page.name":"Page Name",duplicate:"Duplicate",cancel:"Cancel","copy.invite.link":"Copy Invite Link","copy.readonly.link":"Copy ReadOnly Link","create.multiplayer.project":"Create a Multiplayer Project","copy.multiplayer.project":"Copy to Multiplayer Project",select:"Select",eraser:"Eraser",draw:"Draw",arrow:"Arrow",text:"Text",sticky:"Sticky",rectangle:"Rectangle",ellipse:"Ellipse",triangle:"Triangle",line:"Line",rotate:"Rotate","lock.aspect.ratio":"Lock Aspect Ratio","unlock.aspect.ratio":"Unlock Aspect Ratio",group:"Group",ungroup:"Ungroup","move.to.back":"Move to Back","move.backward":"Move Backward","move.forward":"Move Forward","move.to.front":"Move to Front","reset.angle":"Reset Angle",lock:"Lock",unlock:"Unlock","align.distribute":"Align / Distribute","move.to.page":"Move to Page","flip.horizontal":"Flip Horizontal","flip.vertical":"Flip Vertical",move:"Move","to.front":"To Front",forward:"Forward",backward:"Backward",back:"To Back",language:"Language","translation.link":"Learn More","dock.position":"Dock Position",bottom:"Bottom",left:"Left",right:"Right",top:"Top",page:"Page","keyboard.shortcuts":"Keyboard shortcuts",search:"Search",loading:"Loading{dots}","export.background":"Export Background",transparent:"Transparent",auto:"Auto",light:"Light",dark:"Dark",github:"Github",twitter:"Twitter",discord:"Discord",image:"Image","align.left":"Align Left","align.center.x":"Align Horizontal Center","align.right":"Align Right","align.top":"Align Top","align.center.y":"Align Vertical Center","align.bottom":"Align Bottom","distribute.x":"Distribute Horizontal","distribute.y":"Distribute Vertical","stretch.x":"Stretch Horizontal","stretch.y":"Stretch Vertical","dialog.save.firsttime":"Do you want to save your current project?","dialog.save.again":"Do you want to save changes to your current project?","dialog.cancel":"Cancel","dialog.no":"No","dialog.yes":"Yes","enter.file.name":"Enter file name"},en=[{locale:"ar",label:"عربي",messages:{"style.menu.color":"اللون","style.menu.fill":"يملأ","style.menu.dash":"متقطع","style.menu.size":"حجم","style.menu.keep.open":"تبقى مفتوحة","style.menu.font":"الخط","style.menu.align":"محاذاة",styles:"الأنماط","zoom.in":"تكبير","zoom.out":"تصغير",to:"إلى","menu.tools":"أدوات","menu.transform":"التحويلات","menu.file":"ملف","menu.edit":"تحرير","menu.view":"معاينة","menu.preferences":"التفضيلات","menu.sign.in":"تسجيل الدخول","menu.sign.out":"خروج","become.a.sponsor":"كن راعياً","zoom.to.content":"العودة إلى المحتوى","zoom.to.selection":"تكبير للتحديد","zoom.to.fit":"تكبير لتناسب","zoom.to":"تكبير إلى","preferences.dark.mode":"الوضع الداكن","preferences.focus.mode":"وضع التركيز","preferences.debug.mode":"وضع التصحيح","preferences.show.grid":"اظهر الشبكة","preferences.use.cad.selection":"استخدام التحديد CAD","preferences.keep.stylemenu.open":"الاحتفاظ بقائمة النمط مفتوحة","preferences.always.show.snaps":"إظهار اللقطات دائمًا","preferences.rotate.handles":"تدوير المقابض","preferences.binding.handles":"مقابض ملزمة","preferences.clone.handles":"مقابض استنساخ",undo:"الغاء",redo:"إعادة",cut:"قطع",copy:"نسح",paste:"لصق","copy.as":"نسخ الى","export.as":"صدر الى","select.all":"اختر الكل","select.none":"لا تختر شيء",delete:"حذف","new.project":"مشروع جديد",open:"فتح",save:"حفظ","save.as":"حفظ باسم","upload.media":"تحميل الوسائط","create.page":"إنشاء صفحة","new.page":"صفحة جديدة","page.name":"اسم الصفحة",duplicate:"نسخ",cancel:"الغاء","copy.invite.link":"نسخ رابط الدعوة","copy.readonly.link":"نسخ رابط دعوة القراءة فقط","create.multiplayer.project":"قم بإنشاء مشروع متعدد اللاعبين","copy.multiplayer.project":"نسخ إلى مشروع متعدد اللاعبين",select:"اختر",eraser:"ممحاة",draw:"رسم",arrow:"سهم",text:"نص",sticky:"لاصق",rectangle:"مربع",ellipse:"بيضاوي",triangle:"مثلث",line:"خط",rotate:"دوّر","lock.aspect.ratio":"قفل نسبة الابعاد","unlock.aspect.ratio":"فتح قفل نسبة الابعاد",group:"جمّع",ungroup:"فك التجمع","move.to.back":"انتقل إلى الخلف","move.backward":"تحريك للخلف","move.forward":"تحريك للأمام","move.to.front":"تحرك للأمام","reset.angle":"إعادة ضبط الزاوية",lock:"قفل",unlock:"الغاء القفل","move.to.page":"انقل إلى الصفحة","flip.horizontal":"انعكاس أفقي","flip.vertical":"انعكاس عمودي",move:"حرّك","to.front":"للامام",forward:"للخلف",backward:"للوراء",back:"خلف",language:"لغة","translation.link":"للمزيد","dock.position":"موقع الادوات",bottom:"اسفل",left:"يسار",right:"يمين",top:"أعلى",page:"صفحة","keyboard.shortcuts":"اختصارات لوحة المفاتيح",search:"بحث",loading:"{dots}تحميل ","export.background":"تصدير الخلفية",transparent:"شـفاف",auto:"ذاتي",light:"خفيف",dark:"غامق"}},{locale:"da",label:"Danish",messages:{"style.menu.color":"Farve","style.menu.fill":"Fyld","style.menu.dash":"Streg","style.menu.size":"St\xf8rrelse","style.menu.keep.open":"Hold \xe5ben","style.menu.font":"Skrifttype","style.menu.align":"Juster",styles:"Format","zoom.in":"Zoom ind","zoom.out":"Zoom ud",to:"til","menu.file":"Fil","menu.edit":"Rediger","menu.view":"Vis","menu.preferences":"Indstillinger","menu.sign.in":"Log ind","menu.sign.out":"Log ud","become.a.sponsor":"Bliv sponsor","zoom.to.selection":"Zoom til valgte","zoom.to.fit":"Zoom til l\xe6rred","zoom.to":"Zoom til","preferences.dark.mode":"M\xf8rkt udseende","preferences.focus.mode":"Fokus tilstand","preferences.debug.mode":"Debug tilstand","preferences.show.grid":"Vis net","preferences.use.cad.selection":"Brug CAD udv\xe6lgelse","preferences.keep.stylemenu.open":"Hold formatmenuen \xe5ben","preferences.always.show.snaps":"Vis altid snaps","preferences.rotate.handles":"Roteringsh\xe5ndtag","preferences.binding.handles":"Bindingsh\xe5ndtag","preferences.clone.handles":"Kloningsh\xe5ndtag",undo:"Fortryd",redo:"Gentag",cut:"Klip",copy:"Kopier",paste:"Inds\xe6t","copy.as":"Kopier som","export.as":"Eksporter som","select.all":"V\xe6lg alt","select.none":"Frav\xe6lg alt",delete:"Slet","new.project":"Nyt projekt",open:"\xc5ben",save:"Gem","save.as":"Gem som","upload.media":"Upload medie","create.page":"Opret side","new.page":"Ny side","page.name":"Sidenavn",duplicate:"Dupliker",cancel:"Fortryd","copy.invite.link":"Kopier invitationslink","create.multiplayer.project":"Opret multiplayer projekt","copy.multiplayer.project":"Kopier til multiplayer projekt",select:"V\xe6lg",eraser:"Viskel\xe6der",draw:"Tegn",arrow:"Pil",text:"Tekst",sticky:"Note",rectangle:"Rektangel",ellipse:"Ellipse",triangle:"Trekant",line:"Linje",rotate:"Roter","lock.aspect.ratio":"L\xe5s billedformat","unlock.aspect.ratio":"Frig\xf8r billedformat",group:"Grupper",ungroup:"Opdel gruppe","move.to.back":"Placer bagerst","move.backward":"Flyt tilbage","move.forward":"Flyt frem","move.to.front":"L\xe6g forrest","reset.angle":"Nulstil vinkel",lock:"L\xe5s",unlock:"L\xe5s op","move.to.page":"Flyt til side","flip.horizontal":"Vend vandret","flip.vertical":"Vend lodret",move:"Flyt","to.front":"Forrest",forward:"Frem",backward:"Tilbage",back:"Bagerst",language:"Sprog"}},{locale:"de",label:"Deutsch",messages:{"style.menu.color":"Farbe","style.menu.fill":"F\xfcllen","style.menu.dash":"Strich","style.menu.size":"Gr\xf6\xdfe","style.menu.keep.open":"Offen halten","style.menu.font":"Schriftart","style.menu.align":"Ausrichten",styles:"Stile","zoom.in":"Heranzoomen","zoom.out":"Herauszoomen",to:"zu","menu.file":"Datei","menu.edit":"Bearbeiten","menu.view":"Ansicht","menu.preferences":"Pr\xe4ferenzen","menu.sign.in":"Einloggen","menu.sign.out":"Ausloggen","become.a.sponsor":"Sponsor werden","zoom.to.selection":"Zur Auswahl zoomen","zoom.to.fit":"Zoom anpassen","zoom.to":"Zoomen nach","preferences.dark.mode":"Dunkler Modus","preferences.focus.mode":"Fokus Modus","preferences.debug.mode":"Debug Modus","preferences.show.grid":"Gitter anzeigen","preferences.use.cad.selection":"CAD Auswahl verwenden","preferences.keep.stylemenu.open":"Stilmen\xfc offen halten","preferences.always.show.snaps":"Hilfslinien immer anzeigen","preferences.rotate.handles":"Rotiergriffe","preferences.binding.handles":"Bindegriffe","preferences.clone.handles":"Klongriffe",undo:"R\xfcckg\xe4ngig machen",redo:"Wiederherstellen",cut:"Ausschneiden",copy:"Kopieren",paste:"Einf\xfcgen","copy.as":"Kopieren als","export.as":"Exportieren als","select.all":"Alles ausw\xe4hlen","select.none":"Nichts ausw\xe4hlen",delete:"L\xf6schen","new.project":"Neues Projekt",open:"\xd6ffnen",save:"Speichern","save.as":"Speichern als","upload.media":"Medien hochladen","create.page":"Seite erstellen","new.page":"Neue Seite","page.name":"Seitenname",duplicate:"Duplizieren",cancel:"Abbrechen","copy.invite.link":"Einladungslink kopieren","create.multiplayer.project":"Mehrspielerprojekt kreieren","copy.multiplayer.project":"In Mehrspielerprojekt kopieren",select:"Ausw\xe4hlen",eraser:"Radierer",draw:"Malen",arrow:"Pfeil",text:"Text",sticky:"Notiz",rectangle:"Rechteck",ellipse:"Ellipse",triangle:"Dreieck",line:"Linie",rotate:"Drehen","lock.aspect.ratio":"Seitenverh\xe4ltnis sperren","unlock.aspect.ratio":"Seitenverh\xe4ltnis entsperren",group:"Gruppieren",ungroup:"Gruppierung aufheben","move.to.back":"Nach Hinten verschieben","move.backward":"R\xfcckw\xe4rts schieben","move.forward":"Vorw\xe4rts schieben","move.to.front":"Nach Vorne verschieben","reset.angle":"Winkel zur\xfccksetzen",lock:"Sperren",unlock:"Entsperren","move.to.page":"Zu Seite verschieben","flip.horizontal":"Horizontal spiegeln","flip.vertical":"Vertikal spiegeln",move:"Verschieben","to.front":"Nach Vorne",forward:"Vorw\xe4rts",backward:"R\xfcckw\xe4rts",back:"Hinten",language:"Sprache"}},{locale:"en",label:"English",messages:{}},{locale:"es",label:"Espa\xf1ol",messages:{"style.menu.color":"Color","style.menu.fill":"Relleno","style.menu.dash":"L\xednea","style.menu.size":"Tama\xf1o","style.menu.keep.open":"Mantener abierto","style.menu.font":"Fuente","style.menu.align":"Alineaci\xf3n",styles:"Estilos","zoom.in":"Acercar","zoom.out":"Alejar",to:"A","menu.file":"Archivo","menu.edit":"Editar","menu.view":"Ver","menu.preferences":"Preferencias","menu.sign.in":"Iniciar sesi\xf3n","menu.sign.out":"Cerrar sesi\xf3n","become.a.sponsor":"Convi\xe9rtete en patrocinador","zoom.to.content":"Acercar al contenido","zoom.to.selection":"Acercar a la selecci\xf3n","zoom.to.fit":"Ajustar a la ventana","zoom.to":"Acercar a","preferences.dark.mode":"Modo oscuro","preferences.focus.mode":"Modo concentraci\xf3n","preferences.debug.mode":"Modo depuraci\xf3n","preferences.show.grid":"Mostrar cuadr\xedcula","preferences.use.cad.selection":"Usar selecci\xf3n CAD","preferences.keep.stylemenu.open":"Mantener men\xfa de estilos abierto","preferences.always.show.snaps":"Mostrar puntos de ajuste","preferences.rotate.handles":"Control de rotaci\xf3n","preferences.binding.handles":"Control de vinculaci\xf3n","preferences.clone.handles":"Control de clonaci\xf3n",undo:"Deshacer",redo:"Rehacer",cut:"Cortar",copy:"Copiar",paste:"Pegar","copy.as":"Copiar como","export.as":"Exportar como","select.all":"Selecionar todo","select.none":"Selecionar nada",delete:"Borrar","new.project":"Nuevo Proyecto",open:"Abrir",save:"Guardar","save.as":"Guardar como","upload.media":"Subir medios","create.page":"Crear p\xe1gina","new.page":"Nueva p\xe1gina","page.name":"Nombre de p\xe1gina",duplicate:"Duplicar",cancel:"Cancelar","copy.invite.link":"Copiar invitaci\xf3n","copy.readonly.link":"Copiar invitaci\xf3n (solo lectura)","create.multiplayer.project":"Crear proyecto multijugador","copy.multiplayer.project":"Copiar proyecto multijugador",select:"Seleccionar",eraser:"Borrador",draw:"Dibujar",arrow:"Flecha",text:"Texto",sticky:"Pegatina",rectangle:"Rect\xe1ngulo",ellipse:"Elipse",triangle:"Tri\xe1ngulo",line:"L\xednea",rotate:"Rotar","lock.aspect.ratio":"Bloquear relaci\xf3n de aspecto","unlock.aspect.ratio":"Desbloquear relaci\xf3n de aspecto",group:"Agrupar",ungroup:"Desagrupar","move.to.back":"Mover al fondo","move.backward":"Mover atr\xe1s","move.forward":"Mover adelante","move.to.front":"Mover al frente","reset.angle":"Restablecer \xe1ngulo",lock:"Bloquear",unlock:"Desbloquear","move.to.page":"Mover a p\xe1gina","flip.horizontal":"Voltear horizontalmente","flip.vertical":"Voltear verticalmente",move:"Mover","to.front":"Al frente",forward:"Adelante",backward:"Atr\xe1s",back:"Fondo",language:"Idioma","translation.link":"Saber m\xe1s","dock.position":"Posici\xf3n del dock",bottom:"Abajo","keyboard.shortcuts":"Atajos de teclado",loading:"Cargando{dots}",left:"Izquierda",right:"Derecha",top:"Arriba",search:"Buscar",page:"P\xe1gina",image:"Imagen"}},{locale:"fa",label:"فارسی",messages:{"style.menu.color":"رنگ","style.menu.fill":"توپُر","style.menu.dash":"خط‌ چین","style.menu.size":"اندازه","style.menu.keep.open":"باز باش","style.menu.font":"فونت","style.menu.align":"تراز",styles:"استایل‌ها","zoom.in":"زوم جلو","zoom.out":"زوم عقب",to:"به","menu.file":"فایل","menu.edit":"ویرایش","menu.view":"نمایش","menu.preferences":"تنظیم‌ها","menu.sign.in":"ورود","menu.sign.out":"خروج","become.a.sponsor":" حامی شو","zoom.to.selection":"نمایش انتخاب‌شده‌ها","zoom.to.fit":"نمایش کل صفحه","zoom.to":"زوم به ","preferences.dark.mode":"حالت شب","preferences.focus.mode":"حالت تمرکز","preferences.debug.mode":"حالت عیب‌یایی","preferences.show.grid":"نمایش خطوط راهنما","preferences.use.cad.selection":"استفاده از حالت انتخابی CAD","preferences.keep.stylemenu.open":"منوی استایل باز باشه","preferences.always.show.snaps":"راهنمای لبه‌ها رو نشون بده","preferences.rotate.handles":"دستگیره‌های چرخش رو نشون بده","preferences.binding.handles":"دستهٔ لبه‌ها رو نشون بده","preferences.clone.handles":"دستگیره‌های کپی رو نشون بده",undo:"یه قدم عقب",redo:"یه قدم جلو",cut:"بریدن",copy:"کپی",paste:"جای‌گذاری","copy.as":"کپی به‌‌عنوان","export.as":"خروجی با فرمت","select.all":"انتخاب همه","select.none":"انتخاب هیچ",delete:" پاک‌کردن","new.project":"پروژهٔ تازه",open:"باز کن",save:"ذخیره","save.as":"ذخیره با اسم","upload.media":"آپلود عکس","create.page":"ساخت صفحه","new.page":"صفحهٔ تازه","page.name":"اسم صفحه",duplicate:"کپی درجا",cancel:"بی‌خیال","copy.invite.link":"کپی لینک دعوت","create.multiplayer.project":"ساخت پروژهٔ چندنفره","copy.multiplayer.project":"کپی در پروژهٔ چندنفره",select:"انتخاب",eraser:"پاک‌کن",draw:"رسم",arrow:"فلِش",text:"متن",sticky:"یادداشت",rectangle:"چارگوش",ellipse:"گردی",triangle:"سه‌گوش",line:"خط",rotate:"چرخش","lock.aspect.ratio":"تثبیت نسبت ابعاد","unlock.aspect.ratio":"نسبت ابعاد متغیر",group:"جمع کن",ungroup:"جدا کن","move.to.back":"ببر آخر","move.backward":"ببر عقب","move.forward":"ببر اول","move.to.front":"ببر جلو","reset.angle":"حذف چرخش",lock:"قفل‌ کن",unlock:"باز کن","move.to.page":"ببر به صفحه","flip.horizontal":"برگردون افقی","flip.vertical":"برگردون عمودی",move:"جابه‌جاش کن","to.front":"به اول",forward:"به جلو",backward:"به عقب",back:"به آخر",language:"زبان"}},{locale:"fr",label:"Fran\xe7ais",messages:{"style.menu.color":"Couleur","style.menu.fill":"Remplir","style.menu.dash":"Bordure","style.menu.size":"Taille","style.menu.keep.open":"Toujours afficher le menu","style.menu.font":"Police","style.menu.align":"Alignement",styles:"Styles","zoom.in":"Zoom avant","zoom.out":"Zoom arri\xe8re",to:"\xc0","menu.tools":"Outils","menu.transform":"Transformation","menu.file":"Fichier","menu.edit":"\xc9dition","menu.view":"Afficher","menu.preferences":"Pr\xe9f\xe9rences","menu.sign.in":"S'authentifier","menu.sign.out":"Se d\xe9connecter","become.a.sponsor":"Devenir un sponsor","zoom.to.content":"Retour au contenu","zoom.to.selection":"Ajuster le zoom \xe0 la s\xe9lection","zoom.to.fit":"Adapter le zoom au contenu","zoom.to":"R\xe9tablir le zoom \xe0","preferences.dark.mode":"Mode sombre","preferences.focus.mode":"Mode focus","preferences.debug.mode":"Afficher la barre d\xe9veloppeur","preferences.show.grid":"Afficher la grille","preferences.use.cad.selection":"Utiliser la s\xe9lection CAD","preferences.keep.stylemenu.open":"Toujours afficher le menu styles","preferences.always.show.snaps":"Afficher les rep\xe8res dynamiques","preferences.rotate.handles":"Manier la rotation","preferences.binding.handles":"Manier les liaisons","preferences.clone.handles":"Manier le clonage",undo:"Annuler",redo:"R\xe9tablir",cut:"Couper",copy:"Copier",paste:"Coller","copy.as":"Copier au format","export.as":"Exporter au format","select.all":"Tout s\xe9lectionner","select.none":"Tout d\xe9s\xe9lectionner",delete:"Supprimer","new.project":"Nouveau project",open:"Ouvrir",save:"Enregistrer","save.as":"Enregistrer sous","upload.media":"Importer un m\xe9dia","create.page":"Cr\xe9er une page","new.page":"Nouvelle Page","page.name":"Nom de la page",duplicate:"Dupliquer",cancel:"Annuler","copy.invite.link":"Copier le lien d'invitation","copy.readonly.link":"Copier le lien en lecture seule d'invitation","create.multiplayer.project":"Cr\xe9er un project multi-joueurs","copy.multiplayer.project":"Copier dans un projet multi-joueurs",select:"S\xe9lection",eraser:"Gomme",draw:"Crayon",arrow:"Fl\xe8che",text:"Texte",sticky:"Note",rectangle:"Rectangle",ellipse:"Cercle",triangle:"Triangle",line:"Ligne",rotate:"Rotation","lock.aspect.ratio":"Verrouiller les proportions","unlock.aspect.ratio":"D\xe9verrouiller les proportions",group:"Grouper",ungroup:"D\xe9grouper","move.to.back":"D\xe9placer \xe0 l'arri\xe8re-plan","move.backward":"Reculer d'un plan","move.forward":"Avancer d'un plan","move.to.front":"Placer au premier plan","reset.angle":"R\xe9initialiser la rotation",lock:"Verrouiller",unlock:"D\xe9verrouiller","align.distribute":"Alignement / R\xe9partion","move.to.page":"D\xe9placer vers la page","flip.horizontal":"Retourner horizontalement","flip.vertical":"Retourner verticalement",move:"Disposition","to.front":"Placer au premier plan",forward:"Avancer d'un plan",backward:"Reculer d'un plan",back:"D\xe9placer \xe0 l'arri\xe8re-plan",language:"Langage","translation.link":"En savoir plus","dock.position":"Position du dock",bottom:"En bas",left:"\xc0 gauche",right:"\xc0 droite",top:"En haut",page:"Page","keyboard.shortcuts":"Raccourcis clavier",search:"Rechercher",loading:"Chargement{dots}","export.background":"Couleur d'arri\xe8re-plan de l'export",transparent:"Transparent",auto:"Automatique",light:"Clair",dark:"Sombre"}},{locale:"he",label:"עברית",messages:{"style.menu.color":"צבע","style.menu.fill":"מלא","style.menu.dash":"גבול","style.menu.size":"גודל","style.menu.keep.open":"השאר פתוח","style.menu.font":"גופן","style.menu.align":"יישור",styles:"עיצוב","zoom.in":"הגדל תצוגה","zoom.out":"הקטן תצוגה",to:"ל","menu.file":"קובץ","menu.edit":"עריכה","menu.view":"תצוגה","menu.preferences":"מאפיינים","menu.sign.in":"הירשם","menu.sign.out":"התנתק","become.a.sponsor":"מתן חסות","zoom.to.selection":"זום לבחירה","zoom.to.fit":"זום להתאמה","zoom.to":"זום ל","preferences.dark.mode":"מצב חשוך","preferences.focus.mode":"מצב פוקוס","preferences.debug.mode":"מצב דיבאג","preferences.show.grid":"(גריד)הראה רשת עימוד","preferences.use.cad.selection":"סימון CAD","preferences.keep.stylemenu.open":"השאר תפריט עיצוב פתוח","preferences.always.show.snaps":"הראה קווי מתאר","preferences.rotate.handles":"הראה ידיות סיבוב","preferences.binding.handles":"הראה ידיות קשירה","preferences.clone.handles":"הראה ידיות שיכפול",undo:"בטל",redo:"עשה מחדש",cut:"גזור",copy:"העתק",paste:"הדבק","copy.as":"העתק כ","export.as":"ייצא כ","select.all":"בחר הכל","select.none":"בטל בחירה",delete:"מחק","new.project":"פרויקט חדש",open:"פתח",save:"שמור","save.as":"שמור כ","upload.media":"העלאת מדיה","create.page":"צור דף","new.page":"דף חדש","page.name":"שם הדף",duplicate:"שכפל",cancel:"בטל","copy.invite.link":"העתק קישור הזמנה","create.multiplayer.project":"צור פרויקט רב משתתפים","copy.multiplayer.project":"העתק לפרויקט רב משתתפים",select:"סמן",eraser:"מחק",draw:"צייר",arrow:"חץ",text:"טקסט",sticky:"דביקי",rectangle:"מרובע",ellipse:"אליפסה",triangle:"משולש",line:"קו",rotate:"סובב","lock.aspect.ratio":"נעל יחס רוחב-גובה","unlock.aspect.ratio":"שחרר נעילת יחס רוחב-גובה",group:"קבץ",ungroup:"בטל קיבוץ","move.to.back":"הבא לתחתית","move.backward":"הזז אחורה","move.forward":"הזז קדימה","move.to.front":"הבא לחזית","reset.angle":"אפס זווית",lock:"נעל",unlock:"שחרר נעילה","move.to.page":"הזז לדף","flip.horizontal":"הפוך אופקית","flip.vertical":"הפוך אנכית",move:"הזז","to.front":"הבא לקדימה",forward:"קדימה",backward:"אחורה",back:"בחזרה",language:"שפה"}},{locale:"it",label:"Italiano",messages:{"style.menu.color":"Colore","style.menu.fill":"Riempi","style.menu.dash":"Tratteggo","style.menu.size":"Dimensione","style.menu.keep.open":"Mantieni aperto","style.menu.font":"Font","style.menu.align":"Allineamento",styles:"Stile","zoom.in":"Ingrandisci","zoom.out":"Rimpicciolisci",to:"Imposta","menu.file":"File","menu.edit":"Modifica","menu.view":"Visualizzazione","menu.preferences":"Preferenze","menu.sign.in":"Accedi","menu.sign.out":"Esci","become.a.sponsor":"Sponsorizza","zoom.to.selection":"Adatta alla selezione","zoom.to.fit":"Adatta","zoom.to":"Ingrandisci","preferences.dark.mode":"Modalit\xe0 scura","preferences.focus.mode":"Modalit\xe0 zen","preferences.debug.mode":"Modalit\xe0 sviluppatore","preferences.show.grid":"Mostra griglia","preferences.use.cad.selection":"Selezione CAD","preferences.keep.stylemenu.open":"Mantieni menu stile aperto","preferences.always.show.snaps":"Mostra sempre le guide","preferences.rotate.handles":"Controlli d'inclinazione","preferences.binding.handles":"Controlli d'associazione","preferences.clone.handles":"Controlli di clonazione",undo:"Annulla",redo:"Ripristina",cut:"Taglia",copy:"Copia",paste:"Incolla","copy.as":"Copia come","export.as":"Esporta come","select.all":"Seleziona tutto","select.none":"Deseleziona tutto",delete:"Elimina","new.project":"Nuovo progetto",open:"Apri",save:"Salva","save.as":"Salva come","upload.media":"Carica contenuti multimediali","create.page":"Crea nuova pagina","new.page":"Nuova pagina","page.name":"Nome pagina",page:"Pagina",duplicate:"Duplica",cancel:"Chiudi","copy.invite.link":"Copia link invito","create.multiplayer.project":"Crea progetto multiplayer","copy.multiplayer.project":"Trasforma in progetto multiplayer",select:"Seleziona",eraser:"Gomma",draw:"Matita",arrow:"Freccia",text:"Casella di testo",sticky:"Post-it",rectangle:"Rettangolo",ellipse:"Ellisse",triangle:"Triangolo",line:"Linea",rotate:"Ruota","lock.aspect.ratio":"Blocca rapporto lati","unlock.aspect.ratio":"Sblocca rapporto lati",group:"Raggruppa",ungroup:"Separa","move.to.back":"Muovi in fondo","move.backward":"Sposta indietro","move.forward":"Sposta avanti","move.to.front":"Muovi in fronte","reset.angle":"Reimposta angolo",lock:"Blocca",unlock:"Sblocca","move.to.page":"Trasferisci a pagina","flip.horizontal":"Ribalta orizzontalmente","flip.vertical":"Ribalta verticalmente",move:"Sposta","to.front":"In primo piano",forward:"Sposta avanti",backward:"Sposta indietro",back:"In fondo",language:"Lingua","dock.position":"Posizione dock",bottom:"In basso",left:"Sinistra",right:"Destra",top:"In Alto"}},{locale:"ja",label:"日本語",messages:{"style.menu.color":"色","style.menu.fill":"塗りつぶし","style.menu.dash":"線","style.menu.size":"大きさ","style.menu.keep.open":"常に表示","style.menu.font":"フォント","style.menu.align":"配置",styles:"スタイル","zoom.in":"拡大","zoom.out":"縮小",to:" ","menu.file":"ファイル","menu.edit":"編集","menu.view":"表示","menu.preferences":"設定","menu.sign.in":"サインイン","menu.sign.out":"サインアウト","become.a.sponsor":"支援する","zoom.to.selection":"選択したアイテムに合わせて拡大","zoom.to.fit":"拡大してすべてを表示","zoom.to":" ","preferences.dark.mode":"ダークモード","preferences.focus.mode":"フォーカスモード","preferences.debug.mode":"デバッグモード","preferences.show.grid":"グリッドを表示","preferences.use.cad.selection":"CADの選択法を使用","preferences.keep.stylemenu.open":"スタイルメニューを常に表示","preferences.always.show.snaps":"スナップを常に表示","preferences.rotate.handles":"回転ハンドルを表示","preferences.binding.handles":"結合ハンドルを表示","preferences.clone.handles":"クローンハンドルを表示",undo:"元に戻す",redo:"やり直し",cut:"切り取り",copy:"コピー",paste:"貼り付け","copy.as":"形式を選択してコピー","export.as":"形式を選択してエクスポート","select.all":"すべて選択","select.none":"選択を解除",delete:"削除","new.project":"新しいプロジェクト",open:"開く",save:"保存","save.as":"名前をつけて保存","upload.media":"メディアをアップロード","create.page":"ページを作成","new.page":"新規ページ","page.name":"ページ名",duplicate:"複製",cancel:"キャンセル","copy.invite.link":"共有リンクをクリップボードにコピー","create.multiplayer.project":"マルチプレイヤープロジェクトを作成","copy.multiplayer.project":"マルチプレイヤープロジェクトにコピー",select:"選択",eraser:"消しゴム",draw:"描画",arrow:"矢印",text:"テキスト",sticky:"ふせん",rectangle:"長方形",ellipse:"楕円形",triangle:"三角形",line:"線",rotate:"回転","lock.aspect.ratio":"縦横比を固定","unlock.aspect.ratio":"縦横比の固定を解除",group:"グルーピング",ungroup:"グループ解除","move.to.back":"最後面に移動","move.backward":"ひとつ後ろに移動","move.forward":"ひとつ前に移動","move.to.front":"最背面に移動","reset.angle":"角度を初期化",lock:"ロック",unlock:"アンロック","move.to.page":"ページへ移動","flip.horizontal":"水平方向に反転","flip.vertical":"垂直方向に反転",move:"移動","to.front":"最前面へ",forward:"ひとつ前へ",backward:"ひとつ後ろへ",back:"最背面へ",language:"言語"}},{locale:"ko-kr",label:"한국어",messages:{"style.menu.color":"색깔","style.menu.fill":"채우기","style.menu.dash":"테두리","style.menu.size":"크기","style.menu.keep.open":"항상 열기","style.menu.font":"글꼴","style.menu.align":"정렬",styles:"스타일","zoom.in":"확대","zoom.out":"축소",to:"to","menu.file":"파일","menu.edit":"편집","menu.view":"보기","menu.preferences":"설정","menu.sign.in":"로그인","menu.sign.out":"로그아웃","become.a.sponsor":"후원자 되기","zoom.to.selection":"선택 요소 맞추기","zoom.to.fit":"전체 맞추기","zoom.to":"맞추기","preferences.dark.mode":"다크 모드","preferences.focus.mode":"집중 모드","preferences.debug.mode":"디버그 모드","preferences.show.grid":"격자 보기","preferences.use.cad.selection":"CAD 선택 사용","preferences.keep.stylemenu.open":"스타일 메뉴 항상 열기","preferences.always.show.snaps":"Snap 항상 열기","preferences.rotate.handles":"회전 보이기","preferences.binding.handles":"Binding Handles","preferences.clone.handles":"복제 보이기",undo:"실행 취소",redo:"다시 실행",cut:"자르기",copy:"복사",paste:"붙여넣기","copy.as":"다른 형식으로 복사하기","export.as":"내보내기","select.all":"전체 선택","select.none":"선택 안함",delete:"삭제하기","new.project":"새 프로젝트",open:"열기",save:"저장","save.as":"다른 이름으로 저장","upload.media":"미디어 업로드","create.page":"새 페이지 만들기","new.page":"새 페이지","page.name":"페이지 이름",duplicate:"복제",cancel:"취소","copy.invite.link":"초대 링크 복사하기","create.multiplayer.project":"공동 프로젝트 만들기","copy.multiplayer.project":"공동 프로젝트로 복사하기",select:"선택",eraser:"지우개",draw:"그리기",arrow:"화살표",text:"텍스트",sticky:"메모",rectangle:"사각형",ellipse:"원",triangle:"삼각형",line:"선",rotate:"회전","lock.aspect.ratio":"비율 잠금","unlock.aspect.ratio":"비율 잠금 해제",group:"그룹화",ungroup:"그룹화 해제","move.to.back":"맨 뒤로 보내기","move.backward":"뒤로 보내기","move.forward":"앞으로 가져오기","move.to.front":"맨 앞으로 가져오기","reset.angle":"회전 초기화",lock:"잠구기",unlock:"잠금 해제하기","move.to.page":"페이지로 이동","flip.horizontal":"수평으로 뒤집기","flip.vertical":"수직으로 뒤집기",move:"순서","to.front":"맨 앞으로",forward:"앞으로",backward:"뒤로",back:"맨 뒤로",language:"언어"}},{locale:"ne",label:"नेपाली",messages:{"style.menu.color":"रंग","style.menu.fill":"भर्नुहोस्","style.menu.dash":"धर्का","style.menu.size":"आकार","style.menu.keep.open":"खुला राख्नुहोस्","style.menu.font":"फन्ट","style.menu.align":"पङ्क्तिबद्ध",styles:"शैलीहरू","zoom.in":"जुम इन","zoom.out":"जुम आउट",to:"टु","menu.file":"फाइल","menu.edit":"सम्पादन गर्नुहोस्","menu.view":"भ्यू","menu.preferences":"प्राथमिकताहरू","menu.sign.in":"साइन इन गर्नुहोस्","menu.sign.out":"साइन आउट गर्नुहोस्","become.a.sponsor":"प्रायोजक बन्नुहोस्","zoom.to.selection":"जुम टु सेलेक्सन","zoom.to.fit":"जुम टु फिट","zoom.to":"जुम टु","preferences.dark.mode":"अँध्यारो मोड","preferences.focus.mode":"फोकस मोड","preferences.debug.mode":"डिबग मोड","preferences.show.grid":"ग्रिड देखाउनुहोस्","preferences.use.cad.selection":"CAD सेलेक्सन गर्नुहोस्","preferences.keep.stylemenu.open":"स्टाइल मेनु खुला राख्नुहोस्","preferences.always.show.snaps":"सधैँ स्न्यापहरू देखाउनुहोस्","preferences.rotate.handles":"ह्यान्डलहरू घुमाउनुहोस्","preferences.binding.handles":"बाइन्डिङ ह्यान्डलहरू","preferences.clone.handles":"क्लोन ह्यान्डलहरू",undo:"पूर्ववत गर्नुहोस्",redo:"पुनः गर्नुहोस्",cut:"कट गर्नुहोस्",copy:"कपि गर्नुहोस्",paste:"पेस्ट गर्नुहोस्","copy.as":"कपि एज","export.as":"एक्सपोर्ट एज","select.all":"सबै छान्नुहोस्","select.none":"केहि पनि सेलेक्ट नगर्नुहोस्",delete:"मेटाउनुहोस्","new.project":"नयाँ परियोजना",open:"खोल्नुहोस्",save:"सुरक्षित गर्नुहोस्","save.as":"सेभ एज","upload.media":"मिडिया अपलोड गर्नुहोस्","create.page":"पृष्ठ सिर्जना गर्नुहोस्","new.page":"नयाँ पृष्ठ सिर्जना गर्नुहोस्","page.name":"पृष्ठको नाम",duplicate:"अनुलिपि गर्नुहोस्",cancel:"रद्द गर्नुहोस्","copy.invite.link":"निमन्त्रणा लिङ्क कपि गर्नुहोस्","create.multiplayer.project":"मल्टिप्लेयर परियोजना सिर्जना गर्नुहोस्","copy.multiplayer.project":"मल्टिप्लेयर प्रोजेक्टमा कपि गर्नुहोस्",select:"सेलेक्ट",eraser:"इरेजर",draw:"चित्र बनाउनु",arrow:"तीर",text:"शब्द",sticky:"टाँसिने",rectangle:"आयत",ellipse:"दीर्घवृत्त",triangle:"त्रिभुज",line:"रेखा",rotate:"घुमाउनुहोस्","lock.aspect.ratio":"आकार अनुपात लक गर्नुहोस्","unlock.aspect.ratio":"आकार अनुपात अनलक गर्नुहोस्",group:"समूह",ungroup:"समूह रद्द गर्नुहोस्","move.to.back":"पछाडि सार्नुहोस्","move.backward":"थप पछाडि सार्नुहोस्","move.forward":"अगाडि सार्नुहोस्","move.to.front":"थप अगाडि सार्नुहोस्","reset.angle":"कोण रिसेट गर्नुहोस्",lock:"लक गर्नुहोस्",unlock:"अनलक गर्नुहोस्","move.to.page":"पृष्ठमा सार्नुहोस्","flip.horizontal":"तेर्सो फ्लिप गर्नुहोस्","flip.vertical":"ठाडो फ्लिप गर्नुहोस्",move:"सार्नुहोस्","to.front":"थप अगाडि",forward:"अगाडि",backward:"पछाडि",back:"थप पछाडि",language:"भाषा"}},{locale:"no",label:"Norwegian",messages:{"style.menu.color":"Farge","style.menu.fill":"Fyll","style.menu.dash":"Linje","style.menu.size":"St\xf8rrelse","style.menu.keep.open":"Hold \xe5pen","style.menu.font":"Teksttype","style.menu.align":"Juster",styles:"Stiler","zoom.in":"Zoom inn","zoom.out":"Zoom ut",to:"til","menu.file":"Fil","menu.edit":"Rediger","menu.view":"Vis","menu.preferences":"Preferanser","menu.sign.in":"Logg inn","menu.sign.out":"Logg ut","become.a.sponsor":"Bli en sponsor","zoom.to.selection":"Zoom til valg","zoom.to.fit":"Zoom for \xe5 passe","zoom.to":"Zoom til","preferences.dark.mode":"M\xf8rk modus","preferences.focus.mode":"Fokus modus","preferences.debug.mode":"Debug modus","preferences.show.grid":"Vis rutenett","preferences.use.cad.selection":"Bruk CAD seleksjon","preferences.keep.stylemenu.open":"Hold stilmeny \xe5pen","preferences.always.show.snaps":"Vis alltid snaps","preferences.rotate.handles":"Vis roteringsh\xe5ndtak","preferences.binding.handles":"Vis bindingsh\xe5ndtak","preferences.clone.handles":"Vis kloningsh\xe5ndtak",undo:"Angre",redo:"Gj\xf8r om",cut:"Klipp ut",copy:"Kopier",paste:"Lim inn","copy.as":"Kopier som","export.as":"Eksporter som","select.all":"Velg alle","select.none":"Velg ingen",delete:"Slett","new.project":"Nytt prosjekt",open:"\xc5pne",save:"Lagre","save.as":"Lagre som","upload.media":"Last opp media","create.page":"Opprett side","new.page":"Ny side","page.name":"Sidenavn",duplicate:"Dupliser",cancel:"Avbryt","copy.invite.link":"Kopier invitasjonslink","create.multiplayer.project":"Opprett et flerspiller prosjekt","copy.multiplayer.project":"Kopier til flerspiller prosjekt",select:"Velg",eraser:"Viskel\xe6r",draw:"Tegn",arrow:"Pil",text:"Tekst",sticky:"Lapp",rectangle:"Rektangel",ellipse:"Ellipse",triangle:"Trekant",line:"Linje",rotate:"Roter","lock.aspect.ratio":"L\xe5s st\xf8rrelsesforhold","unlock.aspect.ratio":"L\xe5s opp st\xf8rrelsesforhold",group:"Grupper",ungroup:"Avgrupper","move.to.back":"Flytt bakerst","move.backward":"Flytt bakover","move.forward":"Flytt forover","move.to.front":"Flytt til front","reset.angle":"Tilbakestill vinkel",lock:"L\xe5s",unlock:"L\xe5s opp","move.to.page":"Flytt til side","flip.horizontal":"Snu horisontalt","flip.vertical":"Snu vertikalt",move:"Flytt","to.front":"Foran",forward:"Framover",backward:"Bakover",back:"Bakerst",language:"Spr\xe5k"}},{locale:"pl",label:"Polski",messages:{"style.menu.color":"Kolor","style.menu.fill":"Wypełnienie","style.menu.dash":"Linia","style.menu.size":"Rozmiar","style.menu.keep.open":"Zachowaj otwarte","style.menu.font":"Czcionka","style.menu.align":"Wyr\xf3wnanie",styles:"Style","zoom.in":"Przybliż","zoom.out":"Oddal",to:"do","menu.file":"Plik","menu.edit":"Edycja","menu.view":"Widok","menu.preferences":"Preferencje","menu.sign.in":"Zaloguj","menu.sign.out":"Wyloguj","become.a.sponsor":"Zostań sponsorem","zoom.to.selection":"Przybliż do zaznaczenia","zoom.to.fit":"Wypełnij ekran","zoom.to":"Przybliż do","preferences.dark.mode":"Tryb ciemny","preferences.focus.mode":"Tryb skupienia","preferences.debug.mode":"Tryb debugowania","preferences.show.grid":"Pokaż siatkę","preferences.use.cad.selection":"Użyj zaznaczania CAD","preferences.keep.stylemenu.open":"Zachowaj menu styli otwarte","preferences.always.show.snaps":"Przyciągaj obiekty","preferences.rotate.handles":"Uchwyty obrotu","preferences.binding.handles":"Uchwyty powiązania","preferences.clone.handles":"Uchwyty klonujące",undo:"Cofnij",redo:"Powt\xf3rz",cut:"Wytnij",copy:"Kopiuj",paste:"Wklej","copy.as":"Kopiuj jako","export.as":"Eksportuj jako","select.all":"Zaznacz wszystko","select.none":"Odznacz wszystko",delete:"Usuń","new.project":"Nowy projekt",open:"Otw\xf3rz",save:"Zapisz","save.as":"Zapisz jako","upload.media":"Załaduj multimedia","create.page":"Utw\xf3rz stronę","new.page":"Nowa strona","page.name":"Nazwa strony",duplicate:"Powiel",cancel:"Anuluj","copy.invite.link":"Kopiuj link zaproszenia","create.multiplayer.project":"Stw\xf3rz projekt wieloosobowy","copy.multiplayer.project":"Kopiuj do projektu wieloosobowego",select:"Zaznacz",eraser:"Gumka",draw:"Rysuj",arrow:"Strzałka",text:"Tekst",sticky:"Naklejka",rectangle:"Prostokąt",ellipse:"Elipsa",triangle:"Tr\xf3jkąt",line:"Linia",rotate:"Obr\xf3ć","lock.aspect.ratio":"Zablokuj proporcje","unlock.aspect.ratio":"Odblokuj proporcje",group:"Grupuj",ungroup:"Rozgrupuj","move.to.back":"Przenieś na tył","move.backward":"Przesuń do tyłu","move.forward":"Przesuń do przodu","move.to.front":"Przenieś na prz\xf3d","reset.angle":"Resetuj kąt",lock:"Zablokuj",unlock:"Odblokuj","move.to.page":"Przenieś na stronę","flip.horizontal":"Odwr\xf3ć w poziomie","flip.vertical":"Odwr\xf3ć w pionie",move:"Przenieś","to.front":"Na wierzch",forward:"Do przodu",backward:"Do tyłu",back:"Na sp\xf3d",language:"Język"}},{locale:"pt",label:"Portugu\xeas - Europeu",messages:{"style.menu.color":"Cor","style.menu.fill":"Preencher","style.menu.dash":"Tra\xe7o","style.menu.size":"Tamanho","style.menu.keep.open":"Manter aberto","style.menu.font":"Fonte","style.menu.align":"Alinhamento",styles:"Estilos","zoom.in":"Aumentar zoom","zoom.out":"Diminuir zoom",to:"para","menu.file":"Ficheiro","menu.edit":"Editar","menu.view":"Visualizar","menu.preferences":"Prefer\xeancias","menu.sign.in":"Entrar","menu.sign.out":"Sair","become.a.sponsor":"Torne-se um patrocinador","zoom.to.selection":"Zoom na sele\xe7\xe3o","zoom.to.fit":"Zoom para caber","zoom.to":"Zoom para","preferences.dark.mode":"Modo Escuro","preferences.focus.mode":"Modo Foco","preferences.debug.mode":"Modo Debug","preferences.show.grid":"Mostrar Grelha","preferences.use.cad.selection":"Usar sele\xe7\xe3o CAD","preferences.keep.stylemenu.open":"Manter Menu de Estilos Aberto","preferences.always.show.snaps":"Mostrar Pontos de Ajuste","preferences.rotate.handles":"Controlo de Rota\xe7\xe3o","preferences.binding.handles":"Controlo de Binds","preferences.clone.handles":"Controlo de Clone",undo:"Desfazer",redo:"Refazer",cut:"Cortar",copy:"Copiar",paste:"Colar","copy.as":"Copiar como","export.as":"Exportar como","select.all":"Selecionar todos","select.none":"Selecionar nenhum",delete:"Apagar","new.project":"Novo Projeto",open:"Abrir",save:"Salvar","save.as":"Salvar Como","upload.media":"Upload M\xe9dia","create.page":"Criar P\xe1gina","new.page":"Nova P\xe1gina","page.name":"Nome da P\xe1gina",duplicate:"Duplicar",cancel:"Cancelar","copy.invite.link":"Copiar Link de Convite","create.multiplayer.project":"Criar um Projeto Multi-Utilizador","copy.multiplayer.project":"Copiar num Projeto Multi-Utilizador",select:"Selecionar",eraser:"Borracha",draw:"Desenhar",arrow:"Seta",text:"Texto",sticky:"Post-it",rectangle:"Ret\xe2ngulo",ellipse:"Elipse",triangle:"Tri\xe2ngulo",line:"Linha",rotate:"Rodar","lock.aspect.ratio":"Trancar a Propor\xe7\xe3o","unlock.aspect.ratio":"Destrancar a Propor\xe7\xe3o",group:"Agrupar",ungroup:"Desagrupar","move.to.back":"Colocar no Fundo","move.backward":"Mover abaixo","move.forward":"Mover acima","move.to.front":"Colocar \xe0 Frente","reset.angle":"Reiniciar \xc2ngulo",lock:"Trancar",unlock:"Destrancar","move.to.page":"Mover para P\xe1gina","flip.horizontal":"Inverter Horizontalmente","flip.vertical":"Inverter Verticalmente",move:"Mover","to.front":"Para Frente",forward:"Avan\xe7ar",backward:"Recuar",back:"Voltar",language:"L\xedngua"}},{locale:"pt-br",label:"Portugu\xeas - Brasil",messages:{"style.menu.color":"Cor","style.menu.fill":"Preencher","style.menu.dash":"Tra\xe7o","style.menu.size":"Tamanho","style.menu.keep.open":"Manter aberto","style.menu.font":"Fonte","style.menu.align":"Alinhamento",styles:"Estilos","zoom.in":"Aumentar zoom","zoom.out":"Diminuir zoom",to:"para","menu.file":"Arquivo","menu.edit":"Editar","menu.view":"Visualizar","menu.preferences":"Prefer\xeancias","menu.sign.in":"Entrar","menu.sign.out":"Sair","become.a.sponsor":"Torne-se um patrocinador","zoom.to.selection":"Zoom para a sele\xe7\xe3o","zoom.to.fit":"Zoom para ajuste","zoom.to":"Zoom para","preferences.dark.mode":"Modo Escuro","preferences.focus.mode":"Modo Foco","preferences.debug.mode":"Modo Debug","preferences.show.grid":"Mostrar Grade","preferences.use.cad.selection":"Usar sele\xe7\xe3o CAD","preferences.keep.stylemenu.open":"Manter Menu de Estilos Aberto","preferences.always.show.snaps":"Mostrar Pontos de Ajuste","preferences.rotate.handles":"Controle de Rota\xe7\xe3o","preferences.binding.handles":"Controle de V\xednculos","preferences.clone.handles":"Controle de Clone",undo:"Desfazer",redo:"Refazer",cut:"Cortar",copy:"Copiar",paste:"Colar","copy.as":"Copiar como","export.as":"Exportar como","select.all":"Selecionar todos","select.none":"Selecionar nenhum",delete:"Deletar","new.project":"Novo Projeto",open:"Abrir",save:"Salvar","save.as":"Salvar Como","upload.media":"Carregar M\xeddia","create.page":"Criar P\xe1gina","new.page":"Nova P\xe1gina","page.name":"Nome da P\xe1gina",duplicate:"Duplicar",cancel:"Cancelar","copy.invite.link":"Copiar Link de Convite","create.multiplayer.project":"Criar um Projeto Multijogador","copy.multiplayer.project":"Copiar para Projeto Multijogador",select:"Selecionar",eraser:"Borracha",draw:"Desenhar",arrow:"Seta",text:"Texto",sticky:"Adesivo",rectangle:"Ret\xe2ngulo",ellipse:"Elipse",triangle:"Tri\xe2ngulo",line:"Linha",rotate:"Rotacionar","lock.aspect.ratio":"Travar Propor\xe7\xe3o da Tela","unlock.aspect.ratio":"Destravar Propor\xe7\xe3o da Tela",group:"Agrupar",ungroup:"Desagrupar","move.to.back":"Recuar","move.backward":"Enviar para Tr\xe1s","move.forward":"Avan\xe7ar","move.to.front":"Trazer para Frente","reset.angle":"Reiniciar \xc2ngulo",lock:"Travar",unlock:"Destravar","move.to.page":"Mover para P\xe1gina","flip.horizontal":"Virar Horizontalmente","flip.vertical":"Virar Verticalmente",move:"Mover","to.front":"Para Frente",forward:"Avan\xe7ar",backward:"Recuar",back:"Voltar",language:"Idioma"}},{locale:"ru",label:"Russian",messages:{"style.menu.color":"Цвет","style.menu.fill":"Заполнять","style.menu.dash":"Штрих","style.menu.size":"Размер","style.menu.keep.open":"Держать открытым","style.menu.font":"Шрифт","style.menu.align":"Выравнивание",styles:"Стиль","zoom.in":"Увеличить","zoom.out":"Уменьшить",to:"к","menu.tools":"Инструменты","menu.transform":"Изменение","menu.file":"Файл","menu.edit":"Редактирование","menu.view":"Вид","menu.preferences":"Настройки","menu.sign.in":"Войти","menu.sign.out":"Выйти","become.a.sponsor":"Стать спонсором","zoom.to.content":"Вернуться к содержимому","zoom.to.selection":"Масштабировать по выделению","zoom.to.fit":"Масштабировать по размеру экрана","zoom.to":"Масштабировать к","preferences.dark.mode":"Тёмная тема","preferences.focus.mode":"Минималистичный режим","preferences.debug.mode":"Режим отладки","preferences.show.grid":"Показать сетку","preferences.use.cad.selection":"Использовать CAD выделение","preferences.keep.stylemenu.open":"Держать меню стилей открытым","preferences.always.show.snaps":"Всегда показывать привязки","preferences.rotate.handles":"Ручки вращения","preferences.binding.handles":"Ручки привязки","preferences.clone.handles":"Ручки клонирования",undo:"Отменить",redo:"Повторить",cut:"Вырезать",copy:"Копировать",paste:"Вставить","copy.as":"Копировать как","export.as":"Экспортировать как","select.all":"Выделить всё","select.none":"Снять выделение",delete:"Удалить","new.project":"Новый проект",open:"Открыть",save:"Сохранить","save.as":"Сохранить как","upload.media":"Загрузить медиафайл","create.page":"Создать страницу","new.page":"Новая страница","page.name":"Название страницы",duplicate:"Дублировать",cancel:"Отменить","copy.invite.link":"Скопировать ссылку приглашения","copy.readonly.link":"Скопировать ссылку только для чтения","create.multiplayer.project":"Создать многопользовательский проект","copy.multiplayer.project":"Скопировать в многопользовательский проект",select:"Выделить",eraser:"Ластик",draw:"Рисовать",arrow:"Стрелка",text:"Текст",sticky:"Заметка",rectangle:"Прямоугольник",ellipse:"Эллипс",triangle:"Треугольник",line:"Линия",rotate:"Повернуть","lock.aspect.ratio":"Заблокировать соотношение сторон","unlock.aspect.ratio":"Разблокировать соотношение сторон",group:"Сгруппировать",ungroup:"Разгруппировать","move.to.back":"Переместить назад","move.backward":"Переместить на задний план","move.forward":"Переместить вперёд","move.to.front":"Переместить на передний план","reset.angle":"Сбросить угол",lock:"Блокировать",unlock:"Разблокировать","align.distribute":"Выровнять / распределить","move.to.page":"Переместить на страницу","flip.horizontal":"Перевернуть горизонтально","flip.vertical":"Перевернуть вертикально",move:"Переместить","to.front":"На передний план",forward:"Вперед",backward:"На задний план",back:"Назад",language:"Язык","translation.link":"Подробнее","dock.position":"Расположение панели инструментов",bottom:"Снизу",left:"Слева",right:"Справа",top:"Сверху",page:"Страница","keyboard.shortcuts":"Сочетания клавиш",search:"Поиск",loading:"Загрузка","export.background":"Экспорт фона",transparent:"Прозрачный",auto:"Авто",light:"Светлый",dark:"Тёмный",image:"Изображение","align.left":"Выровнять по левому краю","align.center.x":"Выровнять по центру горизонтально","align.right":"Выровнять по правому краю","align.top":"Выровнять по верхнему краю","align.center.y":"Выровнять по центру вертикально","align.bottom":"Выровнять по нижнему краю","distribute.x":"Распределить горизонтально","distribute.y":"Распределить вертикально","stretch.x":"Растянуть горизонтально","stretch.y":"Растянуть вертикально",share:"Поделиться","copy.current.page.link":"Скопировать ссылку на текущую страницу","copy.project.link":"Скопировать ссылку на проект","data.too.big.encoded":"Данные слишком велики, чтобы закодировать их в ссылке. Не включайте изображение или видео!","dialog.save.firsttime":"Сохранить текущий проект?","dialog.save.again":"Сохранить изменения в текущем проекте?","dialog.cancel":"Отменить","dialog.no":"Нет","dialog.yes":"Да","enter.file.name":"Введите имя файла"}},{locale:"sv",label:"Svenska",messages:{"style.menu.color":"F\xe4rg","style.menu.fill":"Ifylld","style.menu.dash":"Streck","style.menu.size":"Storlek","style.menu.keep.open":"H\xe5ll stilmenyn \xf6ppen","style.menu.font":"Typsnitt","style.menu.align":"Justera",styles:"Utseende","zoom.in":"Zooma in","zoom.out":"Zooma ut",to:"Till","menu.tools":"Verktyg","menu.transform":"Transform","menu.file":"Arkiv","menu.edit":"Redigera","menu.view":"Inneh\xe5ll","menu.preferences":"Inst\xe4llningar","menu.sign.in":"Logga in","menu.sign.out":"Logga ut","become.a.sponsor":"Bli en sponsor","zoom.to.content":"Anpassa zoom till inneh\xe5ll","zoom.to.selection":"Anpassa zoom till urval","zoom.to.fit":"Anpassa zoom till sk\xe4rm","zoom.to":"Zooma till","preferences.dark.mode":"M\xf6rkt l\xe4ge","preferences.focus.mode":"Fokusl\xe4ge","preferences.debug.mode":"Debugl\xe4ge","preferences.show.grid":"Visa rutn\xe4t","preferences.use.cad.selection":"V\xe4lj som i CAD-mjukvara","preferences.keep.stylemenu.open":"H\xe5ll stilmenyn \xf6ppen","preferences.always.show.snaps":"Visa alltid f\xe4stpunkter","preferences.rotate.handles":"Rotationshandtag","preferences.binding.handles":"Bindningshandtag","preferences.clone.handles":"Kloningshandtag",undo:"\xc5ngra",redo:"G\xf6r om",cut:"Klipp ut",copy:"Kopiera",paste:"Klistra in","copy.as":"Kopiera som","export.as":"Exportera till","select.all":"V\xe4lj alla","select.none":"V\xe4lj ingen",delete:"Radera","new.project":"Nytt projekt",open:"\xd6ppna",save:"Spara","save.as":"Spara som","upload.media":"Ladda upp media","create.page":"Skapa sida","new.page":"Ny sida","page.name":"Sidnamn",duplicate:"Duplicera",cancel:"Avbryt","copy.invite.link":"Kopiera l\xe4nk med redigeringsr\xe4ttigheter","copy.readonly.link":"Kopiera l\xe4nk med l\xe4sr\xe4ttigheter","create.multiplayer.project":"Skapa ett Multiplayer-projekt","copy.multiplayer.project":"Kopiera till Multiplayer-project",select:"V\xe4lj",eraser:"Radera",draw:"Rita",arrow:"Pil",text:"Text",sticky:"Klisterlapp",rectangle:"Rektangel",ellipse:"Ellips",triangle:"Triangel",line:"Linje",rotate:"Rotera","lock.aspect.ratio":"L\xe5s storleksf\xf6rh\xe5llande","unlock.aspect.ratio":"L\xe5s upp storleksf\xf6rh\xe5llande",group:"Gruppera",ungroup:"Avgruppera","move.to.back":"Placera l\xe4ngst bak","move.backward":"Flytta bak\xe5t","move.forward":"Flytta fram\xe5t","move.to.front":"Placera l\xe4ngst fram","reset.angle":"\xc5terst\xe4ll vinkel",lock:"L\xe5s",unlock:"L\xe5s upp","align.distribute":"Justera / Placera","move.to.page":"Flytta till sida","flip.horizontal":"V\xe4nd horisontellt","flip.vertical":"V\xe4nd vertikalt",move:"Flytta","to.front":"Till l\xe4ngst fram",forward:"Fram\xe5t",backward:"Bak\xe5t",back:"Till l\xe4ngst bak",language:"Spr\xe5k","translation.link":"Mer information","dock.position":"Dockningsposition",bottom:"Botten",left:"V\xe4nster",right:"H\xf6ger",top:"Topp",page:"Sida","keyboard.shortcuts":"Tangentbordsgenv\xe4gar",search:"S\xf6k",loading:"Laddar{dots}","export.background":"Exportbakgrund",transparent:"Transparent",auto:"Auto",light:"Ljus",dark:"M\xf6rk",github:"Github",twitter:"Twitter",discord:"Discord",image:"Bild"}},{locale:"tr",label:"T\xfcrk\xe7e",messages:{"style.menu.color":"Renk","style.menu.fill":"Doldur","style.menu.dash":"\xc7izgi","style.menu.size":"Boyut","style.menu.keep.open":"A\xe7ık Tut","style.menu.font":"Yazı Tipi","style.menu.align":"Hizala",styles:"Stiller","zoom.in":"Yakınlaştır","zoom.out":"Uzaklaştır",to:"","menu.file":"Dosya","menu.edit":"D\xfczenle","menu.view":"G\xf6r\xfcnt\xfc","menu.preferences":"Tercihler","menu.sign.in":"Giriş Yap","menu.sign.out":"Oturumu Kapat","become.a.sponsor":"Sponsor Ol","zoom.to.selection":"Se\xe7ime Yakınlaştır","zoom.to.fit":"Sığdırmak i\xe7in Yakınlaştır","zoom.to":"Yakınlaştır","preferences.dark.mode":"Karanlık Mod","preferences.focus.mode":"Odak Modu","preferences.debug.mode":"Debug Modu","preferences.show.grid":"Izgarayı G\xf6ster","preferences.use.cad.selection":"CAD Se\xe7imi Kullan","preferences.keep.stylemenu.open":"Stil Men\xfcs\xfcn\xfc A\xe7ık Tut","preferences.always.show.snaps":"Hiza \xc7izgilerini Hep G\xf6ster","preferences.rotate.handles":"Rotasyon Kontrolc\xfcleri","preferences.binding.handles":"Bağlama Kontrolc\xfcleri","preferences.clone.handles":"Klon Kontrolc\xfcleri",undo:"Geri Al",redo:"Yinele",cut:"Kes",copy:"Kopyala",paste:"Yapıştır","copy.as":"Olarak Kopyala","export.as":"Olarak Dışarı Aktar","select.all":"Hepsini Se\xe7","select.none":"Hi\xe7birini Se\xe7me",delete:"Sil","new.project":"Yeni Proje",open:"A\xe7",save:"Kaydet","save.as":"Farklı Kaydet","upload.media":"Medya Y\xfckle","create.page":"Sayfa Oluştur","new.page":"Yeni Sayfa","page.name":"Sayfa İsmi",duplicate:"Kopya Oluştur",cancel:"İptal","copy.invite.link":"Davet Linkini Kopyala","create.multiplayer.project":"\xc7ok Oyunculu Proje Oluştur","copy.multiplayer.project":"\xc7ok Oyunculu Projeye Kopyala",select:"Se\xe7",eraser:"Silgi",draw:"\xc7izim",arrow:"Ok",text:"Yazı",sticky:"Yapışkan",rectangle:"Dikd\xf6rtgen",ellipse:"Elips",triangle:"\xdc\xe7gen",line:"\xc7izgi",rotate:"D\xf6nd\xfcr","lock.aspect.ratio":"En Boy Oranını Kilitle","unlock.aspect.ratio":"En Boy Oranı Kilidini A\xe7",group:"Grupla",ungroup:"Gruplamayı Kaldır","move.to.back":"Arkaya Taşı","move.backward":"En Arkaya Taşı","move.forward":"En \xd6ne Taşı","move.to.front":"\xd6ne Taşı","reset.angle":"A\xe7ıyı Sıfırla",lock:"Kilitle",unlock:"Kilidini A\xe7","move.to.page":"Sayfaya Taşı","flip.horizontal":"Yatay \xc7evir","flip.vertical":"Dikey \xc7evir",move:"Taşı","to.front":"\xd6ne",forward:"En \xd6ne",backward:"En Arkaya",back:"Arkaya",language:"Dil"}},{locale:"uk",label:"Ukrainian",messages:{"style.menu.color":"Колір","style.menu.fill":"Заповнювати","style.menu.dash":"Штрих","style.menu.size":"Розмір","style.menu.keep.open":"Тримати відкритим","style.menu.font":"Шрифт","style.menu.align":"Вирівняти",styles:"Стиль","zoom.in":"Збільшити","zoom.out":"Зменшити",to:"до","menu.file":"Файл","menu.edit":"Редагування","menu.view":"Вигляд","menu.preferences":"Налаштування","menu.sign.in":"Увійти","menu.sign.out":"Вийти","become.a.sponsor":"Стати спонсором","zoom.to.selection":"Наблизити до виділення","zoom.to.fit":"Збільшити за розміром екрану","zoom.to":"Наблизити до","preferences.dark.mode":"Темна тема","preferences.focus.mode":"Мінімалістичний режим","preferences.debug.mode":"Режим налагодження","preferences.show.grid":"Показати сітку","preferences.use.cad.selection":"Використовувати CAD виділення","preferences.keep.stylemenu.open":"Тримати меню стилів відкритим","preferences.always.show.snaps":"Завжди показувати прив'язки","preferences.rotate.handles":"Ручки обертання","preferences.binding.handles":"Ручки прив'язки","preferences.clone.handles":"Ручки клонування",undo:"Скасувати",redo:"Повторити",cut:"Вирізати",copy:"Скопіювати",paste:"Вставити","copy.as":"Скопіювати як","export.as":"Експортувати як","select.all":"Обрати все","select.none":"Зняти виділення",delete:"Видалити","new.project":"Новий проект",open:"Відкрити",save:"Зберегти","save.as":"Зберегти як","upload.media":"Завантажити медіа","create.page":"Створити сторінку","new.page":"Нова сторінка","page.name":"Назва сторінки",duplicate:"Дублювати",cancel:"Скасувати","copy.invite.link":"Скопіювати посилання на запрошення","create.multiplayer.project":"Створити багатокористувацький проект","copy.multiplayer.project":"Скопіювати в багатокористувацький проект",select:"Вибирати",eraser:"Ластик",draw:"Малювати",arrow:"Стрілка",text:"Текст",sticky:"Нотатка",rectangle:"Прямокутник",ellipse:"Еліпс",triangle:"Трикутник",line:" Лінія",rotate:"Повернути","lock.aspect.ratio":"Заблокувати співвідношення сторін","unlock.aspect.ratio":" Розблокувати співвідношення сторін",group:"Згрупувати",ungroup:" Розгрупувати","move.to.back":"Перемістити назад","move.backward":"Перемістити на задній план","move.forward":"Перемістити вперед","move.to.front":"Перемістити на передній план","reset.angle":"Скидання кута",lock:"Блокування",unlock:" Розблокування","move.to.page":"Перейти на сторінку","flip.horizontal":"Перевернути горизонтально","flip.vertical":"Перевернути вертикально",move:"Перемістити","to.front":"На передній план",forward:" Вперед",backward:"На задній план",back:"Назад",language:"Мова"}},{locale:"zh-ch",label:"简体中文",messages:{"style.menu.color":"颜色","style.menu.fill":"填充","style.menu.dash":"边框","style.menu.size":"尺寸","style.menu.keep.open":"保持常开","style.menu.font":"字体","style.menu.align":"对齐",styles:"样式","zoom.in":"放大","zoom.out":"缩小",to:"缩放至","menu.tools":"工具","menu.transform":"转换","menu.file":"文件","menu.edit":"编辑","menu.view":"视图","menu.preferences":"偏好","menu.sign.in":"登录","menu.sign.out":"登出",search:"搜索","become.a.sponsor":"成为赞助者","zoom.to.selection":"缩放选中","zoom.to.fit":"自适应缩放","zoom.to":"缩放至","zoom.to.content":"缩放至内容","preferences.dark.mode":"暗黑模式","preferences.focus.mode":"专注模式","preferences.debug.mode":"调试模式","preferences.show.grid":"显示网格","preferences.use.cad.selection":"使用 CAD 选择","preferences.keep.stylemenu.open":"保持样式菜单常开","preferences.always.show.snaps":"总是展示对齐线","preferences.rotate.handles":"旋转手柄","preferences.binding.handles":"捆绑手柄","preferences.clone.handles":"克隆手柄",undo:"撤销",redo:"重做",cut:"剪切",copy:"复制",paste:"粘贴","copy.as":"复制为","export.as":"导出为","select.all":"选中全部","select.none":"取消选中",delete:"删除","new.project":"新项目",open:"打开",save:"保存","save.as":"保存为","upload.media":"上传媒体文件","create.page":"创建页面","new.page":"新页面","page.name":"页面名称",duplicate:"复制",cancel:"取消","copy.invite.link":"复制邀请链接","create.multiplayer.project":"创建多人项目","copy.multiplayer.project":"复制到多人项目",select:"选择",eraser:"橡皮",draw:"画笔",arrow:"箭头",text:"文本",sticky:"便利贴",rectangle:"矩形",ellipse:"椭圆形",triangle:"三角形",line:"直线",rotate:"旋转","lock.aspect.ratio":"锁定宽高比","unlock.aspect.ratio":"解锁宽高比",group:"分组",ungroup:"取消分组","move.to.back":"置底","move.backward":"下移一层","move.forward":"上移一层","move.to.front":"置顶","reset.angle":"重置旋转角度",lock:"锁定",unlock:"解锁","move.to.page":"移动到页面","flip.horizontal":"水平翻转","flip.vertical":"垂直翻转",move:"移动","to.front":"置顶",forward:"上一层",backward:"下一层",back:"置底",language:"语言","keyboard.shortcuts":"键盘快捷键","translation.link":"了解更多",page:"页面","dock.position":"锚点位置",bottom:"下面",left:"左面",right:"右面",top:"上面","export.background":"导出背景色",transparent:"透明",auto:"自动",light:"明亮",dark:"暗黑","copy.readonly.link":"复制只读链接",image:"图片","align.distribute":"对齐 / 分散","dialog.save.firsttime":"您是否想保存当前的项目？","dialog.save.again":"您是否想保存对当前项目的更改？","dialog.cancel":"取消","dialog.no":"否","dialog.yes":"是"}},{locale:"zh-tw",label:"繁體中文 (台灣)",messages:{"style.menu.color":"顏色","style.menu.fill":"填充","style.menu.dash":"虛線","style.menu.size":"大小","style.menu.keep.open":"保持開啟","style.menu.font":"字型","style.menu.align":"對齊",styles:"樣式","zoom.in":"放大","zoom.out":"縮小",to:"至","menu.file":"檔案","menu.edit":"編輯","menu.view":"檢視","menu.preferences":"選項","menu.sign.in":"登入","menu.sign.out":"登出","become.a.sponsor":"成為贊助者","zoom.to.selection":"縮放至選取範圍","zoom.to.fit":"縮放至適當大小","zoom.to":"縮放至","preferences.dark.mode":"深色模式","preferences.focus.mode":"專注模式","preferences.debug.mode":"除錯模式","preferences.show.grid":"顯示網格","preferences.use.cad.selection":"使用 CAD 選取","preferences.keep.stylemenu.open":"樣式選單保持開啟","preferences.always.show.snaps":"永遠顯示對齊線","preferences.rotate.handles":"旋轉控點","preferences.binding.handles":"綁定控點","preferences.clone.handles":"複製控點",undo:"復原",redo:"取消復原",cut:"剪下",copy:"複製",paste:"貼上","copy.as":"複製成","export.as":"匯出成","select.all":"全選","select.none":"取消選取",delete:"刪除","new.project":"新專案",open:"開啟",save:"儲存","save.as":"另存為","upload.media":"上傳媒體","create.page":"建立頁面","new.page":"新頁面","page.name":"頁面名稱",duplicate:"複製",cancel:"取消","copy.invite.link":"複製邀請連結","copy.readonly.link":"複製唯讀連結","create.multiplayer.project":"建立多人專案","copy.multiplayer.project":"複製至多人專案",select:"選取",eraser:"橡皮擦",draw:"手繪",arrow:"箭頭",text:"文字",sticky:"便利貼",rectangle:"長方形",ellipse:"橢圓形",triangle:"三角形",line:"直線",rotate:"旋轉","lock.aspect.ratio":"鎖定長寬比","unlock.aspect.ratio":"解鎖長寬比",group:"組成群組",ungroup:"取消群組","move.to.back":"移到最下層","move.backward":"下移一層","move.forward":"上移一層","move.to.front":"移到最上層","reset.angle":"重設角度",lock:"鎖定",unlock:"解鎖","move.to.page":"移至頁面","flip.horizontal":"水平翻轉","flip.vertical":"垂直翻轉",move:"移動","to.front":"到最上層",forward:"到上一層",backward:"到下一層",back:"到最下層",language:"語言","translation.link":"了解詳情","dock.position":"工具列位置",bottom:"下方",left:"左側",right:"右側",top:"上方",page:"頁面"}}];en.sort((e,t)=>e.locale<t.locale?-1:1);var ea=n.createContext({}),eo=()=>{let e=n.useContext(ea);if(!e)throw Error("useCtx must be inside a Provider with a value");return e},{styled:er,createTheme:el}=(0,d.GC)({themeMap:C({},d.zk),theme:{colors:{bounds:"rgba(65, 132, 244, 1.000)",boundsBg:"rgba(65, 132, 244, 0.05)",hover:"#ececec",overlay:"rgba(0, 0, 0, 0.15)",overlayContrast:"rgba(255, 255, 255, 0.15)",panel:"#fefefe",panelContrast:"#ffffff",selected:"rgba(66, 133, 244, 1.000)",selectedContrast:"#fefefe",text:"#333333",tooltip:"#1d1d1d",tooltipContrast:"#ffffff",warn:"rgba(255, 100, 100, 1)",canvas:"rgb(248, 249, 250)"},shadows:{2:"0px 1px 1px rgba(0, 0, 0, 0.14)",3:"0px 2px 3px rgba(0, 0, 0, 0.14)",4:"0px 4px 5px -1px rgba(0, 0, 0, 0.14)",8:"0px 12px 17px rgba(0, 0, 0, 0.14)",12:"0px 12px 17px rgba(0, 0, 0, 0.14)",24:"0px 24px 38px rgba(0, 0, 0, 0.14)",key:"1px 1px rgba(0,0,0,1)",panel:`0px 0px 16px -1px rgba(0, 0, 0, 0.05), 
        0px 0px 16px -8px rgba(0, 0, 0, 0.05), 
        0px 0px 16px -12px rgba(0, 0, 0, 0.12),
        0px 0px 2px 0px rgba(0, 0, 0, 0.08)`},space:{0:"2px",1:"3px",2:"4px",3:"8px",4:"12px",5:"16px",6:"32px",7:"48px"},fontSizes:{0:"10px",1:"12px",2:"13px",3:"16px",4:"18px"},fonts:{ui:'"Recursive", system-ui, sans-serif',body:'"Recursive", system-ui, sans-serif',mono:'"Recursive Mono", monospace'},fontWeights:{},lineHeights:{},letterSpacings:{},sizes:{},borderWidths:{0:"$1"},borderStyles:{},radii:{0:"2px",1:"4px",2:"8px",3:"12px",4:"16px"},zIndices:{},transitions:{}},media:{micro:"(max-width: 370px)",sm:"(min-width: 640px)",md:"(min-width: 768px)",lg:"(min-width: 1024px)"},utils:{zStrokeWidth:()=>e=>Array.isArray(e)?{strokeWidth:`calc(${e[0]}px / var(--camera-zoom))`}:{strokeWidth:`calc(${e}px / var(--camera-zoom))`}}}),ed=el({colors:{bounds:"rgba(38, 150, 255, 1.000)",boundsBg:"rgba(38, 150, 255, 0.05)",hover:"#444A50",overlay:"rgba(0, 0, 0, 0.15)",overlayContrast:"rgba(255, 255, 255, 0.15)",panel:"#363D44",panelContrast:"#49555f",selected:"rgba(38, 150, 255, 1.000)",selectedContrast:"#fefefe",text:"#f8f9fa",tooltip:"#1d1d1d",tooltipContrast:"#ffffff",canvas:"#212529"},shadows:{2:"0px 1px 1px rgba(0, 0, 0, 0.24)",3:"0px 2px 3px rgba(0, 0, 0, 0.24)",4:"0px 4px 5px -1px rgba(0, 0, 0, 0.24)",8:"0px 12px 17px rgba(0, 0, 0, 0.24)",12:"0px 12px 17px rgba(0, 0, 0, 0.24)",24:"0px 24px 38px rgba(0, 0, 0, 0.24)",panel:`0px 0px 16px -1px rgba(0, 0, 0, 0.05), 
      0px 0px 16px -8px rgba(0, 0, 0, 0.09), 
      0px 0px 16px -12px rgba(0, 0, 0, 0.2)`}}),eh=e=>e.appState.isLoading;function ep(){let e=et().useStore(eh);return n.createElement(ec,{hidden:!e},n.createElement(r.A,{id:"loading",values:{dots:"..."}}))}var ec=er("div",{position:"absolute",top:0,left:"50%",transform:"translate(-50%, 0)",borderBottomLeftRadius:"12px",borderBottomRightRadius:"12px",padding:"8px 16px",fontFamily:"var(--fonts-ui)",fontSize:"var(--fontSizes-1)",boxShadow:"var(--shadows-panel)",backgroundColor:"white",zIndex:200,pointerEvents:"none","& > div > *":{pointerEvents:"all"},variants:{transform:{hidden:{transform:"translate(-50%, 100%)"},visible:{transform:"translate(-50%, 0%)"}}}}),eu="-0.03em",eg=[.5,.5],em=".tldr",ef={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>e*(2-e),easeInOutQuad:e=>e<.5?2*e*e:-1+(4-2*e)*e,easeInCubic:e=>e*e*e,easeOutCubic:e=>--e*e*e+1,easeInOutCubic:e=>e<.5?4*e*e*e:(e-1)*(2*e-2)*(2*e-2)+1,easeInQuart:e=>e*e*e*e,easeOutQuart:e=>1- --e*e*e*e,easeInOutQuart:e=>e<.5?8*e*e*e*e:1-8*--e*e*e*e,easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>1+--e*e*e*e*e,easeInOutQuint:e=>e<.5?16*e*e*e*e*e:1+16*--e*e*e*e*e,easeInSine:e=>1-Math.cos(e*Math.PI/2),easeOutSine:e=>Math.sin(e*Math.PI/2),easeInOutSine:e=>-(Math.cos(Math.PI*e)-1)/2,easeInExpo:e=>e<=0?0:Math.pow(2,10*e-10),easeOutExpo:e=>e>=1?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>e<=0?0:e>=1?1:e<.5?Math.pow(2,20*e-10)/2:(2-Math.pow(2,-20*e+10))/2},ev=["#EC5E41","#F2555A","#F04F88","#E34BA9","#BD54C6","#9D5BD2","#7B66DC","#02B1CC","#11B3A3","#39B178","#55B467","#FF802B"],eb="undefined"!=typeof Window&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent),ey="undefined"!=typeof Window&&/linux/i.test(navigator.userAgent),eS=[".png",".svg",".jpg",".jpeg",".gif"],eI=eb?[]:[".mp4",".webm"],ek={};function ew(e,t){let i=s.Aq.getFromCache(t,e,()=>{let[t,i]=e.size;return{minX:0,maxX:t,minY:0,maxY:i,width:t,height:i}});return s.Aq.translateBounds(i,e.point)}E(ek,{LabelMask:()=>ej,PolygonUtils:()=>eF,TextAreaUtils:()=>e4,TextLabel:()=>ts,clearPrevSize:()=>eB,defaultStyle:()=>e5,defaultTextStyle:()=>e3,fills:()=>e_,getBoundsRectangle:()=>ew,getFontFace:()=>eZ,getFontSize:()=>eV,getFontStyle:()=>eQ,getOffsetPolygon:()=>eL,getShapeStyle:()=>e2,getStickyFontSize:()=>eJ,getStickyFontStyle:()=>e0,getStickyShapeStyle:()=>e1,getStrokeWidth:()=>eX,getTextAlign:()=>eP,getTextLabelSize:()=>ez,getTextSvgElement:()=>eD,stickyFills:()=>eU,strokes:()=>eK,transformRectangle:()=>tl,transformSingleRectangle:()=>td});var ex={start:"left",middle:"center",end:"right",justify:"justify"};function eP(e="start"){return ex[e]}"undefined"!=typeof window&&(tW=function(){var e;null==(e=document.getElementById("__textLabelMeasure"))||e.remove();let t=document.createElement("pre");return t.id="__textLabelMeasure",Object.assign(t.style,{whiteSpace:"pre",width:"auto",border:"1px solid transparent",padding:"4px",margin:"0px",letterSpacing:eu,opacity:"0",position:"absolute",top:"-500px",left:"0px",zIndex:"9999",pointerEvents:"none",userSelect:"none",alignmentBaseline:"mathematical",dominantBaseline:"mathematical",lineHeight:1.3}),t.tabIndex=-1,document.body.appendChild(t),t}());var eC="",eA="",eE=[0,0];function eB(){eC=""}function ez(e,t){return e?tW?(tW.parent||document.body.appendChild(tW),e===eC&&t===eA)?eE:(eC=e,eA=t,tW.textContent=e,tW.style.font=t,eE=[tW.offsetWidth||1,tW.offsetHeight||1]):[10,10]:[16,32]}var eM=RegExp(`${[32,160,4961,65792,65793,4153,4241].map(e=>String.fromCodePoint(e)).join("|")}`);function eD(e,t,i,s,n,a=!1){let o="normal",r=function(e){let t=document.createElement("div");t.style.setProperty("position","absolute"),t.style.setProperty("top","-9999px"),t.style.setProperty("left","-9999px"),t.style.setProperty("width",e.width+"px"),t.style.setProperty("height","min-content"),t.style.setProperty("font-size",e.fontSize+"px"),t.style.setProperty("font-family",e.fontFamily),t.style.setProperty("font-weight",e.fontWeight),t.style.setProperty("line-height",e.lineHeight*e.fontSize+"px"),t.style.setProperty("letter-spacing",e.letterSpacing),t.style.setProperty("text-align",e.textAlign),document.body.appendChild(t);let i=e.text.split(eM).flatMap(e=>e.replaceAll(`
`,` 
`)).join(" ").split(" ");t.innerText=i[0];let s=t.offsetHeight,n=[i[0]],a=[n];for(let e=1;e<i.length;e++){let o=i[e];t.innerText+=" "+o;let r=t.offsetHeight;r>s&&(s=r,n=[],a.push(n)),n.push(o)}return t.remove(),a.map(e=>e.join(" "))}({text:e,wrap:a,width:n,fontSize:t,fontWeight:o,fontFamily:i,fontStyle:"normal",textAlign:"left",letterSpacing:eu,lineHeight:1}),l=document.createElementNS("http://www.w3.org/2000/svg","text");l.setAttribute("font-size",t+"px"),l.setAttribute("font-family",i),l.setAttribute("font-weight",o),l.setAttribute("line-height",1.3*t+"px"),l.setAttribute("letter-spacing",eu),l.setAttribute("text-align",null!=s?s:"left"),l.setAttribute("dominant-baseline","mathematical"),l.setAttribute("alignment-baseline","mathematical");let d=r.map((e,i)=>{let s=document.createElementNS("http://www.w3.org/2000/svg","tspan");return s.textContent=e+`
`,s.setAttribute("y",1.3*t*(i+.5)+"px"),l.appendChild(s),s});switch(s){case"middle":l.setAttribute("text-align","center"),l.setAttribute("text-anchor","middle"),d.forEach(e=>e.setAttribute("x",4+n/2+""));break;case"end":l.setAttribute("text-align","right"),l.setAttribute("text-anchor","end"),d.forEach(e=>e.setAttribute("x",4+n+""));break;default:l.setAttribute("text-align","left"),l.setAttribute("text-anchor","start"),d.forEach(e=>e.setAttribute("x","4"))}return l}function ej({id:e,bounds:t,labelSize:i,offset:s,scale:a=1}){return n.createElement("defs",null,n.createElement("mask",{id:e+"_clip"},n.createElement("rect",{x:-100,y:-100,width:t.width+200,height:t.height+200,fill:"white"}),n.createElement("rect",{x:t.width/2-i[0]/2*a+((null==s?void 0:s[0])||0),y:t.height/2-i[1]/2*a+((null==s?void 0:s[1])||0),width:i[0]*a,height:i[1]*a,rx:4*a,ry:4*a,fill:"black",opacity:Math.max(a,.8)})))}var eT=2*Math.PI,eO=class{static inwardEdgeNormal(e){let t=h.A.sub(e[1],e[0]),i=h.A.len2(t);return[-t[0]/i,t[1]/i]}static outwardEdgeNormal(e){return h.A.neg(eO.inwardEdgeNormal(e))}static isReflexVertex(e,t){let i=e.length,s=e[(t+i-1)%i],n=e[t],a=e[(t+1)%i];return 0>eO.leftSide(s,a,n)}static getEdges(e){return e.map((t,i)=>[t,e[(i+1)%e.length]])}static edgesIntersection([e,t],[i,s]){let n=(s[1]-i[1])*(t[0]-e[0])-(s[0]-i[0])*(t[1]-e[1]);if(0==n)return null;let a=((s[0]-i[0])*(e[1]-i[1])-(s[1]-i[1])*(e[0]-i[0]))/n,o=((t[0]-e[0])*(e[1]-i[1])-(t[1]-e[1])*(e[0]-i[0]))/n;return a<0||o<0||a>1||o>1?null:[e[0]+a*(t[0]-e[0]),e[1]+a*(t[1]-e[1])]}static appendArc(e,t,i,s,n,a=!1){let o=[...e],r=Math.atan2(s[1]-t[1],s[0]-t[0]),l=Math.atan2(n[1]-t[1],n[0]-t[0]);r<0&&(r+=eT),l<0&&(l+=eT);let d=r>l?r-l:r+eT-l,h=(a?-d:eT-d)/5;o.push(s);for(let e=1;e<5;++e){let s=r+h*e;o.push([t[0]+Math.cos(s)*i,t[1]+Math.sin(s)*i])}return o.push(n),o}static createOffsetEdge(e,t){return e.map(e=>h.A.add(e,t))}static getOffsetPolygon(e,t=0){let i=eO.getEdges(e),s=i.map(e=>eO.createOffsetEdge(e,h.A.mul(eO.outwardEdgeNormal(e),t))),n=[];for(let e=0;e<s.length;e++){let a=s[e],o=s[(e+s.length-1)%s.length],r=eO.edgesIntersection(o,a);r?n.push(r):eO.appendArc(n,i[e][0],t,o[1],a[0],!1)}return n}static createPaddingPolygon(e,t=0){let i=e.map(e=>eO.createOffsetEdge(e,eO.inwardEdgeNormal(e))),s=[];for(let n=0;n<i.length;n++){let a=i[n],o=i[(n+i.length-1)%i.length],r=eO.edgesIntersection(o,a);r?s.push(r):eO.appendArc(s,e[n][0],t,o[1],a[0],!0)}return s}},eF=eO;function eL(e,t){if(e.length<3)throw Error("Polygon must have at least 3 points");let i=e.length;return e.map((t,s)=>[t,e[(s+1)%i]]).map(([e,i])=>{let s=h.A.mul(h.A.per(h.A.uni(h.A.sub(i,e))),t);return[h.A.add(e,s),h.A.add(i,s)]}).map((e,t,i)=>{let s=(0,p.M_)(e,i[(t+1)%i.length]);if(void 0===s)throw Error("Expected an intersection");return s})}B(eF,"leftSide",h.A.isLeft);var eR="#fafafa",eq="#343d45",eH={white:"#f0f1f3",lightGray:"#c6cbd1",gray:"#788492",black:"#1d1d1d",green:"#36b24d",cyan:"#0e98ad",blue:"#1c7ed6",indigo:"#4263eb",violet:"#7746f1",red:"#ff2133",orange:"#ff9433",yellow:"#ffc936"},eU={light:A(C({},Object.fromEntries(Object.entries(eH).map(([e,t])=>[e,s.Aq.lerpColor(t,eR,.45)]))),{white:"#ffffff",black:"#3d3d3d"}),dark:A(C({},Object.fromEntries(Object.entries(eH).map(([e,t])=>[e,s.Aq.lerpColor(s.Aq.lerpColor(t,"#999999",.3),eq,.4)]))),{white:"#1d1d1d",black:"#bbbbbb"})},eK={light:A(C({},eH),{white:"#1d1d1d"}),dark:A(C({},Object.fromEntries(Object.entries(eH).map(([e,t])=>[e,s.Aq.lerpColor(t,eq,.1)]))),{white:"#cecece",black:"#cecece"})},e_={light:A(C({},Object.fromEntries(Object.entries(eH).map(([e,t])=>[e,s.Aq.lerpColor(t,eR,.82)]))),{white:"#fefefe"}),dark:A(C({},Object.fromEntries(Object.entries(eH).map(([e,t])=>[e,s.Aq.lerpColor(t,eq,.82)]))),{white:"rgb(30,33,37)",black:"#1e1e1f"})},eN={small:2,medium:3.5,large:5},eW={small:28,medium:48,large:96,auto:"auto"},eY={script:'"Caveat Brush"',sans:'"Source Sans Pro"',serif:'"Crimson Pro"',mono:'"Source Code Pro"'},e$={script:1,sans:1,serif:1,mono:1},eG={small:24,medium:36,large:48,auto:"auto"};function eX(e){return eN[e]}function eV(e,t="script"){return eW[e]*e$[t]}function eZ(e="script"){return eY[e]}function eJ(e){return eG[e]}function eQ(e){let t=eV(e.size,e.font),i=eZ(e.font),{scale:s=1}=e;return`${t*s}px/1.3 ${i}`}function e0(e){let t=eG[e.size],i=eZ(e.font),{scale:s=1}=e;return`${t*s}px/1 ${i}`}function e1(e,t=!1){let{color:i}=e,s=t?"dark":"light",n="white"===i||"black"===i?"yellow":i;return{fill:eU[s][n],stroke:eK[s][n],color:t?"#1d1d1d":"#0d0d0d"}}function e2(e,t){let{color:i,size:s,isFilled:n}=e,a=eN[s],o=t?"dark":"light";return{stroke:eK[o][i],fill:n?e_[o][i]:"none",strokeWidth:a}}var e5={color:"black",size:"small",isFilled:!1,dash:"draw",scale:1},e3=A(C({},e5),{font:"script",textAlign:"middle"}),e4=class{static insertTextFirefox(e,t){e.setRangeText(t,e.selectionStart||0,e.selectionEnd||0,"end"),e.dispatchEvent(new InputEvent("input",{data:t,inputType:"insertText",isComposing:!1}))}static insert(e,t){let i=e.ownerDocument,s=i.activeElement;s!==e&&e.focus(),i.execCommand("insertText",!1,t)||e4.insertTextFirefox(e,t),s===i.body?e.blur():s instanceof HTMLElement&&s!==e&&s.focus()}static set(e,t){e.select(),e4.insert(e,t)}static getSelection(e){let{selectionStart:t,selectionEnd:i}=e;return e.value.slice(t||void 0,i||void 0)}static wrapSelection(e,t,i){let{selectionStart:s,selectionEnd:n}=e,a=e4.getSelection(e);e4.insert(e,t+a+(null!=i?i:t)),e.selectionStart=(s||0)+t.length,e.selectionEnd=(n||0)+t.length}static replace(e,t,i){let s=0;e.value.replace(t,(...t)=>{let n=s+t[t.length-2],a=t[0].length;e.selectionStart=n,e.selectionEnd=n+a;let o="string"==typeof i?i:i(...t);return e4.insert(e,o),e.selectionStart=n,s+=o.length-a,o})}static findLineEnd(e,t){let i=e.lastIndexOf(`
`,t-1)+1;return"	"!==e.charAt(i)?t:i+1}static indent(e){var t;let{selectionStart:i,selectionEnd:s,value:n}=e,a=n.slice(i,s),o=null==(t=/\n/g.exec(a))?void 0:t.length;if(o&&o>0){let t=n.lastIndexOf(`
`,i-1)+1,a=e.value.slice(t,s-1),o=a.replace(/^|\n/g,"$&  "),r=o.length-a.length;e.setSelectionRange(t,s-1),e4.insert(e,o),e.setSelectionRange(i+1,s+r)}else e4.insert(e,"  ")}static unindent(e){let{selectionStart:t,selectionEnd:i,value:s}=e,n=s.lastIndexOf(`
`,t-1)+1,a=e4.findLineEnd(s,i),o=e.value.slice(n,a),r=o.replace(/(^|\n)(\t| {1,2})/g,"$1"),l=o.length-r.length;e.setSelectionRange(n,a),e4.insert(e,r);let d=/\t| {1,2}/.exec(s.slice(n,t)),h=d?d[0].length:0,p=t-h;e.setSelectionRange(t-h,Math.max(p,i-l))}},e8=e=>e.stopPropagation();function e6(e){if(null===e)return e;if(e instanceof Date)return new Date(e.getTime());if("object"==typeof e)if("function"==typeof e[Symbol.iterator]){let t=[];if(e.length>0)for(let i of e)t.push(e6(i));return t}else{let t=Object.keys(e),i={};if(t.length>0)for(let s of t)i[s]=e6(e[s]);return i}return e}function e9(e,t=0,i=0){let[s,n]=e,a=[[s/2,0],[s,n],[0,n]];return t&&(a=eL(a,t)),i&&(a=a.map(e=>h.A.rotWith(e,[s/2,n/2],i))),a}function e7(e){let[t,i]=e,s=[[t/2,0],[t,i],[0,i]];return[(s[0][0]+s[1][0]+s[2][0])/3,(s[0][1]+s[1][1]+s[2][1])/3]}function te(e,t,i){let{strokeWidth:n}=e2(i),{points:a}=function(e,t,i){let[n,a]=t,o=s.Aq.rng(e),r=Array.from([,,,]).map(()=>[o()*i*.75,o()*i*.75]),l=[h.A.add([n/2,0],r[0]),h.A.add([n,a],r[1]),h.A.add([0,a],r[2])],d=Math.round(Math.abs(2*o()*3)),p=s.Aq.rotateArray([h.A.pointsBetween(l[0],l[1],32),h.A.pointsBetween(l[1],l[2],32),h.A.pointsBetween(l[2],l[0],32)],d);return{points:[...p.flat(),...p[0]]}}(e,t,n);return{points:a,options:{size:n,thinning:.65,streamline:.3,smoothing:1,simulatePressure:!1,last:!0}}}var tt=class{static getShapeUtil(e){return iv(e)}static getSelectedShapes(e,t){let i=tt.getPage(e,t);return tt.getSelectedIds(e,t).map(e=>i.shapes[e])}static screenToWorld(e,t){let i=tt.getPageState(e,e.appState.currentPageId).camera;return h.l.sub(h.l.div(t,i.zoom),i.point)}static getCameraZoom(e){return s.Aq.clamp(e,.1,5)}static getPage(e,t){return e.document.pages[t]}static getPageState(e,t){return e.document.pageStates[t]}static getSelectedIds(e,t){return tt.getPageState(e,t).selectedIds}static getShapes(e,t){return Object.values(tt.getPage(e,t).shapes)}static getCamera(e,t){return tt.getPageState(e,t).camera}static getShape(e,t,i){return tt.getPage(e,i).shapes[t]}static getCenter(e){return tt.getShapeUtil(e).getCenter(e)}static getBounds(e){return tt.getShapeUtil(e).getBounds(e)}static getRotatedBounds(e){return tt.getShapeUtil(e).getRotatedBounds(e)}static getSelectedBounds(e){return s.Aq.getCommonBounds(tt.getSelectedShapes(e,e.appState.currentPageId).map(e=>tt.getShapeUtil(e).getBounds(e)))}static getParentId(e,t,i){return tt.getShape(e,t,i).parentId}static getDocumentBranch(e,t,i){let s=tt.getShape(e,t,i);return void 0===s.children?[t]:[t,...s.children.flatMap(t=>tt.getDocumentBranch(e,t,i))]}static getSelectedBranchSnapshot(e,t,i){let n=tt.getPage(e,t),a=tt.getSelectedIds(e,t).flatMap(i=>tt.getDocumentBranch(e,i,t).map(e=>n.shapes[e])).filter(e=>!e.isLocked).map(s.Aq.deepClone);return void 0!==i?a.map(e=>C({id:e.id},i(e))):a}static getSelectedShapeSnapshot(e,t,i){let n=tt.getSelectedShapes(e,t).filter(e=>!e.isLocked).map(s.Aq.deepClone);return void 0!==i?n.map(e=>C({id:e.id},i(e))):n}static getAllEffectedShapeIds(e,t,i){let s=tt.getPage(e,i),n=new Set(t);return t.forEach(e=>{let t=s.shapes[e];!function e(t){void 0!==t.children&&t.children.filter(e=>!n.has(e)).forEach(t=>{n.add(t),e(s.shapes[t])})}(t),function e(t){let i=t.parentId;i!==s.id&&(n.has(i)||(n.add(i),e(s.shapes[i])))}(t),n.forEach(e=>{Object.values(s.bindings).filter(t=>t.fromId===e||t.toId===e).forEach(t=>n.add(t.fromId===e?t.toId:t.fromId))})}),Array.from(n.values())}static getLinkedShapeIds(e,t,i,s=!0){let n=tt.getSelectedIds(e,t),a=tt.getPage(e,t),o=new Set(n),r=new Set,l=[...n],d=new Set(Object.values(a.shapes).filter(e=>{var t;return"arrow"===e.type&&(e.handles.start.bindingId||(null==(t=e.handles)?void 0:t.end.bindingId))}));for(;l.length;){let e=l.pop();if(!(e&&d.size))break;r.has(e)||(r.add(e),d.forEach(t=>{var n,r;let{handles:{start:{bindingId:h},end:{bindingId:p}}}=t,c=h?a.bindings[h]:null,u=p?a.bindings[p]:null,g=!1;c&&c.toId===e?("center"===i||((null==(n=t.decorations)?void 0:n.start)&&u?"left"===i:"right"===i))&&(s&&o.add(t.id),o.add(e),u&&(o.add(u.toId),l.push(u.toId))):u&&u.toId===e&&("center"===i||((null==(r=t.decorations)?void 0:r.end)&&c?"left"===i:"right"===i))&&(s&&o.add(t.id),o.add(e),c&&(o.add(c.toId),l.push(c.toId))),(!c||o.has(c.toId))&&(!u||o.has(u.toId))&&d.delete(t)}))}return Array.from(o.values())}static getChildIndexAbove(e,t,i){let s=e.document.pages[i],n=s.shapes[t],a;if(n.parentId===s.id)a=Object.values(s.shapes).filter(e=>e.parentId===s.id).sort((e,t)=>e.childIndex-t.childIndex);else{let e=s.shapes[n.parentId];if(!e.children)throw Error("No children in parent!");a=e.children.map(e=>s.shapes[e]).sort((e,t)=>e.childIndex-t.childIndex)}let o=a.indexOf(n),r=a[o+1];return r?r.childIndex:n.childIndex+1}static getBeforeShape(e,t){return Object.fromEntries(Object.keys(t).map(t=>[t,e[t]]))}static mutateShapes(e,t,i,n,a=!1){let o={},r={};t.forEach((s,l)=>{let d=tt.getShape(e,s,n);if(d.isLocked)return;(null==d?void 0:d.type)==="group"&&(1===t.length||a)&&d.children.forEach((t,s)=>{let a=tt.getShape(e,t,n);if(a.isLocked)return;let l=i(a,s);l&&(o[t]=tt.getBeforeShape(a,l),r[t]=l)});let h=i(d,l);h&&(o[s]=tt.getBeforeShape(d,h),r[s]=h)});let l=s.Aq.deepMerge(e,{document:{pages:{[e.appState.currentPageId]:{shapes:r}}}});return{before:o,after:r,data:l}}static createShapes(e,t,i){return{before:{document:{pages:{[i]:{shapes:C({},Object.fromEntries(t.flatMap(t=>{let s=[[t.id,void 0]];if(t.parentId!==i){let n=tt.getShape(e,t.parentId,i);if(!n.children)throw Error("No children in parent!");s.push([n.id,{children:n.children}])}return s})))}}}},after:{document:{pages:{[i]:{shapes:{shapes:C({},Object.fromEntries(t.flatMap(t=>{let s=[[t.id,t]];if(t.parentId!==i){let n=tt.getShape(e,t.parentId,i);if(!n.children)throw Error("No children in parent!");s.push([n.id,{children:[...n.children,t.id]}])}return s})))}}}}}}}static deleteShapes(e,t,i){i=i||e.appState.currentPageId;let s=tt.getPage(e,i),n="string"==typeof t[0]?t:t.map(e=>e.id);return{before:{document:{pages:{[i]:{shapes:C({},Object.fromEntries(n.flatMap(e=>{let t=s.shapes[e],n=[[t.id,t]];if(t.parentId!==i){let e=s.shapes[t.parentId];if(!e.children)throw Error("No children in parent!");n.push([e.id,{children:e.children}])}return n}))),bindings:C({},Object.fromEntries(Object.values(s.bindings).filter(e=>n.includes(e.fromId)||n.includes(e.toId)).map(e=>[e.id,e])))}}}},after:{document:{pages:{[i]:{shapes:C({},Object.fromEntries(n.flatMap(e=>{let t=s.shapes[e],i=[[t.id,void 0]];if(t.parentId!==s.id){let e=s.shapes[t.parentId];if(!e.children)throw Error("No children in parent!");i.push([e.id,{children:e.children.filter(e=>e!==t.id)}])}return i})))}}}}}}static onSessionComplete(e){var t,i;let s=null==(i=(t=tt.getShapeUtil(e)).onSessionComplete)?void 0:i.call(t,e);return s?C(C({},e),s):e}static onChildrenChange(e,t,i){var s,n;if(!t.children)return;let a=null==(n=(s=tt.getShapeUtil(t)).onChildrenChange)?void 0:n.call(s,t,t.children.map(t=>tt.getShape(e,t,i)));return a?C(C({},t),a):t}static updateArrowBindings(e,t){var i,n,a,o,r,l;let d={start:e6(t.handles.start),end:e6(t.handles.end)},c={isBound:!1,handle:t.handles.start,point:h.l.add(t.handles.start.point,t.point)},u={isBound:!1,handle:t.handles.end,point:h.l.add(t.handles.end.point,t.point)};if(t.handles.start.bindingId){let n=(null==(i=t.decorations)?void 0:i.start)!==void 0,a=t.handles.start,o=e.bindings[t.handles.start.bindingId];if(!o)throw Error("Could not find a binding to match the start handle's bindingId");let r=e.shapes[o.toId],l=tt.getShapeUtil(r),d=l.getBounds(r),p=l.getExpandedBounds(r),u=n?s.Aq.expandBounds(d,o.distance):d,{minX:g,minY:m,width:f,height:v}=p,b=h.l.add([g,m],h.l.mulV([f,v],h.l.rotWith(o.point,[.5,.5],r.rotation||0)));c={isBound:!0,hasDecoration:n,binding:o,handle:a,point:b,util:l,target:r,bounds:d,expandedBounds:p,intersectBounds:u,center:l.getCenter(r)}}if(t.handles.end.bindingId){let i=(null==(n=t.decorations)?void 0:n.end)!==void 0,a=t.handles.end,o=e.bindings[t.handles.end.bindingId];if(!o)throw Error("Could not find a binding to match the end handle's bindingId");let r=e.shapes[o.toId],l=tt.getShapeUtil(r),d=l.getBounds(r),p=l.getExpandedBounds(r),c=i?s.Aq.expandBounds(d,o.distance):d,{minX:g,minY:m,width:f,height:v}=p,b=h.l.add([g,m],h.l.mulV([f,v],h.l.rotWith(o.point,[.5,.5],r.rotation||0)));u={isBound:!0,hasDecoration:i,binding:o,handle:a,point:b,util:l,target:r,bounds:d,expandedBounds:p,intersectBounds:c,center:l.getCenter(r)}}for(let e of["end","start"]){let i="start"===e?c:u,n="start"===e?u:c;if(i.isBound)if(i.binding.distance){let r=h.l.uni(h.l.sub(i.point,n.point));switch(i.target.type){case"ellipse":{let s=(0,p.MV)(n.point,r,i.center,i.target.radius[0]+(i.hasDecoration?i.binding.distance:0),i.target.radius[1]+(i.hasDecoration?i.binding.distance:0),i.target.rotation||0).points.sort((e,t)=>h.l.dist(e,n.point)-h.l.dist(t,n.point));void 0!==s[0]&&(d[e].point=h.l.toFixed(h.l.sub(s[0],t.point)));break}case"triangle":{let a=i.target.point,o=e9(i.target.size,16*!!i.hasDecoration,i.target.rotation).map(e=>h.l.add(e,a)),l=s.Aq.pointsToLineSegments(o,!0).map(([e,t])=>(0,p.zd)(n.point,r,e,t)).filter(e=>e.didIntersect).flatMap(e=>e.points).sort((e,t)=>h.l.dist(e,n.point)-h.l.dist(t,n.point));void 0!==l[0]&&(d[e].point=h.l.toFixed(h.l.sub(l[0],t.point)));break}default:{let l,c=(0,p.Rr)(n.point,r,i.intersectBounds,i.target.rotation).filter(e=>e.didIntersect).map(e=>e.points[0]).sort((e,t)=>h.l.dist(e,n.point)-h.l.dist(t,n.point));if(!c[0])continue;if(n.isBound&&(l=(0,p.Rr)(n.point,r,n.intersectBounds,n.target.rotation).filter(e=>e.didIntersect).map(e=>e.points[0]).sort((e,t)=>h.l.dist(e,n.point)-h.l.dist(t,n.point))[0]),n.isBound&&(c.length<2||l&&c[0]&&40>Math.ceil(h.l.dist(c[0],l))||s.Aq.boundsContain(i.expandedBounds,n.expandedBounds)||s.Aq.boundsCollide(i.expandedBounds,n.expandedBounds))){let a=h.l.uni(h.l.sub(n.point,i.point)),o=(0,p.Rr)(i.point,a,i.bounds,i.target.rotation).filter(e=>e.didIntersect).map(e=>e.points[0]);if(!o[0])continue;d[e].point=h.l.toFixed(h.l.sub(o[0],t.point)),d["start"===e?"end":"start"].point=h.l.toFixed(h.l.add(h.l.sub(o[0],t.point),h.l.mul(a,Math.min(h.l.dist(o[0],n.point),40*(s.Aq.boundsContain(n.bounds,i.intersectBounds)?-1:1)))))}else if(!n.isBound&&(c[0]&&40>h.l.dist(c[0],n.point)||s.Aq.pointInBounds(n.point,i.intersectBounds))){let s=h.l.uni(h.l.sub(i.center,n.point));return null==(o=(a=tt.getShapeUtil(t)).onHandleChange)?void 0:o.call(a,t,{[e]:A(C({},t.handles[e]),{point:h.l.toFixed(h.l.add(h.l.sub(n.point,t.point),h.l.mul(s,40)))})})}else c[0]&&(d[e].point=h.l.toFixed(h.l.sub(c[0],t.point)))}}}else d[e].point=h.l.sub(i.point,t.point)}return null==(l=(r=tt.getShapeUtil(t)).onHandleChange)?void 0:l.call(r,t,d)}static transform(e,t,i){let s=tt.getShapeUtil(e).transform(e,t,i);return s?C(C({},e),s):e}static transformSingle(e,t,i){let s=tt.getShapeUtil(e).transformSingle(e,t,i);return s?C(C({},e),s):e}static getRotatedShapeMutation(e,t,i,n){var a,o;let r=h.l.sub(t,e.point),l=h.l.rotWith(t,i,n),d=h.l.toFixed(h.l.sub(l,r));return void 0!==e.handles?null==(o=(a=this.getShapeUtil(e)).onHandleChange)?void 0:o.call(a,A(C({},e),{point:d}),Object.fromEntries(Object.entries(e.handles).map(([e,t])=>{let i=h.l.toFixed(h.l.rotWith(t.point,r,n));return[e,A(C({},t),{point:i})]}))):{point:d,rotation:s.Aq.clampRadians((e.rotation||0)+n)}}static updateParents(e,t,i){let s=tt.getPage(e,t);if(0===i.length)return;let{shapes:n}=tt.getPage(e,t),a=Array.from(new Set(i.map(e=>n[e].parentId).values())).filter(e=>e!==s.id);for(let i of a){let s=n[i];if(!s.children)throw Error("A shape is parented to a shape without a children array.");tt.onChildrenChange(e,s,t)}tt.updateParents(e,t,a)}static getBinding(e,t,i){return tt.getPage(e,i).bindings[t]}static getBindings(e,t){return Object.values(tt.getPage(e,t).bindings)}static getBindableShapeIds(e){return tt.getShapes(e,e.appState.currentPageId).filter(e=>tt.getShapeUtil(e).canBind).sort((e,t)=>t.childIndex-e.childIndex).map(e=>e.id)}static getBindingsWithShapeIds(e,t,i){return Array.from(new Set(tt.getBindings(e,i).filter(e=>t.includes(e.toId)||t.includes(e.fromId))).values())}static getRelatedBindings(e,t,i){let s=new Set(t),n=Object.values(tt.getPage(e,i).bindings),a=new Set(n.filter(e=>s.has(e.toId)||s.has(e.fromId))),o=a.size,r=-1;for(;0!==r;)a.forEach(e=>{let t=e.fromId;for(let e of n)e.fromId===t&&a.add(e),e.toId===t&&a.add(e)}),r=a.size-o,o=a.size;return Array.from(a.values())}static normalizeText(e){return e.replace(tt.fixNewLines,`
`).split(`
`).map(e=>e||" ").join(`
`)}static assertShapeHasProperty(e,t){if(void 0===e[t])throw Error()}static warn(e){}static error(e){}static getSvgString(e,t=1){let i=e.cloneNode(!0);return e.setAttribute("width",e.getAttribute("width")*t+""),e.setAttribute("height",e.getAttribute("height")*t+""),new XMLSerializer().serializeToString(i).replaceAll("&#10;      ","").replaceAll(/((\s|")[0-9]*\.[0-9]{2})([0-9]*)(\b|"|\))/g,"$1")}static getSvgAsDataUrl(e,t=1){let i=tt.getSvgString(e,t);return`data:image/svg+xml;base64,${window.btoa(unescape(i))}`}static getImageForSvg(e){return z(this,arguments,function*(e,t="png",i={}){let{scale:s=2,quality:n=1}=i,a=tt.getSvgString(e,s),o=+e.getAttribute("width"),r=+e.getAttribute("height");if(!a)return;let l=yield new Promise(e=>{let t=new Image;t.crossOrigin="anonymous";let i=`data:image/svg+xml;base64,${window.btoa(unescape(encodeURIComponent(a)))}`;t.onload=()=>{let s=document.createElement("canvas"),n=s.getContext("2d");s.width=o,s.height=r,n.drawImage(t,0,0,o,r),URL.revokeObjectURL(i),e(s)},t.onerror=()=>{console.warn("Could not convert that SVG to an image.")},t.src=i});return yield new Promise(e=>l.toBlob(t=>e(t),"image/"+t,n))})}},ti=tt;B(ti,"copyStringToClipboard",e=>{try{navigator.clipboard&&navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([e],{type:"text/plain"})})])}catch(i){let t=document.createElement("textarea");t.setAttribute("position","fixed"),t.setAttribute("top","0"),t.setAttribute("readonly","true"),t.setAttribute("contenteditable","true"),t.style.position="fixed",t.value=e,document.body.appendChild(t),t.focus(),t.select();try{let e=document.createRange();e.selectNodeContents(t);let i=window.getSelection();i&&(i.removeAllRanges(),i.addRange(e),t.setSelectionRange(0,t.value.length)),document.execCommand("copy")}catch(e){}finally{document.body.removeChild(t)}}}),B(ti,"flattenShape",(e,t)=>{var i;return[t,...(null!=(i=t.children)?i:[]).map(t=>tt.getShape(e,t,e.appState.currentPageId)).sort((e,t)=>e.childIndex-t.childIndex).flatMap(t=>tt.flattenShape(e,t))]}),B(ti,"flattenPage",(e,t)=>Object.values(e.document.pages[t].shapes).sort((e,t)=>e.childIndex-t.childIndex).reduce((t,i)=>[...t,...tt.flattenShape(e,i)],[])),B(ti,"getTopChildIndex",(e,t)=>{let i=tt.getShapes(e,t);return 0===i.length?1:i.filter(e=>e.parentId===t).sort((e,t)=>t.childIndex-e.childIndex)[0].childIndex+1}),B(ti,"fixNewLines",/\r?\n|\r/g);var ts=n.memo(function({font:e,text:t,color:i,offsetX:s=0,offsetY:a=0,scale:o=1,isEditing:r=!1,onBlur:l,onChange:d,shape:h}){let p=n.useRef(null),c=n.useRef(!1),u=n.useCallback(e=>{d(ti.normalizeText(e.currentTarget.value))},[d]),g=n.useCallback(e=>{if("Escape"===e.key){e.preventDefault(),e.stopPropagation(),null==l||l();return}if("Tab"===e.key&&0===t.length)return void e.preventDefault();if("Meta"===e.key||e.metaKey){if("z"===e.key&&e.metaKey){e.shiftKey?document.execCommand("redo",!1):document.execCommand("undo",!1),e.stopPropagation(),e.preventDefault();return}}else e.stopPropagation();(e.metaKey||e.ctrlKey)&&"="===e.key&&e.preventDefault(),"Tab"===e.key&&(e.preventDefault(),e.shiftKey?e4.unindent(e.currentTarget):e4.indent(e.currentTarget),null==d||d(ti.normalizeText(e.currentTarget.value)))},[d]),m=n.useCallback(e=>{e.currentTarget.setSelectionRange(0,0),null==l||l()},[l]),f=n.useCallback(e=>{r&&c.current&&document.activeElement===e.currentTarget&&e.currentTarget.select()},[r]),v=n.useCallback(e=>{r&&e.stopPropagation()},[r]);n.useEffect(()=>{r?requestAnimationFrame(()=>{c.current=!0;let e=p.current;e&&(e.focus(),e.select())}):null==l||l()},[r,l]);let b=n.useRef(null);return n.useLayoutEffect(()=>{let i=b.current;if(!i)return;let n=ez(t,e);i.style.transform=`scale(${o}, ${o}) translate(${s}px, ${a}px)`,i.style.width=n[0]+1+"px",i.style.height=n[1]+1+"px"},[t,e,a,s,o]),n.createElement(tn,null,n.createElement(to,{ref:b,hasText:!!t,isEditing:r,style:{font:e,color:i},"data-color":null==h?void 0:h.style.color},r?n.createElement(tr,{ref:p,style:{font:e,color:i},name:"text",tabIndex:-1,autoComplete:"false",autoCapitalize:"false",autoCorrect:"false",autoSave:"false",autoFocus:!0,placeholder:"",spellCheck:"true",wrap:"off",dir:"auto",datatype:"wysiwyg",defaultValue:t,color:i,onFocus:f,onChange:u,onKeyDown:g,onBlur:m,onPointerDown:v,onContextMenu:e8,onCopy:e8,onPaste:e8,onCut:e8}):t,"​"))}),tn=er("div",{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",userSelect:"none",variants:{isGhost:{false:{opacity:1},true:{transition:"opacity .2s",opacity:.3}}}}),ta={whiteSpace:"pre-wrap",overflowWrap:"break-word",letterSpacing:eu},to=er("div",C({position:"absolute",padding:"4px",zIndex:1,minHeight:1,minWidth:1,lineHeight:1,outline:0,fontWeight:"500",textAlign:"center",backfaceVisibility:"hidden",userSelect:"none",WebkitUserSelect:"none",WebkitTouchCallout:"none",variants:{hasText:{false:{pointerEvents:"none"},true:{pointerEvents:"all"}},isEditing:{false:{userSelect:"none"},true:{background:"$boundsBg",userSelect:"text",WebkitUserSelect:"text"}}}},ta)),tr=er("textarea",A(C({position:"absolute",top:0,left:0,zIndex:1,width:"100%",height:"100%",border:"none",padding:"4px",resize:"none",textAlign:"inherit",minHeight:"inherit",minWidth:"inherit",lineHeight:"inherit",outline:0,fontWeight:"inherit",overflow:"hidden",backfaceVisibility:"hidden",display:"inline-block",pointerEvents:"all",background:"$boundsBg",userSelect:"text",WebkitUserSelect:"text",fontSmooth:"always",WebkitFontSmoothing:"subpixel-antialiased",MozOsxFontSmoothing:"auto"},ta),{"&:focus":{outline:"none",border:"none"}}));function tl(e,t,{initialShape:i,transformOrigin:s,scaleX:n,scaleY:a}){return e.rotation||i.isAspectRatioLocked?{size:h.A.toFixed(h.A.mul(i.size,Math.min(Math.abs(n),Math.abs(a)))),point:h.A.toFixed([t.minX+(t.width-e.size[0])*(n<0?1-s[0]:s[0]),t.minY+(t.height-e.size[1])*(a<0?1-s[1]:s[1])]),rotation:n<0&&a>=0||a<0&&n>=0?i.rotation?-i.rotation:0:i.rotation}:{point:h.A.toFixed([t.minX,t.minY]),size:h.A.toFixed([t.width,t.height])}}function td(e,t){return{size:h.A.toFixed([t.width,t.height]),point:h.A.toFixed([t.minX,t.minY])}}var th=class extends s.EO{constructor(){super(...arguments),B(this,"canBind",!1),B(this,"canEdit",!1),B(this,"canClone",!1),B(this,"isAspectRatioLocked",!1),B(this,"hideResizeHandles",!1),B(this,"bindingDistance",16),B(this,"hitTestPoint",(e,t)=>s.Aq.pointInBounds(t,this.getRotatedBounds(e))),B(this,"hitTestLineSegment",(e,t,i)=>{let n=s.Aq.getBoundsFromPoints([t,i]),a=this.getBounds(e);return s.Aq.boundsContain(a,n)||e.rotation?(0,p.NC)(t,i,s.Aq.getRotatedCorners(this.getBounds(e))).didIntersect:(0,p.Oy)(t,i,this.getBounds(e)).length>0}),B(this,"create",e=>(this.refMap.set(e.id,n.createRef()),this.getShape(e))),B(this,"getCenter",e=>s.Aq.getBoundsCenter(this.getBounds(e))),B(this,"getExpandedBounds",e=>s.Aq.expandBounds(this.getBounds(e),this.bindingDistance)),B(this,"getBindingPoint",(e,t,i,n,a,o)=>{let r=this.getBounds(e),l=this.getExpandedBounds(e);if(!s.Aq.pointInBounds(i,l))return;let d=(0,p.Rr)(n,a,l).filter(e=>e.didIntersect).map(e=>e.points[0]);if(!d.length)return;let c=this.getCenter(e),u=d.sort((e,t)=>h.l.dist(t,n)-h.l.dist(e,n))[0],g=h.l.med(i,u),m,f;o?(m=8>h.l.dist(i,c)?c:i,f=0):(m=8>h.l.distanceToLineSegment(i,g,c)?c:g,f=s.Aq.pointInBounds(i,r)?this.bindingDistance:Math.max(this.bindingDistance,s.Aq.getBoundsSides(r).map(e=>h.l.distanceToLineSegment(e[1][0],e[1][1],i)).sort((e,t)=>e-t)[0]));let v=h.l.divV(h.l.sub(m,[l.minX,l.minY]),[l.width,l.height]);return{point:h.l.clampV(v,0,1),distance:f}}),B(this,"mutate",(e,t)=>t),B(this,"transform",(e,t,i)=>A(C({},e),{point:[t.minX,t.minY]})),B(this,"transformSingle",(e,t,i)=>this.transform(e,t,i)),B(this,"updateChildren"),B(this,"onChildrenChange"),B(this,"onHandleChange"),B(this,"onRightPointHandle"),B(this,"onDoubleClickHandle"),B(this,"onDoubleClickBoundsHandle"),B(this,"onSessionComplete"),B(this,"getSvgElement",(e,t)=>{var i,s,n,a,o;let r=null==(i=document.getElementById(e.id+"_svg"))?void 0:i.cloneNode(!0);if(r){if(null!=(a=null==(n=null==(s=e.label)?void 0:s.trim())?void 0:n.length)&&a){let i=document.createElementNS("http://www.w3.org/2000/svg","g"),s=eQ(e.style),n=ez(e.label,s),a=eV(e.style.size,e.style.font)*(null!=(o=e.style.scale)?o:1),l=eZ(e.style.font).slice(1,-1),d=eD(e.label,a,l,"middle",n[0],!1),h=this.getBounds(e);return d.setAttribute("transform",`translate(${h.width/2-n[0]/2}, ${h.height/2-n[1]/2})`),d.setAttribute("fill",e2(e.style,t).stroke),d.setAttribute("data-color",e.style.color),d.setAttribute("transform-origin","center center"),i.setAttribute("text-align","center"),i.setAttribute("text-anchor","middle"),i.appendChild(r),i.appendChild(d),i}return r}})}};function tp(e,t){let{start:i,end:s}=e,n=h.A.dist(i.point,s.point),a=h.A.med(i.point,s.point),o=n/2*t,r=h.A.uni(h.A.vec(i.point,s.point));return h.A.toFixed(10>Math.abs(o)?a:h.A.add(a,h.A.mul(h.A.per(r),o)))}function tc(e,t,i){return s.Aq.circleFromThreePoints(e,i,t)}function tu(e,t,i,s,n){let a=(0,p.Op)(e,.618*t,i,s).points;if(!a)return ti.warn("Could not find an intersection for the arrow head."),{left:e,right:e};let o=n?a[0]:a[1];return{left:o?h.A.nudge(h.A.rotWith(o,e,Math.PI/6),e,-.382*t):e,right:o?h.A.nudge(h.A.rotWith(o,e,-Math.PI/6),e,-.382*t):e}}function tg(e,t,i){let s=(0,p.NO)(e,i,e,t).points;if(!s)return ti.warn("Could not find an intersection for the arrow head."),{left:e,right:e};let n=s[0];return{left:n?h.A.rotWith(n,e,Math.PI/6):e,right:n?h.A.rotWith(n,e,-Math.PI/6):e}}function tm(e,t,i,s,n){let{left:a,right:o}=tu(e,t,i,s,n);return`M ${a} L ${e} ${o}`}function tf(e,t,i){let{left:s,right:n}=tg(e,t,i);return`M ${s} L ${e} ${n}`}function tv(e,t,i){if(4>=h.A.dist2(t,h.A.med(e,i)))return[e,i];let n=[],a=tc(e,t,i),o=[a[0],a[1]],r=a[2],l=h.A.angle(o,e),d=h.A.angle(o,i);for(let e=.05;e<1;e+=.05){let t=s.Aq.lerpAngles(l,d,e);n.push(h.A.nudgeAtAngle(o,t,r))}return n}function tb(e,t,i,n){return 2*Math.PI*t*(s.Aq.getSweep(e,i,n)/(2*Math.PI))}function ty({left:e,middle:t,right:i,stroke:s,strokeWidth:a}){return n.createElement("g",null,n.createElement("path",{className:"tl-stroke-hitarea",d:`M ${e} L ${t} ${i}`}),n.createElement("path",{d:`M ${e} L ${t} ${i}`,fill:"none",stroke:s,strokeWidth:a,strokeLinecap:"round",strokeLinejoin:"round",pointerEvents:"none"}))}var tS=n.memo(function({id:e,style:t,start:i,bend:a,end:o,arrowBend:r,decorationStart:l,decorationEnd:d,isDraw:p,isDarkMode:u}){let g=h.A.dist(i,o);if(g<2)return null;let m=e2(t,u),{strokeWidth:f}=m,v=1+1.618*f,b=tc(i,a,o),y=[b[0],b[1]],S=b[2],I=tb(y,S,i,o),k=ef[s.Aq.rng(e)()>0?"easeInOutSine":"easeInOutCubic"],w=p?function(e,t,i,n,a,o,r,l,d,p){let u=s.Aq.rng(e),g=e2(t).strokeWidth,m=a?h.A.rotWith(i,r,g/d):i,f=o?h.A.rotWith(n,r,-(g/d)):n,v=h.A.angle(r,m),b=h.A.angle(r,f),y=[],S=8+Math.floor(Math.abs(d)/20*1+u()/2);for(let e=0;e<S;e++){let t=p(e/S),i=s.Aq.lerpAngles(v,b,t);y.push(h.A.toFixed(h.A.nudgeAtAngle(r,i,l)))}let I=(0,c.Ay)([m,...y,f],{size:1+g,thinning:.618+.2*u(),easing:ef.easeOutQuad,simulatePressure:!1,streamline:0,last:!0});return s.Aq.getSvgPathFromStroke(I)}(e,t,i,o,l,d,y,S,I,k):["M",i[0],i[1],"A",b[2],b[2],0,0,r<0?0:1,o[0],o[1]].join(" "),{strokeDasharray:x,strokeDashoffset:P}=s.Aq.getPerfectDashProps(Math.abs(I),v,t.dash,2,!1),C=Math.min(g/3,8*f),A=l?tu(i,C,y,S,I<0):null,E=d?tu(o,C,y,S,I>=0):null;return n.createElement(n.Fragment,null,n.createElement("path",{className:"tl-stroke-hitarea",d:w}),n.createElement("path",{d:w,fill:p?m.stroke:"none",stroke:m.stroke,strokeWidth:p?0:v,strokeDasharray:x,strokeDashoffset:P,strokeLinecap:"round",strokeLinejoin:"round",pointerEvents:"none"}),A&&n.createElement(ty,{left:A.left,middle:i,right:A.right,stroke:m.stroke,strokeWidth:v}),E&&n.createElement(ty,{left:E.left,middle:o,right:E.right,stroke:m.stroke,strokeWidth:v}))}),tI=n.memo(function({id:e,style:t,start:i,end:a,decorationStart:o,decorationEnd:r,isDraw:l,isDarkMode:d}){let p,u,g,m,f,v=h.A.dist(i,a);if(v<2)return null;let b=e2(t,d),{strokeWidth:y}=b,S=1+1.618*y,I=l?(p=s.Aq.rng(e),u=e2(t).strokeWidth,g=o?h.A.nudge(i,a,u):i,m=r?h.A.nudge(a,i,u):a,f=(0,c.Ay)([g,m],{size:u,thinning:.618+.2*p(),easing:ef.easeOutQuad,simulatePressure:!0,streamline:0,last:!0}),s.Aq.getSvgPathFromStroke(f)):"M"+h.A.toFixed(i)+"L"+h.A.toFixed(a),{strokeDasharray:k,strokeDashoffset:w}=s.Aq.getPerfectDashProps(v,1.618*y,t.dash,2,!1),x=Math.min(v/3,8*y),P=o?tg(i,a,x):null,C=r?tg(a,i,x):null;return n.createElement(n.Fragment,null,n.createElement("path",{className:"tl-stroke-hitarea",d:I}),n.createElement("path",{d:I,fill:b.stroke,stroke:b.stroke,strokeWidth:l?S/2:S,strokeDasharray:k,strokeDashoffset:w,strokeLinecap:"round",strokeLinejoin:"round",pointerEvents:"stroke"}),P&&n.createElement(ty,{left:P.left,middle:i,right:P.right,stroke:b.stroke,strokeWidth:S}),C&&n.createElement(ty,{left:C.left,middle:a,right:C.right,stroke:b.stroke,strokeWidth:S}))}),tk=class extends th{constructor(){super(...arguments),B(this,"type","arrow"),B(this,"hideBounds",!0),B(this,"canEdit",!0),B(this,"pathCache",new WeakMap),B(this,"getShape",e=>{var t,i,s,n;return C({id:"id",type:"arrow",name:"Arrow",parentId:"page",childIndex:1,point:[0,0],rotation:0,bend:0,handles:{start:C({id:"start",index:0,point:[0,0],canBind:!0},null==(t=e.handles)?void 0:t.start),end:C({id:"end",index:1,point:[1,1],canBind:!0},null==(i=e.handles)?void 0:i.end),bend:C({id:"bend",index:2,point:[.5,.5]},null==(s=e.handles)?void 0:s.bend)},decorations:null!=(n=e.decorations)?n:{end:"arrow"},style:C(A(C({},e5),{isFilled:!1}),e.style),label:"",labelPoint:[.5,.5]},e)}),B(this,"Component",th.Component(({shape:e,isEditing:t,isGhost:i,meta:a,events:o,onShapeChange:r,onShapeBlur:l},d)=>{var p,c;let{id:u,label:g="",handles:{start:m,bend:f,end:v},decorations:b={},style:y}=e,S=null!=(c=null==(p=null==g?void 0:g.trim())?void 0:p.length)&&c,I=1>h.l.dist(f.point,h.l.toFixed(h.l.med(m.point,v.point))),k=eQ(y),w=e2(y,a.isDarkMode),x=S||t?ez(g,k):[0,0],P=this.getBounds(e),A=n.useMemo(()=>{let{start:t,bend:i,end:s}=e.handles;if(I)return h.l.dist(t.point,s.point);let n=tc(t.point,i.point,s.point);return Math.abs(tb(n.slice(0,2),n[2],t.point,s.point))},[e.handles]),E=Math.max(.5,Math.min(1,Math.max(A/(x[1]+128),A/(x[0]+128)))),B=n.useMemo(()=>{let t=this.getBounds(e);return h.l.sub(e.handles.bend.point,h.l.toFixed([t.width/2,t.height/2]))},[e,E]),z=n.useCallback(e=>{null==r||r({id:u,label:e})},[r]);return n.createElement(tw,C({ref:d},o),n.createElement(ts,{font:k,text:g,color:w.stroke,offsetX:B[0],offsetY:B[1],scale:E,isEditing:t,onChange:z,onBlur:l,shape:e}),n.createElement(s.UL,{id:e.id+"_svg",shapeStyle:y},n.createElement("defs",null,n.createElement("mask",{id:e.id+"_clip"},n.createElement("rect",{x:-100,y:-100,width:P.width+200,height:P.height+200,fill:"white"}),n.createElement("rect",{x:P.width/2-x[0]/2*E+B[0],y:P.height/2-x[1]/2*E+B[1],width:x[0]*E,height:x[1]*E,rx:4*E,ry:4*E,fill:"black",opacity:1}))),n.createElement("g",{pointerEvents:"none",opacity:i?.3:1,mask:S||t?`url(#${e.id}_clip)`:""},n.createElement(I?tI:tS,{id:u,style:y,start:m.point,end:v.point,bend:f.point,arrowBend:e.bend,decorationStart:null==b?void 0:b.start,decorationEnd:null==b?void 0:b.end,isDraw:"draw"===y.dash,isDarkMode:a.isDarkMode}))))})),B(this,"Indicator",th.Indicator(({shape:e,bounds:t})=>{var i,s;let{style:a,decorations:o,label:r,handles:{start:l,bend:d,end:p}}=e,c=null!=(s=null==(i=null==r?void 0:r.trim())?void 0:i.length)&&s,u=eQ(a),g=c?ez(r,u):[0,0],m=1>h.l.dist(d.point,h.l.toFixed(h.l.med(l.point,p.point))),f=n.useMemo(()=>{let{start:t,bend:i,end:s}=e.handles;if(m)return h.l.dist(t.point,s.point);let n=tc(t.point,i.point,s.point);return Math.abs(tb(n.slice(0,2),n[2],t.point,s.point))},[e.handles]),v=Math.max(.5,Math.min(1,Math.max(f/(g[1]+128),f/(g[0]+128)))),b=n.useMemo(()=>{let t=this.getBounds(e);return h.l.sub(e.handles.bend.point,[t.width/2,t.height/2])},[e,v]);return n.createElement(n.Fragment,null,c&&n.createElement(ej,{id:e.id,scale:v,offset:b,bounds:t,labelSize:g}),n.createElement("path",{d:function(e,t,i,s,n,a){let{strokeWidth:o}=e2(e,!1),r=Math.min(h.A.dist(t,s)/3,8*o),l=[];if(1>h.A.dist(i,h.A.toFixed(h.A.med(t,s))))l.push(`M ${t} L ${s}`),n&&l.push(tf(t,s,r)),a&&l.push(tf(s,t,r));else{let e=tc(t,i,s),o=[e[0],e[1]],d=e[2],h=tb(o,d,t,s);l.push(`M ${t} A ${d} ${d} 0 0 ${h>0?"1":"0"} ${s}`),n&&l.push(tm(t,r,o,d,h<0)),a&&l.push(tm(s,r,o,d,h>=0))}return l.join(" ")}(a,l.point,d.point,p.point,null==o?void 0:o.start,null==o?void 0:o.end),mask:c?`url(#${e.id}_clip)`:""}),c&&n.createElement("rect",{x:t.width/2-g[0]/2*v+b[0],y:t.height/2-g[1]/2*v+b[1],width:g[0]*v,height:g[1]*v,rx:4*v,ry:4*v,fill:"transparent"}))})),B(this,"getBounds",e=>{let t=s.Aq.getFromCache(this.boundsCache,e,()=>{let{handles:{start:t,bend:i,end:n}}=e;return s.Aq.getBoundsFromPoints(tv(t.point,i.point,n.point))});return s.Aq.translateBounds(t,e.point)}),B(this,"getRotatedBounds",e=>{let{handles:{start:t,bend:i,end:n}}=e,a=tv(t.point,i.point,n.point),{minX:o,minY:r,maxX:l,maxY:d}=s.Aq.getBoundsFromPoints(a);return 0!==e.rotation&&(a=a.map(t=>h.l.rotWith(t,[(o+l)/2,(r+d)/2],e.rotation||0))),s.Aq.translateBounds(s.Aq.getBoundsFromPoints(a),e.point)}),B(this,"getCenter",e=>{let{start:t,end:i}=e.handles;return h.l.add(e.point,h.l.med(t.point,i.point))}),B(this,"shouldRender",(e,t)=>t.decorations!==e.decorations||t.handles!==e.handles||t.style!==e.style||t.label!==e.label),B(this,"hitTestPoint",(e,t)=>{let{handles:{start:i,bend:s,end:n}}=e,a=h.l.sub(t,e.point),o=tv(i.point,s.point,n.point);for(let e=1;e<o.length;e++)if(1>h.l.distanceToLineSegment(o[e-1],o[e],a))return!0;return!1}),B(this,"hitTestLineSegment",(e,t,i)=>{let{handles:{start:s,bend:n,end:a}}=e,o=h.l.sub(t,e.point),r=h.l.sub(i,e.point),l=tv(s.point,n.point,a.point);for(let e=1;e<l.length;e++)if((0,p._o)(l[e-1],l[e],o,r).didIntersect)return!0;return!1}),B(this,"hitTestBounds",(e,t)=>{let{start:i,end:n,bend:a}=e.handles,o=h.l.add(e.point,i.point),r=h.l.add(e.point,n.point);if(s.Aq.pointInBounds(o,t)||s.Aq.pointInBounds(r,t))return!0;if(h.l.isEqual(h.l.med(i.point,n.point),a.point))return(0,p.Oy)(o,r,t).length>0;{let[s,l,d]=tc(i.point,a.point,n.point),c=h.l.add(e.point,[s,l]);return(0,p.jy)(c,d,o,r,t).length>0}}),B(this,"transform",(e,t,{initialShape:i,scaleX:s,scaleY:n})=>{let a=this.getBounds(i),o=C({},i.handles);["start","end"].forEach(e=>{let[i,r]=o[e].point,l=i/a.width,d=r/a.height;o[e]=A(C({},o[e]),{point:[t.width*(s<0?1-l:l),t.height*(n<0?1-d:d)]})});let{start:r,bend:l,end:d}=o,p=h.l.dist(r.point,d.point),c=h.l.med(r.point,d.point),u=p/2*i.bend,g=h.l.uni(h.l.vec(r.point,d.point)),m=h.l.add(c,h.l.mul(h.l.per(g),u));return o.bend=A(C({},l),{point:h.l.toFixed(10>Math.abs(u)?c:m)}),{point:h.l.toFixed([t.minX,t.minY]),handles:o}}),B(this,"onDoubleClickHandle",(e,t)=>{var i,s;switch(t){case"bend":return{bend:0,handles:A(C({},e.handles),{bend:A(C({},e.handles.bend),{point:tp(e.handles,e.bend)})})};case"start":return{decorations:A(C({},e.decorations),{start:(null==(i=e.decorations)?void 0:i.start)?void 0:"arrow"})};case"end":return{decorations:A(C({},e.decorations),{end:(null==(s=e.decorations)?void 0:s.end)?void 0:"arrow"})}}return this}),B(this,"onHandleChange",(e,t)=>{let i=s.Aq.deepMerge(e.handles,t),n=e.bend;if(i=s.Aq.deepMerge(i,{start:{point:h.l.toFixed(i.start.point)},end:{point:h.l.toFixed(i.end.point)}}),h.l.isEqual(i.start.point,i.end.point))return;if("bend"in t){let{start:e,end:t,bend:a}=i,o=h.l.dist(e.point,t.point),r=h.l.med(e.point,t.point),l=h.l.angle(e.point,t.point),d=h.l.uni(h.l.vec(e.point,t.point)),p=h.l.add(r,h.l.mul(h.l.per(d),o)),c=h.l.sub(r,h.l.mul(h.l.per(d),o)),u=h.l.nearestPointOnLineSegment(p,c,a.point,!0),g=h.l.dist(r,u)/(o/2);n=s.Aq.clamp(g,-.99,.99);let m=h.l.angle(e.point,u);h.l.isEqual(r,tp(i,n))?n=0:function(e,t,i){if(i===e||i===t)return!0;let s=2*Math.PI,n=(t-e+s)%s;return n<=Math.PI!=(i-e+s)%s>n}(l,l+Math.PI,m)&&(n*=-1)}let a={point:e.point,bend:n,handles:A(C({},i),{bend:A(C({},i.bend),{point:tp(i,n)})})},o=e.point,r=this.getBounds(C({},a)),l=h.l.sub([r.minX,r.minY],o);return h.l.isEqual(l,[0,0])||(Object.values(a.handles).forEach(e=>{e.point=h.l.toFixed(h.l.sub(e.point,l))}),a.point=h.l.toFixed(h.l.add(a.point,l))),a}),B(this,"getSvgElement",(e,t)=>{var i,s,n,a,o;let r=null==(i=document.getElementById(e.id+"_svg"))?void 0:i.cloneNode(!0);if(r){if(null!=(a=null==(n=null==(s=e.label)?void 0:s.trim())?void 0:n.length)&&a){let i=document.createElementNS("http://www.w3.org/2000/svg","g"),s=eQ(e.style),n=ez(e.label,s),a=eV(e.style.size,e.style.font)*(null!=(o=e.style.scale)?o:1),l=eZ(e.style.font).slice(1,-1),d=eD(e.label,a,l,"start",n[0],!1),p,{start:c,bend:u,end:g}=e.handles;if(1>h.l.dist(u.point,h.l.toFixed(h.l.med(c.point,g.point))))p=h.l.dist(c.point,g.point);else{let e=tc(c.point,u.point,g.point);p=Math.abs(tb(e.slice(0,2),e[2],c.point,g.point))}let m=Math.max(.5,Math.min(1,Math.max(p/(n[1]+128),p/(n[0]+128)))),f=this.getBounds(e),v=h.l.sub(e.handles.bend.point,[f.width/2,f.height/2]),b=f.width/2-n[0]/2*m+v[0],y=f.height/2-n[1]/2*m+v[1];return d.setAttribute("transform",`translate(${b}, ${y})`),d.setAttribute("fill",e2(e.style,t).stroke),d.setAttribute("transform-origin","center center"),i.setAttribute("text-align","center"),i.setAttribute("text-anchor","middle"),i.appendChild(r),i.appendChild(d),i}return r}})}},tw=er("div",{width:"100%",height:"100%"}),tx={easing:e=>Math.sin(e*Math.PI/2),simulatePressure:!0},tP={easing:e=>e*e,simulatePressure:!1};function tC(e){return A(C({size:1+1.5*e2(e.style).strokeWidth,thinning:.65,streamline:.65,smoothing:.65},.5===e.points[1][2]?tx:tP),{last:e.isComplete})}function tA(e,t){return(0,c.Km)(e.points,t)}function tE(e){let{points:t}=e;if(t.length<2)return"M 0 0 L 0 0";let i=tC(e),n=tA(e,i),a=t[t.length-1];return h.A.isEqual(n[0].point,a)||n.push({point:a}),s.Aq.getSvgPathFromStrokePoints(n)}var tB=class extends th{constructor(){super(...arguments),B(this,"type","draw"),B(this,"pointsBoundsCache",new WeakMap([])),B(this,"shapeBoundsCache",new Map),B(this,"rotatedCache",new WeakMap([])),B(this,"pointCache",{}),B(this,"canClone",!0),B(this,"getShape",e=>s.Aq.deepMerge({id:"id",type:"draw",name:"Draw",parentId:"page",childIndex:1,point:[0,0],rotation:0,style:e5,points:[],isComplete:!1},e)),B(this,"Component",th.Component(({shape:e,meta:t,isSelected:i,isGhost:a,events:o},r)=>{let{points:l,style:d,isComplete:p}=e,u=n.useMemo(()=>(function(e){return e.points.length<2?"":s.Aq.getSvgPathFromStroke((0,c.Km)(e.points,tC(e)).map(e=>e.point))})(e),[l,d.size]),g=n.useMemo(()=>"draw"===d.dash?function(e){if(e.points.length<2)return"";let t=tC(e),i=tA(e,t);return s.Aq.getSvgPathFromStroke((0,c.U$)(i,t))}(e):tE(e),[l,d.size,d.dash,p]),{stroke:m,fill:f,strokeWidth:v}=e2(d,t.isDarkMode),b=this.getBounds(e);if(b.width<=v/2&&b.height<=v/2)return n.createElement(s.UL,C({ref:r,id:e.id+"_svg"},o),n.createElement("circle",{r:1+v,fill:m,stroke:m,pointerEvents:"all",opacity:a?.3:1}));let y=d.isFilled&&l.length>3&&h.l.dist(l[0],l[l.length-1])<2*v;if("draw"===e.style.dash)return n.createElement(s.UL,C({ref:r,id:e.id+"_svg"},o),n.createElement("g",{opacity:a?.3:1},n.createElement("path",{className:y||i?"tl-fill-hitarea":"tl-stroke-hitarea",d:g}),y&&n.createElement("path",{d:u,stroke:"none",fill:f,strokeLinejoin:"round",strokeLinecap:"round",pointerEvents:"none"}),n.createElement("path",{d:g,fill:m,stroke:m,strokeWidth:v/2,strokeLinejoin:"round",strokeLinecap:"round",pointerEvents:"none"})));let S={draw:"none",solid:"none",dotted:`0.1 ${4*v}`,dashed:`${4*v} ${4*v}`}[d.dash],I={draw:"none",solid:"none",dotted:"0",dashed:"0"}[d.dash];return n.createElement(s.UL,C({ref:r,id:e.id+"_svg"},o),n.createElement("g",{opacity:a?.3:1},n.createElement("path",{className:y&&i?"tl-fill-hitarea":"tl-stroke-hitarea",d:g}),n.createElement("path",{d:g,fill:y?f:"none",stroke:"none",strokeWidth:Math.min(4,2*v),strokeLinejoin:"round",strokeLinecap:"round",pointerEvents:"none"}),n.createElement("path",{d:g,fill:"none",stroke:m,strokeWidth:1+1.5*v,strokeDasharray:S,strokeDashoffset:I,strokeLinejoin:"round",strokeLinecap:"round",pointerEvents:"none"})))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{points:t}=e,i=n.useMemo(()=>tE(e),[t]),s=this.getBounds(e);return s.width<4&&s.height<4?n.createElement("circle",{x:s.width/2,y:s.height/2,r:1}):n.createElement("path",{d:i})})),B(this,"transform",(e,t,{initialShape:i,scaleX:n,scaleY:a})=>{let o=s.Aq.getFromCache(this.boundsCache,i,()=>s.Aq.getBoundsFromPoints(i.points)),r=i.points.map(([e,i,s])=>[t.width*(n<0?1-e/o.width:e/o.width),t.height*(a<0?1-i/o.height:i/o.height),s]),l=s.Aq.getBoundsFromPoints(e.points);return{points:r,point:h.l.sub([t.minX,t.minY],[l.minX,l.minY])}}),B(this,"getBounds",e=>{let t=!this.pointsBoundsCache.has(e.points),i=this.pointCache[e.id]!==e.point;if(t){let t=s.Aq.getBoundsFromPoints(e.points);this.pointsBoundsCache.set(e.points,t),this.shapeBoundsCache.set(e.id,s.Aq.translateBounds(t,e.point)),this.pointCache[e.id]=e.point}else i&&!t&&(this.pointCache[e.id]=e.point,this.shapeBoundsCache.set(e.id,s.Aq.translateBounds(this.pointsBoundsCache.get(e.points),e.point)));return this.shapeBoundsCache.get(e.id)}),B(this,"shouldRender",(e,t)=>t.points!==e.points||t.style!==e.style||t.isComplete!==e.isComplete),B(this,"hitTestPoint",(e,t)=>{let i=h.l.sub(t,e.point);return s.Aq.pointInPolyline(i,e.points)}),B(this,"hitTestLineSegment",(e,t,i)=>{let{points:n,point:a}=e,o=h.l.sub(t,a),r=h.l.sub(i,a),l=this.getBounds(e);if(l.width<8&&l.height<8)return 5>h.l.distanceToLineSegment(t,i,s.Aq.getBoundsCenter(l));if((0,p.Oy)(o,r,l)){for(let e=1;e<n.length;e++)if((0,p._o)(n[e-1],n[e],o,r).didIntersect)return!0}return!1}),B(this,"hitTestBounds",(e,t)=>{if(!e.rotation){let i=this.getBounds(e);return s.Aq.boundsContain(t,i)||(s.Aq.boundsContain(i,t)||(0,p.SF)(i,t).length>0)&&(0,p.v8)(s.Aq.translateBounds(t,h.l.neg(e.point)),e.points).length>0}let i=this.getRotatedBounds(e),n=s.Aq.getFromCache(this.rotatedCache,e,()=>{let t=s.Aq.getBoundsCenter(s.Aq.getBoundsFromPoints(e.points));return e.points.map(i=>h.l.rotWith(i,t,e.rotation||0))});return s.Aq.boundsContain(t,i)||(0,p.v8)(s.Aq.translateBounds(t,h.l.neg(e.point)),n).length>0})}},tz=n.memo(function({radius:e,style:t,isSelected:i,isDarkMode:a}){let{stroke:o,strokeWidth:r,fill:l}=e2(t,a),d=1+1.618*r,h=Math.max(0,e[0]-d/2),p=Math.max(0,e[1]-d/2),c=s.Aq.perimeterOfEllipse(h,p),{strokeDasharray:u,strokeDashoffset:g}=s.Aq.getPerfectDashProps(c<64?2*c:c,1.618*r,t.dash,4);return n.createElement(n.Fragment,null,n.createElement("ellipse",{className:t.isFilled||i?"tl-fill-hitarea":"tl-stroke-hitarea",cx:e[0],cy:e[1],rx:e[0],ry:e[1]}),n.createElement("ellipse",{cx:e[0],cy:e[1],rx:h,ry:p,fill:l,stroke:o,strokeWidth:d,strokeDasharray:u,strokeDashoffset:g,pointerEvents:"none",strokeLinecap:"round",strokeLinejoin:"round"}))});function tM(e,t,i){let{strokeWidth:n}=e2(i),a=s.Aq.rng(e),o=t[0]+a()*n*2,r=t[1]+a()*n*2,l=s.Aq.perimeterOfEllipse(o,r),d=[],h=Math.PI+Math.PI*a(),p=Math.abs(a()),u=Math.max(16,l/10);for(let e=0;e<u;e++){let i=ef.easeInOutSine(e/(u+1)),s=2*h+Math.PI*(2+p)*i,n=Math.cos(s),l=Math.sin(s);d.push([o*n+t[0],r*l+t[1],i+.5+a()/2])}return(0,c.Km)(d,{size:1+2*n,thinning:.618,end:{taper:l/8},start:{taper:l/12},streamline:0,simulatePressure:!0})}function tD(e,t,i){return s.Aq.getSvgPathFromStrokePoints(tM(e,t,i))}var tj=n.memo(function({id:e,radius:t,style:i,isSelected:a,isDarkMode:o}){let{stroke:r,strokeWidth:l,fill:d}=e2(i,o),h=function(e,t,i){let{strokeWidth:n}=e2(i),a=s.Aq.rng(e),o=t[0]+a()*n*2,r=t[1]+a()*n*2,l=s.Aq.perimeterOfEllipse(o,r);return s.Aq.getSvgPathFromStroke((0,c.U$)(tM(e,t,i),{size:2+2*n,thinning:.618,end:{taper:l/8},start:{taper:l/12},streamline:0,simulatePressure:!0}))}(e,t,i);return n.createElement(n.Fragment,null,n.createElement("ellipse",{className:i.isFilled||a?"tl-fill-hitarea":"tl-stroke-hitarea",cx:t[0],cy:t[1],rx:t[0],ry:t[1]}),i.isFilled&&n.createElement("path",{d:tD(e,t,i),stroke:"none",fill:d,pointerEvents:"none"}),n.createElement("path",{d:h,fill:r,stroke:r,strokeWidth:l,pointerEvents:"none",strokeLinecap:"round",strokeLinejoin:"round"}))}),tT=class extends th{constructor(){super(...arguments),B(this,"type","ellipse"),B(this,"canBind",!0),B(this,"canClone",!0),B(this,"canEdit",!0),B(this,"getShape",e=>s.Aq.deepMerge({id:"id",type:"ellipse",name:"Ellipse",parentId:"page",childIndex:1,point:[0,0],radius:[1,1],rotation:0,style:e5,label:"",labelPoint:[.5,.5]},e)),B(this,"Component",th.Component(({shape:e,isGhost:t,isSelected:i,isBinding:a,isEditing:o,meta:r,bounds:l,events:d,onShapeChange:h,onShapeBlur:p},c)=>{let{id:u,radius:g,style:m,label:f="",labelPoint:v=eg}=e,b=eQ(e.style),y=e2(m,r.isDarkMode),S=1+1.618*y.strokeWidth,I=Math.max(0,g[0]-S/2),k=Math.max(0,g[1]-S/2),w="draw"===m.dash?tj:tz,x=n.useCallback(e=>null==h?void 0:h({id:u,label:e}),[h]);return n.createElement(tO,C({ref:c},d),n.createElement(ts,{isEditing:o,onChange:x,onBlur:p,font:b,text:f,color:y.stroke,offsetX:(v[0]-.5)*l.width,offsetY:(v[1]-.5)*l.height,shape:e}),n.createElement(s.UL,{id:e.id+"_svg",opacity:t?.3:1,shapeStyle:m},a&&n.createElement("ellipse",{className:"tl-binding-indicator",cx:g[0],cy:g[1],rx:I,ry:k,strokeWidth:this.bindingDistance}),n.createElement(w,{id:u,radius:g,style:m,isSelected:i,isDarkMode:r.isDarkMode})))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{id:t,radius:i,style:s}=e,a=1+1.618*e2(s).strokeWidth,o=Math.max(0,i[0]-a/2),r=Math.max(0,i[1]-a/2);return"draw"===s.dash?n.createElement("path",{d:tD(t,i,s)}):n.createElement("ellipse",{cx:i[0],cy:i[1],rx:o,ry:r})})),B(this,"hitTestPoint",(e,t)=>s.Aq.pointInBounds(t,this.getRotatedBounds(e))&&s.Aq.pointInEllipse(t,this.getCenter(e),e.radius[0],e.radius[1],e.rotation||0)),B(this,"hitTestLineSegment",(e,t,i)=>(0,p.vL)(t,i,this.getCenter(e),e.radius[0],e.radius[1],e.rotation||0).didIntersect),B(this,"getBounds",e=>s.Aq.getFromCache(this.boundsCache,e,()=>s.Aq.getRotatedEllipseBounds(e.point[0],e.point[1],e.radius[0],e.radius[1],0))),B(this,"getRotatedBounds",e=>s.Aq.getRotatedEllipseBounds(e.point[0],e.point[1],e.radius[0],e.radius[1],e.rotation)),B(this,"hitTestBounds",(e,t)=>{let i=this.getBounds(e);return s.Aq.boundsContained(i,t)||(0,p.ts)(this.getCenter(e),e.radius[0],e.radius[1],e.rotation||0,t).length>0}),B(this,"shouldRender",(e,t)=>t.radius!==e.radius||t.style!==e.style||t.label!==e.label),B(this,"getCenter",e=>h.l.add(e.point,e.radius)),B(this,"getBindingPoint",(e,t,i,n,a,o)=>{{let t=this.getExpandedBounds(e),r=this.getCenter(e),l,d;if(!s.Aq.pointInEllipse(i,r,e.radius[0]+this.bindingDistance,e.radius[1]+this.bindingDistance))return;if(o)l=12>h.l.dist(i,this.getCenter(e))?[.5,.5]:h.l.divV(h.l.sub(i,[t.minX,t.minY]),[t.width,t.height]),d=0;else{let o=(0,p.MV)(n,a,r,e.radius[0],e.radius[1],e.rotation||0).points.sort((e,t)=>h.l.dist(e,n)-h.l.dist(t,n))[0];if(o||(o=(0,p.vL)(i,r,r,e.radius[0],e.radius[1],e.rotation||0).points.sort((e,t)=>h.l.dist(e,i)-h.l.dist(t,i))[0]),!o)return;let c=h.l.med(i,o);if(l=12>h.l.distanceToLineSegment(i,c,this.getCenter(e))?[.5,.5]:h.l.divV(h.l.sub(c,[t.minX,t.minY]),[t.width,t.height]),s.Aq.pointInEllipse(i,r,e.radius[0],e.radius[1],e.rotation||0))d=this.bindingDistance/2;else{let t=(0,p.vL)(i,r,r,e.radius[0],e.radius[1],e.rotation||0).points[0];if(!t)return;d=Math.max(this.bindingDistance/2,h.l.dist(i,t))}}return{point:l,distance:d}}}),B(this,"transform",(e,t,{scaleX:i,scaleY:s,initialShape:n})=>{let{rotation:a=0}=n;return{point:[t.minX,t.minY],radius:[t.width/2,t.height/2],rotation:i<0&&s>=0||s<0&&i>=0?-(a||0):a||0}}),B(this,"transformSingle",(e,t)=>({point:h.l.toFixed([t.minX,t.minY]),radius:h.l.div([t.width,t.height],2)}))}},tO=er("div",{width:"100%",height:"100%"}),tF=class extends th{constructor(){super(...arguments),B(this,"type","group"),B(this,"canBind",!0),B(this,"getShape",e=>s.Aq.deepMerge({id:"id",type:"group",name:"Group",parentId:"page",childIndex:1,point:[0,0],size:[100,100],rotation:0,children:[],style:e5},e)),B(this,"Component",th.Component(({shape:e,isBinding:t,isGhost:i,isHovered:a,isSelected:o,events:r},l)=>{let{id:d,size:h}=e,p=Math.max(0,h[0]-1),c=Math.max(0,h[1]-1),u=[[[1,1],[p,1],p-1],[[p,1],[p,c],c-1],[[p,c],[1,c],p-1],[[1,c],[1,1],c-1]].map(([e,t],i)=>n.createElement("line",{key:d+"_"+i,x1:e[0],y1:e[1],x2:t[0],y2:t[1]}));return n.createElement(s.UL,C({ref:l},r),t&&n.createElement("rect",{className:"tl-binding-indicator",strokeWidth:this.bindingDistance}),n.createElement("g",{opacity:i?.3:1},n.createElement("rect",{x:0,y:0,width:h[0],height:h[1],fill:"transparent",pointerEvents:"all"}),n.createElement(tL,{stroke:"black",opacity:a||o?1:0,strokeLinecap:"round",pointerEvents:"stroke"},u)))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{id:t,size:i}=e,s=Math.max(0,i[0]-1),a=Math.max(0,i[1]-1),o=[[[1,1],[s,1],s-1],[[s,1],[s,a],a-1],[[s,a],[1,a],s-1],[[1,a],[1,1],a-1]].map(([e,i],s)=>n.createElement("line",{key:t+"_"+s,x1:e[0],y1:e[1],x2:i[0],y2:i[1]}));return n.createElement(tL,{strokeLinecap:"round",pointerEvents:"stroke"},o)})),B(this,"getBounds",e=>ew(e,this.boundsCache)),B(this,"shouldRender",(e,t)=>t.size!==e.size||t.style!==e.style)}},tL=er("g",{strokeWidth:"calc(1.5px * var(--tl-scale))",strokeDasharray:"calc(1px * var(--tl-scale)), calc(3px * var(--tl-scale))"}),tR=class extends th{constructor(){super(...arguments),B(this,"type","image"),B(this,"canBind",!0),B(this,"canClone",!0),B(this,"isAspectRatioLocked",!0),B(this,"showCloneHandles",!1),B(this,"getShape",e=>s.Aq.deepMerge({id:"image",type:"image",name:"Image",parentId:"page",childIndex:1,point:[0,0],size:[1,1],rotation:0,style:A(C({},e5),{isFilled:!0}),assetId:"assetId"},e)),B(this,"Component",th.Component(({shape:e,asset:t={src:""},isBinding:i,isGhost:a,meta:o,events:r,onShapeChange:l},d)=>{let{size:h,style:p}=e,{bindingDistance:c}=this,u=n.useRef(null),g=n.useRef(null);return n.useLayoutEffect(()=>{let e=g.current;if(!e)return;let[t,i]=h;e.style.width=`${t}px`,e.style.height=`${i}px`},[h]),n.createElement(s.hD,C({ref:d},r),i&&n.createElement("div",{className:"tl-binding-indicator",style:{position:"absolute",top:`calc(${-c}px * var(--tl-zoom))`,left:`calc(${-c}px * var(--tl-zoom))`,width:`calc(100% + ${2*c}px * var(--tl-zoom))`,height:`calc(100% + ${2*c}px * var(--tl-zoom))`,backgroundColor:"var(--tl-selectFill)"}}),n.createElement(tq,{ref:g,isDarkMode:o.isDarkMode,isFilled:p.isFilled,isGhost:a},n.createElement(tH,{id:e.id+"_image",ref:u,src:t.src,alt:"tl_image_asset",draggable:!1})))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{size:[t,i]}=e;return n.createElement("rect",{x:0,y:0,rx:2,ry:2,width:Math.max(1,t),height:Math.max(1,i)})})),B(this,"getBounds",e=>ew(e,this.boundsCache)),B(this,"shouldRender",(e,t)=>t.size!==e.size||t.style!==e.style),B(this,"transform",tl),B(this,"transformSingle",td),B(this,"getSvgElement",e=>{let t=this.getBounds(e),i=document.createElementNS("http://www.w3.org/2000/svg","image");return i.setAttribute("width",`${t.width}`),i.setAttribute("height",`${t.height}`),i.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),i})}},tq=(0,d.I4)("div",{pointerEvents:"all",position:"relative",fontFamily:"sans-serif",fontSize:"2em",height:"100%",width:"100%",borderRadius:"3px",perspective:"800px",overflow:"hidden",p:{userSelect:"none"},img:{userSelect:"none"},variants:{isGhost:{false:{opacity:1},true:{transition:"opacity .2s",opacity:.3}},isFilled:{true:{},false:{}},isDarkMode:{true:{},false:{}}},compoundVariants:[{isFilled:!0,isDarkMode:!0,css:{}},{isFilled:!0,isDarkMode:!1,css:{}}]}),tH=(0,d.I4)("img",{position:"absolute",top:0,left:0,width:"100%",height:"100%",maxWidth:"100%",minWidth:"100%",pointerEvents:"none",objectFit:"cover",userSelect:"none",borderRadius:2});function tU({strokeWidth:e,size:t}){return n.createElement("rect",{className:"tl-binding-indicator",x:e,y:e,width:Math.max(0,t[0]-e/2),height:Math.max(0,t[1]-e/2),strokeWidth:32})}var tK=n.memo(function({id:e,style:t,size:i,isSelected:a,isDarkMode:o}){let{stroke:r,strokeWidth:l,fill:d}=e2(t,o),h=1+1.618*l,p=Math.max(0,i[0]-h/2),c=Math.max(0,i[1]-h/2),u=[[[h/2,h/2],[p,h/2],p-h/2],[[p,h/2],[p,c],c-h/2],[[p,c],[h/2,c],p-h/2],[[h/2,c],[h/2,h/2],c-h/2]].map(([i,a,o],r)=>{let{strokeDasharray:d,strokeDashoffset:h}=s.Aq.getPerfectDashProps(o,1.618*l,t.dash);return n.createElement("line",{key:e+"_"+r,x1:i[0],y1:i[1],x2:a[0],y2:a[1],strokeDasharray:d,strokeDashoffset:h})});return n.createElement(n.Fragment,null,n.createElement("rect",{className:a||t.isFilled?"tl-fill-hitarea":"tl-stroke-hitarea",x:h/2,y:h/2,width:p,height:c,strokeWidth:16}),t.isFilled&&n.createElement("rect",{x:h/2,y:h/2,width:p,height:c,fill:d,pointerEvents:"none"}),n.createElement("g",{pointerEvents:"none",stroke:r,strokeWidth:h,strokeLinecap:"round"},u))});function t_(e,t,i){let n,a,o,r,l,d,p,c,u,g,m,f,v,b,y,S,{points:I}=(n=e2(t),a=s.Aq.rng(e),o=n.strokeWidth,r=Math.max(0,i[0]),l=Math.max(0,i[1]),d=Array.from([,,,,]).map(()=>[a()*o*.75,a()*o*.75]),p=h.A.add([o/2,o/2],d[0]),c=h.A.add([r-o/2,o/2],d[1]),u=h.A.add([r-o/2,l-o/2],d[2]),g=h.A.add([o/2,l-o/2],d[3]),m=Math.round(Math.abs(2*a()*4)),f=Math.min(r/4,2*o),v=Math.min(l/4,2*o),b=Math.max(8,Math.floor(r/16)),y=Math.max(8,Math.floor(l/16)),{points:[...(S=s.Aq.rotateArray([h.A.pointsBetween(h.A.add(p,[f,0]),h.A.sub(c,[f,0]),b),h.A.pointsBetween(h.A.add(c,[0,v]),h.A.sub(u,[0,v]),y),h.A.pointsBetween(h.A.sub(u,[f,0]),h.A.add(g,[f,0]),b),h.A.pointsBetween(h.A.sub(g,[0,v]),h.A.add(p,[0,v]),y)],m)).flat(),...S[0]].slice(5,Math.floor(-((m%2==0?b:y)/2))+3)}),{strokeWidth:k}=e2(t);return{points:I,options:{size:k,thinning:.65,streamline:.3,smoothing:1,simulatePressure:!1,last:!0}}}function tN(e,t,i){let{points:n,options:a}=t_(e,t,i);return s.Aq.getSvgPathFromStrokePoints((0,c.Km)(n,a))}var tW,tY,t$=n.memo(function({id:e,style:t,size:i,isSelected:a,isDarkMode:o}){let{isFilled:r}=t,{stroke:l,strokeWidth:d,fill:h}=e2(t,o),p=function(e,t,i){let{points:n,options:a}=t_(e,t,i),o=(0,c.Ay)(n,a);return s.Aq.getSvgPathFromStroke(o)}(e,t,i),u=tN(e,t,i);return n.createElement(n.Fragment,null,n.createElement("path",{className:t.isFilled||a?"tl-fill-hitarea":"tl-stroke-hitarea",d:u}),r&&n.createElement("path",{d:u,fill:h,pointerEvents:"none"}),n.createElement("path",{d:p,fill:l,stroke:l,strokeWidth:d,pointerEvents:"none"}))}),tG=class extends th{constructor(){super(...arguments),B(this,"type","rectangle"),B(this,"canBind",!0),B(this,"canClone",!0),B(this,"canEdit",!0),B(this,"getShape",e=>s.Aq.deepMerge({id:"id",type:"rectangle",name:"Rectangle",parentId:"page",childIndex:1,point:[0,0],size:[1,1],rotation:0,style:e5,label:"",labelPoint:[.5,.5]},e)),B(this,"Component",th.Component(({shape:e,isEditing:t,isBinding:i,isSelected:a,isGhost:o,meta:r,bounds:l,events:d,onShapeBlur:h,onShapeChange:p},c)=>{let{id:u,size:g,style:m,label:f="",labelPoint:v=eg}=e,b=eQ(m),y=e2(m,r.isDarkMode),S="draw"===m.dash?t$:tK,I=n.useCallback(e=>null==p?void 0:p({id:u,label:e}),[p]);return n.createElement(tX,C({ref:c},d),n.createElement(ts,{isEditing:t,onChange:I,onBlur:h,font:b,text:f,color:y.stroke,offsetX:(v[0]-.5)*l.width,offsetY:(v[1]-.5)*l.height,shape:e}),n.createElement(s.UL,{id:e.id+"_svg",opacity:o?.3:1,shapeStyle:m},i&&n.createElement(tU,{strokeWidth:y.strokeWidth,size:g}),n.createElement(S,{id:u,style:m,size:g,isSelected:a,isDarkMode:r.isDarkMode})))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{id:t,style:i,size:s}=e,a=e2(i,!1).strokeWidth;return"draw"===i.dash?n.createElement("path",{d:tN(t,i,s)}):n.createElement("rect",{x:a,y:a,rx:1,ry:1,width:Math.max(1,s[0]-2*a),height:Math.max(1,s[1]-2*a)})})),B(this,"getBounds",e=>ew(e,this.boundsCache)),B(this,"shouldRender",(e,t)=>t.size!==e.size||t.style!==e.style||t.label!==e.label),B(this,"transform",tl),B(this,"transformSingle",td)}},tX=er("div",{width:"100%",height:"100%"}),tV=class extends th{constructor(){super(...arguments),B(this,"type","sticky"),B(this,"canBind",!0),B(this,"canEdit",!0),B(this,"canClone",!0),B(this,"hideResizeHandles",!0),B(this,"showCloneHandles",!0),B(this,"getShape",e=>s.Aq.deepMerge({id:"id",type:"sticky",name:"Sticky",parentId:"page",childIndex:1,point:[0,0],size:[200,200],text:"",rotation:0,style:e3},e)),B(this,"Component",th.Component(({shape:e,meta:t,events:i,isGhost:a,isBinding:o,isEditing:r,onShapeBlur:l,onShapeChange:d},h)=>{let p=e0(e.style),{color:c,fill:u}=e1(e.style,t.isDarkMode),g=n.useRef(null),m=n.useRef(null),f=n.useRef(null),v=n.useRef(!1),b=n.useCallback(e=>{e.stopPropagation()},[]),y=n.useCallback(t=>{null==d||d({id:e.id,type:e.type,text:ti.normalizeText(t)})},[e.id]),S=n.useCallback(e=>{y(e.currentTarget.value)},[d,y]),I=n.useCallback(t=>{if("Escape"===t.key){t.preventDefault(),t.stopPropagation(),null==l||l();return}if("Tab"===t.key&&0===e.text.length)return void t.preventDefault();if("Meta"===t.key||t.metaKey){if("z"===t.key&&t.metaKey){t.shiftKey?document.execCommand("redo",!1):document.execCommand("undo",!1),t.stopPropagation(),t.preventDefault();return}}else t.stopPropagation();(t.metaKey||t.ctrlKey)&&"="===t.key&&t.preventDefault(),"Tab"===t.key&&(t.preventDefault(),t.shiftKey?e4.unindent(t.currentTarget):e4.indent(t.currentTarget),null==d||d(A(C({},e),{text:ti.normalizeText(t.currentTarget.value)})))},[e,d]),k=n.useCallback(e=>{e.currentTarget.setSelectionRange(0,0),null==l||l()},[]),w=n.useCallback(e=>{r&&v.current&&e.currentTarget.select()},[r]);n.useEffect(()=>{if(r){v.current=!0;let e=m.current;e.focus(),e.select()}},[r]),n.useEffect(()=>{let t=f.current,{size:i}=e,{offsetHeight:s}=t,n=tJ-2*tZ;if(s===i[1]-2*tZ)return;if(s>n){null==d||d({id:e.id,size:[i[0],s+2*tZ]});return}if(s<n&&i[1]>tJ){null==d||d({id:e.id,size:[i[0],tJ]});return}let a=m.current;null==a||a.focus()},[e.text,e.size[1],e.style]);let x={font:p,color:c,textShadow:t.isDarkMode?"0.5px 0.5px 2px rgba(255, 255, 255,.25)":"0.5px 0.5px 2px rgba(255, 255, 255,.5)"};return n.createElement(s.hD,C({ref:h},i),n.createElement(tQ,{ref:g,isDarkMode:t.isDarkMode,isGhost:a,style:C({backgroundColor:u},x)},o&&n.createElement("div",{className:"tl-binding-indicator",style:{position:"absolute",top:-this.bindingDistance,left:-this.bindingDistance,width:`calc(100% + ${2*this.bindingDistance}px)`,height:`calc(100% + ${2*this.bindingDistance}px)`,backgroundColor:"var(--tl-selectFill)"}}),n.createElement(t1,{ref:f,isEditing:r,alignment:e.style.textAlign},e.text,"​"),r&&n.createElement(t2,{ref:m,onPointerDown:b,value:e.text,onChange:S,onKeyDown:I,onFocus:w,onBlur:k,tabIndex:-1,autoComplete:"false",autoCapitalize:"false",autoCorrect:"false",autoSave:"false",autoFocus:!0,spellCheck:!0,alignment:e.style.textAlign,onContextMenu:e8,onCopy:e8,onPaste:e8,onCut:e8})))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{size:[t,i]}=e;return n.createElement("rect",{x:0,y:0,rx:3,ry:3,width:Math.max(1,t),height:Math.max(1,i)})})),B(this,"getBounds",e=>ew(e,this.boundsCache)),B(this,"shouldRender",(e,t)=>t.size!==e.size||t.style!==e.style||t.text!==e.text),B(this,"transform",(e,t,{scaleX:i,scaleY:s,transformOrigin:n})=>({point:h.l.toFixed([t.minX+(t.width-e.size[0])*(i<0?1-n[0]:n[0]),t.minY+(t.height-e.size[1])*(s<0?1-n[1]:n[1])])})),B(this,"transformSingle",e=>e),B(this,"getSvgElement",(e,t)=>{var i,s;let n=this.getBounds(e),a=e1(e.style,t),o=eG[e.style.size]*(null!=(i=e.style.scale)?i:1),r=eZ(e.style.font).slice(1,-1),l=null!=(s=e.style.textAlign)?s:"start",d=eD(e.text,o,r,l,n.width-2*tZ,!0);d.setAttribute("fill",a.color),d.setAttribute("transform",`translate(${tZ}, ${tZ})`);let h=document.createElementNS("http://www.w3.org/2000/svg","g"),p=document.createElementNS("http://www.w3.org/2000/svg","rect");return p.setAttribute("width",n.width+""),p.setAttribute("height",n.height+""),p.setAttribute("fill",a.fill),p.setAttribute("rx","3"),p.setAttribute("ry","3"),h.appendChild(p),h.appendChild(d),h})}},tZ=16,tJ=200,tQ=er("div",{pointerEvents:"all",position:"relative",backgroundColor:"rgba(255, 220, 100)",fontFamily:"sans-serif",height:"100%",width:"100%",padding:"16px",borderRadius:"3px",perspective:"800px",variants:{isGhost:{false:{opacity:1},true:{transition:"opacity .2s",opacity:.3}},isDarkMode:{true:{boxShadow:"2px 3px 12px -2px rgba(0,0,0,.3), 1px 1px 4px rgba(0,0,0,.3), 1px 1px 2px rgba(0,0,0,.3)"},false:{boxShadow:"2px 3px 12px -2px rgba(0,0,0,.2), 1px 1px 4px rgba(0,0,0,.16),  1px 1px 2px rgba(0,0,0,.16)"}}}}),t0={whiteSpace:"pre-wrap",overflowWrap:"break-word",letterSpacing:eu},t1=er("div",C({position:"absolute",top:tZ,left:tZ,width:`calc(100% - ${2*tZ}px)`,height:"fit-content",font:"inherit",pointerEvents:"none",userSelect:"none",variants:{isEditing:{true:{opacity:1},false:{opacity:1}},alignment:{start:{textAlign:"left"},middle:{textAlign:"center"},end:{textAlign:"right"},justify:{textAlign:"justify"}}}},t0)),t2=er("textarea",A(C({width:"100%",height:"100%",border:"none",overflow:"hidden",background:"none",outline:"none",textAlign:"left",font:"inherit",padding:0,color:"transparent",verticalAlign:"top",resize:"none",caretColor:"black"},t0),{variants:{alignment:{start:{textAlign:"left"},middle:{textAlign:"center"},end:{textAlign:"right"},justify:{textAlign:"justify"}}},"&:focus":{outline:"none",border:"none"}})),t5=class extends th{constructor(){super(...arguments),B(this,"type","text"),B(this,"isAspectRatioLocked",!0),B(this,"canEdit",!0),B(this,"canBind",!0),B(this,"canClone",!0),B(this,"bindingDistance",8),B(this,"getShape",e=>s.Aq.deepMerge({id:"id",type:"text",name:"Text",parentId:"page",childIndex:1,point:[0,0],rotation:0,text:" ",style:e3},e)),B(this,"texts",new Map),B(this,"Component",th.Component(({shape:e,isBinding:t,isGhost:i,isEditing:a,onShapeBlur:o,onShapeChange:r,meta:l,events:d},p)=>{let{text:c,style:u}=e,g=e2(u,l.isDarkMode),m=eQ(e.style),f=n.useRef(null),v=n.useRef(!1),b=n.useRef(c);n.useLayoutEffect(()=>{if(c!==b.current){let t=[0,0];this.texts.set(e.id,c);let i=this.getBounds(e),s=this.getBounds(e);switch(e.style.textAlign){case"start":break;case"middle":t=h.l.div([s.width-i.width,0],2);break;case"end":t=[s.width-i.width,0]}b.current=c,null==r||r(A(C({},e),{id:e.id,point:h.l.sub(e.point,t),text:c}))}},[c]);let y=n.useCallback(t=>{let i=[0,0],s=ti.normalizeText(t.currentTarget.value),n=this.getBounds(e);this.texts.set(e.id,s);let a=this.getBounds(A(C({},e),{text:s}));switch(e.style.textAlign){case"start":break;case"middle":i=h.l.div([a.width-n.width,0],2);break;case"end":i=[a.width-n.width,0]}b.current=s,null==r||r(A(C({},e),{id:e.id,point:h.l.sub(e.point,i),text:s}))},[e.id,e.point]),S=n.useCallback(t=>{if("Escape"===t.key){t.preventDefault(),t.stopPropagation(),null==o||o();return}if("Tab"===t.key&&0===e.text.length)return void t.preventDefault();if("Enter"===t.key&&(t.metaKey||t.ctrlKey)){t.preventDefault(),t.stopPropagation(),f.current.blur();return}if("Meta"===t.key||t.metaKey){if("z"===t.key&&t.metaKey){t.shiftKey?document.execCommand("redo",!1):document.execCommand("undo",!1),t.stopPropagation(),t.preventDefault();return}}else t.stopPropagation();(t.metaKey||t.ctrlKey)&&"="===t.key&&t.preventDefault(),"Tab"===t.key&&(t.preventDefault(),t.shiftKey?e4.unindent(t.currentTarget):e4.indent(t.currentTarget),null==r||r(A(C({},e),{text:ti.normalizeText(t.currentTarget.value)})))},[e,r]),I=n.useCallback(e=>{e.currentTarget.setSelectionRange(0,0),null==o||o()},[]),k=n.useCallback(e=>{a&&v.current&&document.activeElement===e.currentTarget&&e.currentTarget.select()},[a]),w=n.useCallback(e=>{a&&e.stopPropagation()},[a]);return n.useEffect(()=>{a?(this.texts.set(e.id,c),requestAnimationFrame(()=>{v.current=!0;let e=f.current;e&&(e.focus(),e.select())})):null==o||o()},[a]),n.createElement(s.hD,C({ref:p},d),n.createElement(t3,{isGhost:i,isEditing:a,onPointerDown:w},n.createElement(t8,{style:{font:m,color:g.stroke,textAlign:eP(u.textAlign)},"data-color":e.style.color},t&&n.createElement("div",{className:"tl-binding-indicator",style:{position:"absolute",top:-this.bindingDistance,left:-this.bindingDistance,width:`calc(100% + ${2*this.bindingDistance}px)`,height:`calc(100% + ${2*this.bindingDistance}px)`,backgroundColor:"var(--tl-selectFill)"}}),a?n.createElement(t6,{ref:f,style:{font:m,color:g.stroke},name:"text",tabIndex:-1,autoComplete:"false",autoCapitalize:"false",autoCorrect:"false",autoSave:"false",autoFocus:!0,placeholder:"",spellCheck:"true",wrap:"off",dir:"auto",datatype:"wysiwyg",defaultValue:c,color:g.stroke,onFocus:k,onChange:y,onKeyDown:S,onBlur:I,onPointerDown:w,onContextMenu:e8,onCopy:e8,onPaste:e8,onCut:e8}):c,"​")))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{width:t,height:i}=this.getBounds(e);return n.createElement("rect",{x:0,y:0,width:t,height:i})})),B(this,"getBounds",e=>{let t=s.Aq.getFromCache(this.boundsCache,e,()=>{var t;if(!tY)return{minX:0,minY:0,maxX:10,maxY:10,width:10,height:10};tY.parentNode||document.body.appendChild(tY),tY.style.font=eQ(e.style),tY.textContent=null!=(t=this.texts.get(e.id))?t:e.text;let i=tY.offsetWidth||1,s=tY.offsetHeight||1;return{minX:0,maxX:i,minY:0,maxY:s,width:i,height:s}});return s.Aq.translateBounds(t,e.point)}),B(this,"shouldRender",(e,t)=>t.text!==e.text||t.style.scale!==e.style.scale||t.style!==e.style),B(this,"transform",(e,t,{initialShape:i,scaleX:s,scaleY:n})=>{let{rotation:a=0,style:{scale:o=1}}=i,r=o*Math.abs(Math.min(s,n));return{point:[t.minX,t.minY],rotation:s<0&&n>=0||n<0&&s>=0?-(a||0):a,style:A(C({},i.style),{scale:r})}}),B(this,"transformSingle",(e,t,{initialShape:i,scaleX:s,scaleY:n})=>{let{style:{scale:a=1}}=i;return{point:h.l.toFixed([t.minX,t.minY]),style:A(C({},i.style),{scale:a*Math.max(Math.abs(n),Math.abs(s))})}}),B(this,"onDoubleClickBoundsHandle",e=>{let t=this.getCenter(e),i=this.getCenter(A(C({},e),{style:A(C({},e.style),{scale:1})}));return{style:A(C({},e.style),{scale:1}),point:h.l.toFixed(h.l.add(e.point,h.l.sub(t,i)))}}),B(this,"getSvgElement",(e,t)=>{var i,s;let n=this.getBounds(e),a=e2(e.style,t),o=eV(e.style.size,e.style.font)*(null!=(i=e.style.scale)?i:1),r=eZ(e.style.font).slice(1,-1),l=null!=(s=e.style.textAlign)?s:"start",d=eD(e.text,o,r,l,n.width,!1);return d.setAttribute("fill",a.stroke),d.setAttribute("data-color",e.style.color),d})}};"undefined"!=typeof window&&(tY=function(){var e;null==(e=document.getElementById("__textMeasure"))||e.remove();let t=document.createElement("pre");return t.id="__textMeasure",Object.assign(t.style,{whiteSpace:"pre",width:"auto",border:"1px solid transparent",padding:"4px",margin:"0px",letterSpacing:eu,opacity:"0",position:"absolute",top:"-500px",left:"0px",zIndex:"9999",pointerEvents:"none",userSelect:"none",alignmentBaseline:"mathematical",dominantBaseline:"mathematical"}),t.tabIndex=-1,document.body.appendChild(t),t}());var t3=er("div",{width:"100%",height:"100%",variants:{isGhost:{false:{opacity:1},true:{transition:"opacity .2s",opacity:.3}},isEditing:{false:{pointerEvents:"all",userSelect:"all"},true:{pointerEvents:"none",userSelect:"none"}}}}),t4={whiteSpace:"pre-wrap",overflowWrap:"break-word"},t8=er("div",C({position:"absolute",width:"100%",height:"100%",padding:"4px",zIndex:1,minHeight:1,minWidth:1,lineHeight:1,letterSpacing:eu,outline:0,fontWeight:"500",backfaceVisibility:"hidden",userSelect:"none",pointerEvents:"none",WebkitUserSelect:"none",WebkitTouchCallout:"none",isEditing:{false:{},true:{pointerEvents:"all",background:"$boundsBg",userSelect:"text",WebkitUserSelect:"text"}}},t4)),t6=er("textarea",A(C({position:"absolute",top:0,left:0,zIndex:1,width:"100%",height:"100%",border:"none",padding:"4px",resize:"none",textAlign:"inherit",minHeight:"inherit",minWidth:"inherit",lineHeight:"inherit",letterSpacing:"inherit",outline:0,fontWeight:"inherit",overflow:"hidden",backfaceVisibility:"hidden",display:"inline-block",pointerEvents:"all",background:"$boundsBg",userSelect:"text",WebkitUserSelect:"text"},t4),{"&:focus":{outline:"none",border:"none"}})),t9=n.memo(function({id:e,size:t,style:i,isSelected:a,isDarkMode:o}){let{stroke:r,strokeWidth:l,fill:d}=e2(i,o),p=1+1.618*l,c=e9(t),u=s.Aq.pointsToLineSegments(c,!0).map(([t,a],o)=>{let{strokeDasharray:d,strokeDashoffset:c}=s.Aq.getPerfectDashProps(h.A.dist(t,a),1.618*l,i.dash);return n.createElement("line",{key:e+"_"+o,x1:t[0],y1:t[1],x2:a[0],y2:a[1],stroke:r,strokeWidth:p,strokeLinecap:"round",strokeDasharray:d,strokeDashoffset:c})}),g=c.join();return n.createElement(n.Fragment,null,n.createElement("polygon",{className:i.isFilled||a?"tl-fill-hitarea":"tl-stroke-hitarea",points:g}),i.isFilled&&n.createElement("polygon",{fill:d,points:g,pointerEvents:"none"}),n.createElement("g",{pointerEvents:"stroke"},u))}),t7=n.memo(function({id:e,size:t,style:i,isSelected:a,isDarkMode:o}){let{stroke:r,strokeWidth:l,fill:d}=e2(i,o),h=function(e,t,i){let{points:n,options:a}=te(e,t,i),o=(0,c.Ay)(n,a);return s.Aq.getSvgPathFromStroke(o)}(e,t,i),p=function(e,t,i){let{points:n,options:a}=te(e,t,i);return s.Aq.getSvgPathFromStrokePoints((0,c.Km)(n,a))}(e,t,i);return n.createElement(n.Fragment,null,n.createElement("path",{className:i.isFilled||a?"tl-fill-hitarea":"tl-stroke-hitarea",d:p}),i.isFilled&&n.createElement("path",{d:p,fill:d,pointerEvents:"none"}),n.createElement("path",{d:h,fill:r,stroke:r,strokeWidth:l,pointerEvents:"none"}))});function ie({size:e}){let t=e9(e).join();return n.createElement("polygon",{className:"tl-binding-indicator",points:t,strokeWidth:32})}var it=class extends th{constructor(){super(...arguments),B(this,"type","triangle"),B(this,"canBind",!0),B(this,"canClone",!0),B(this,"canEdit",!0),B(this,"getShape",e=>s.Aq.deepMerge({id:"id",type:"triangle",name:"Triangle",parentId:"page",childIndex:1,point:[0,0],size:[1,1],rotation:0,style:e5,label:"",labelPoint:[.5,.5]},e)),B(this,"Component",th.Component(({shape:e,bounds:t,isBinding:i,isEditing:a,isSelected:o,isGhost:r,meta:l,events:d,onShapeChange:h,onShapeBlur:p},c)=>{let{id:u,label:g="",size:m,style:f,labelPoint:v=eg}=e,b=eQ(f),y=e2(f,l.isDarkMode),S="draw"===f.dash?t7:t9,I=n.useCallback(e=>null==h?void 0:h({id:u,label:e}),[h]),k=n.useMemo(()=>this.getLabelOffsetY(e),[m]);return n.createElement(ii,C({ref:c},d),n.createElement(ts,{font:b,text:g,color:y.stroke,offsetX:(v[0]-.5)*t.width,offsetY:k+(v[1]-.5)*t.height,isEditing:a,onChange:I,onBlur:p,shape:e}),n.createElement(s.UL,{id:e.id+"_svg",opacity:r?.3:1,shapeStyle:f},i&&n.createElement(ie,{size:m}),n.createElement(S,{id:u,style:f,size:m,isSelected:o,isDarkMode:l.isDarkMode})))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{size:t}=e;return n.createElement("polygon",{points:e9(t).join()})})),B(this,"shouldRender",(e,t)=>t.size!==e.size||t.style!==e.style||t.label!==e.label),B(this,"getBounds",e=>ew(e,this.boundsCache)),B(this,"getExpandedBounds",e=>s.Aq.getBoundsFromPoints(e9(e.size,this.bindingDistance).map(t=>h.A.add(t,e.point)))),B(this,"hitTestLineSegment",(e,t,i)=>(0,p.NC)(t,i,this.getPoints(e)).didIntersect),B(this,"hitTestBounds",(e,t)=>s.Aq.boundsContained(this.getBounds(e),t)||(0,p.VH)(t,this.getPoints(e)).length>0),B(this,"getBindingPoint",(e,t,i,n,a,o)=>{let r=this.getExpandedBounds(e);if(!s.Aq.pointInBounds(i,r))return;let l=e9(e.size).map(t=>h.A.add(t,e.point)),d=e9(e.size,this.bindingDistance).map(t=>h.A.add(t,e.point)),c=s.Aq.pointsToLineSegments(l,!0).map(([e,t])=>h.A.distanceToLineSegment(e,t,i)).sort((e,t)=>e-t)[0];if(!(s.Aq.pointInPolygon(i,d)||c<this.bindingDistance))return;let u=s.Aq.pointsToLineSegments(d.concat([d[0]])).map(e=>(0,p.zd)(n,a,e[0],e[1])).filter(e=>e.didIntersect).flatMap(e=>e.points);if(!u.length)return;let g=h.A.add(e7(e.size),e.point),m=u.sort((e,t)=>h.A.dist(t,n)-h.A.dist(e,n))[0],f=h.A.med(i,m),v,b;o?(v=8>h.A.dist(i,g)?g:i,b=0):(v=8>h.A.distanceToLineSegment(i,f,g)?g:f,b=s.Aq.pointInPolygon(i,l)?this.bindingDistance:Math.max(this.bindingDistance,c));let y=h.A.divV(h.A.sub(v,[r.minX,r.minY]),[r.width,r.height]);return{point:h.A.clampV(y,0,1),distance:b}}),B(this,"transform",tl),B(this,"transformSingle",td),B(this,"getSvgElement",e=>{var t,i;let s=null==(t=document.getElementById(e.id+"_svg"))?void 0:t.cloneNode(!0);if(s){if("label"in e&&void 0!==e.label){let t=document.createElementNS("http://www.w3.org/2000/svg","g"),n=this.getBounds(e),a=eQ(e.style),o=void 0!==e.style.scale?e.style.scale:1,r=eV(e.style.size,e.style.font)*(null!=(i=e.style.scale)?i:1),l=eZ(e.style.font).slice(1,-1),d=ez(e.label,a),h=eD(e.label,r,l,"middle",d[0],!1);return h.setAttribute("fill",e2(e.style).stroke),h.setAttribute("transform-origin","top left"),h.setAttribute("transform",`translate(${(n.width-d[0]*o)/2}, ${(n.height-d[1]*o)/2+this.getLabelOffsetY(e)})`),t.appendChild(s),t.appendChild(h),t}return s}}),B(this,"getLabelOffsetY",e=>{let t=h.A.div(e.size,2);return(e7(e.size)[1]-t[1])*.72})}getPoints(e){let{rotation:t=0,point:[i,s],size:[n,a]}=e;return[[i+n/2,s],[i,s+a],[i+n,s+a]].map(i=>h.A.rotWith(i,this.getCenter(e),t))}},ii=er("div",{width:"100%",height:"100%"}),is=class extends th{constructor(){super(...arguments),B(this,"type","video"),B(this,"canBind",!0),B(this,"canEdit",!0),B(this,"canClone",!0),B(this,"isAspectRatioLocked",!0),B(this,"showCloneHandles",!1),B(this,"isStateful",!0),B(this,"getShape",e=>s.Aq.deepMerge({id:"video",type:"video",name:"Video",parentId:"page",childIndex:1,point:[0,0],size:[1,1],rotation:0,style:e5,assetId:"assetId",isPlaying:!0,currentTime:0},e)),B(this,"Component",th.Component(({shape:e,asset:t={src:""},isBinding:i,isEditing:a,isGhost:o,meta:r,events:l,onShapeChange:d},h)=>{let p=n.useRef(null),c=n.useRef(null),{currentTime:u=0,size:g,isPlaying:m,style:f}=e;n.useLayoutEffect(()=>{let e=c.current;if(!e)return;let[t,i]=g;e.style.width=`${t}px`,e.style.height=`${i}px`},[g]),n.useLayoutEffect(()=>{let e=p.current;e&&(m?e.play():e.pause())},[m]),n.useLayoutEffect(()=>{let e=p.current;e&&u!==e.currentTime&&(e.currentTime=u)},[u]);let v=n.useCallback(()=>{null==d||d({id:e.id,isPlaying:!0})},[]),b=n.useCallback(()=>{null==d||d({id:e.id,isPlaying:!1})},[]),y=n.useCallback(()=>{let t=p.current;t&&a&&null!=d&&d({id:e.id,currentTime:t.currentTime})},[a]);return n.createElement(s.hD,C({ref:h},l),i&&n.createElement("div",{className:"tl-binding-indicator",style:{position:"absolute",top:-this.bindingDistance,left:-this.bindingDistance,width:`calc(100% + ${2*this.bindingDistance}px)`,height:`calc(100% + ${2*this.bindingDistance}px)`,backgroundColor:"var(--tl-selectFill)"}}),n.createElement(ia,{ref:c,isDarkMode:r.isDarkMode,isGhost:o,isFilled:f.isFilled},n.createElement(io,{ref:p,id:e.id+"_video",muted:!0,loop:!0,playsInline:!0,disableRemotePlayback:!0,disablePictureInPicture:!0,controls:a,autoPlay:m,onPlay:v,onPause:b,onTimeUpdate:y},n.createElement("source",{src:t.src}))))})),B(this,"Indicator",th.Indicator(({shape:e})=>{let{size:[t,i]}=e;return n.createElement("rect",{x:0,y:0,rx:2,ry:2,width:Math.max(1,t),height:Math.max(1,i)})})),B(this,"getBounds",e=>ew(e,this.boundsCache)),B(this,"shouldRender",(e,t)=>t.size!==e.size||t.style!==e.style||t.isPlaying!==e.isPlaying),B(this,"getSvgElement",e=>{let t=this.getBounds(e),i=document.createElementNS("http://www.w3.org/2000/svg","image");return i.setAttribute("width",`${t.width}`),i.setAttribute("height",`${t.height}`),i.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),i}),B(this,"transform",tl),B(this,"transformSingle",td)}},ia=(0,d.I4)("div",{pointerEvents:"all",position:"relative",fontFamily:"sans-serif",fontSize:"2em",height:"100%",width:"100%",borderRadius:"3px",perspective:"800px",overflow:"hidden",p:{userSelect:"none"},img:{userSelect:"none"},variants:{isGhost:{false:{opacity:1},true:{transition:"opacity .2s",opacity:.3}},isFilled:{true:{},false:{}},isDarkMode:{true:{},false:{}}},compoundVariants:[{isFilled:!0,isDarkMode:!0,css:{boxShadow:"2px 3px 12px -2px rgba(0,0,0,.3), 1px 1px 4px rgba(0,0,0,.3), 1px 1px 2px rgba(0,0,0,.3)"}},{isFilled:!0,isDarkMode:!1,css:{boxShadow:"2px 3px 12px -2px rgba(0,0,0,.2), 1px 1px 4px rgba(0,0,0,.16),  1px 1px 2px rgba(0,0,0,.16)"}}]}),io=(0,d.I4)("video",{position:"absolute",top:0,left:0,width:"100%",height:"100%",maxWidth:"100%",minWidth:"100%",pointerEvents:"none",objectFit:"cover",userSelect:"none",borderRadius:2}),ir=new tG,il=new it,id=new tT,ih=new tB,ip=new tk,ic=new t5,iu=new tF,ig=new tV,im={rectangle:ir,triangle:il,ellipse:id,draw:ih,arrow:ip,text:ic,group:iu,sticky:ig,image:new tR,video:new is},iv=e=>"string"==typeof e?im[e]:im[e.type],ib="tldraw_clipboard",iy=class{constructor(e,t,i,n){B(this,"_idbId"),B(this,"initialState"),B(this,"store"),B(this,"pointer",-1),B(this,"_state"),B(this,"_status","loading"),B(this,"stack",[]),B(this,"_snapshot"),B(this,"useStore"),B(this,"ready"),B(this,"isPaused",!1),B(this,"persist",(e,t)=>{if("ready"===this._status&&(this.onPersist&&this.onPersist(this._state,e,t),this._idbId))return u.hZ(this._idbId,this._state).catch(e=>console.error(e))}),B(this,"applyPatch",(e,t)=>{let i=this._state,n=s.Aq.deepMerge(this._state,e),a=this.cleanup(n,i,e,t);return this.onStateWillChange&&this.onStateWillChange(a,t),this._state=a,this.store.setState(this._state,!0),this.onStateDidChange&&this.onStateDidChange(this._state,t),this}),B(this,"migrate",e=>e),B(this,"cleanup",(e,t,i,s)=>e),B(this,"onStateWillChange"),B(this,"onStateDidChange"),B(this,"patchState",(e,t)=>(this.applyPatch(e,t),this.onPatch&&this.onPatch(this._state,e,t),this)),B(this,"replaceState",(e,t)=>{let i=this.cleanup(e,this._state,e,t);return this.onStateWillChange&&this.onStateWillChange(i,"replace"),this._state=i,this.store.setState(this._state,!0),this.onStateDidChange&&this.onStateDidChange(this._state,"replace"),this}),B(this,"setState",(e,t=e.id)=>(this.pointer<this.stack.length-1&&(this.stack=this.stack.slice(0,this.pointer+1)),this.stack.push(A(C({},e),{id:t})),this.pointer=this.stack.length-1,this.applyPatch(e.after,t),this.onCommand&&this.onCommand(this._state,e,t),this.persist(e.after,t),this)),B(this,"onReady"),B(this,"onPatch"),B(this,"onCommand"),B(this,"onPersist"),B(this,"onReplace"),B(this,"onReset"),B(this,"onResetHistory"),B(this,"onUndo"),B(this,"onRedo"),B(this,"reset",()=>(this.onStateWillChange&&this.onStateWillChange(this.initialState,"reset"),this._state=this.initialState,this.store.setState(this._state,!0),this.resetHistory(),this.persist({},"reset"),this.onStateDidChange&&this.onStateDidChange(this._state,"reset"),this.onReset&&this.onReset(this._state),this)),B(this,"replaceHistory",(e,t=e.length-1)=>(this.stack=e,this.pointer=t,this.onReplace&&this.onReplace(this._state),this)),B(this,"resetHistory",()=>(this.stack=[],this.pointer=-1,this.onResetHistory&&this.onResetHistory(this._state),this)),B(this,"undo",()=>{if(!this.isPaused){if(!this.canUndo)return this;let e=this.stack[this.pointer];this.pointer--,this.applyPatch(e.before,"undo"),this.persist(e.before,"undo")}return this.onUndo&&this.onUndo(this._state),this}),B(this,"redo",()=>{if(!this.isPaused){if(!this.canRedo)return this;this.pointer++;let e=this.stack[this.pointer];this.applyPatch(e.after,"redo"),this.persist(e.after,"undo")}return this.onRedo&&this.onRedo(this._state),this}),B(this,"setSnapshot",()=>(this._snapshot=C({},this._state),this)),B(this,"forceUpdate",()=>{this.store.setState(this._state,!0)}),this._idbId=t,this._state=e6(e),this._snapshot=e6(e),this.initialState=e6(e),this.store=(0,m.A)(()=>this._state),this.useStore=(0,g.Ay)(this.store),this.ready=new Promise(s=>{let a="none";this._idbId?(a="restored",u.Jt(this._idbId).then(o=>z(this,null,function*(){if(o){let s=o;if(i){let r=yield u.Jt(t+"_version");r&&r<i&&(s=n?n(o,e,r):e,a="migrated")}yield u.hZ(t+"_version",i||-1);let r=this._state.appState.isEmptyCanvas;s=this.migrate(s),this._state=e6(s),this._snapshot=e6(s),this._state.appState.isEmptyCanvas=r,this.store.setState(this._state,!0)}else yield u.hZ(t+"_version",i||-1);this._status="ready",s(a)})).catch(e=>console.error(e))):(this._status="ready",s(a))}).then(e=>(this.onReady&&this.onReady(e),e))}pause(){this.isPaused=!0}resume(){this.isPaused=!1}get canUndo(){return this.pointer>-1}get canRedo(){return this.pointer<this.stack.length-1}get state(){return this._state}get status(){return this._status}get snapshot(){return this._snapshot}};function iS(e,t,i=[]){let{currentPageId:s}=e,n={},a={};t.forEach(e=>{n[e.id]=void 0,a[e.id]=e});let o={},r={};return i.forEach(e=>{o[e.id]=void 0,r[e.id]=e}),{id:"create",before:{document:{pages:{[s]:{shapes:n,bindings:o}},pageStates:{[s]:{selectedIds:[...e.selectedIds]}}}},after:{document:{pages:{[s]:{shapes:a,bindings:r}},pageStates:{[s]:{selectedIds:t.map(e=>e.id)}}}}}}var iI=(e,t)=>{let i=C({},e);return t.forEach(e=>i[e]=void 0),i};function ik(e,t,i=e.currentPageId){var s;let n,a,o,r,l,d,{pageState:h,selectedIds:p,document:{assets:c}}=e,{before:u,after:g,assetsToRemove:m}=(s=e.state,n={shapes:{},bindings:{}},a={shapes:{},bindings:{}},o=[],r=new Set,l=new Set,t.filter(e=>!ti.getShape(s,e,i).isLocked).forEach(e=>{r.add(e);let t=ti.getShape(s,e,i);n.shapes[e]=t,a.shapes[e]=void 0,void 0!==t.children&&t.children.forEach(e=>{r.add(e);let t=ti.getShape(s,e,i);n.shapes[e]=t,a.shapes[e]=void 0}),t.parentId!==i&&o.push(ti.getShape(s,t.parentId,i)),t.assetId&&l.add(t.assetId)}),o.forEach(e=>{var o;t.includes(e.id)||(r.add(e.id),n.shapes[e.id]={children:e.children},a.shapes[e.id]={children:e.children.filter(e=>!t.includes(e))},(null==(o=a.shapes[e.id])?void 0:o.children.length)===0&&(a.shapes[e.id]=void 0,n.shapes[e.id]=ti.getShape(s,e.id,i)))}),Object.values((d=ti.getPage(s,i)).bindings).filter(e=>r.has(e.fromId)||r.has(e.toId)).forEach(e=>{for(let t of[e.toId,e.fromId])if(void 0===a.shapes[t]){n.bindings[e.id]=e,a.bindings[e.id]=void 0;let i=d.shapes[t];i&&i.handles&&Object.values(i.handles).filter(t=>t.bindingId===e.id).forEach(i=>{var s,o,l,d,h,p;n.shapes[t]=A(C({},n.shapes[t]),{handles:A(C({},null==(s=n.shapes[t])?void 0:s.handles),{[i.id]:A(C({},null==(l=null==(o=n.shapes[t])?void 0:o.handles)?void 0:l[i.id]),{bindingId:e.id})})}),r.has(t)||(a.shapes[t]=A(C({},a.shapes[t]),{handles:A(C({},null==(d=a.shapes[t])?void 0:d.handles),{[i.id]:A(C({},null==(p=null==(h=a.shapes[t])?void 0:h.handles)?void 0:p[i.id]),{bindingId:void 0})})}))})}}),Object.values(s.document.pages).flatMap(e=>Object.values(e.shapes)).forEach(e=>{"assetId"in e&&e.assetId&&!r.has(e.id)&&l.delete(e.assetId)}),{before:n,after:a,assetsToRemove:Array.from(l)}),f=iI(c,m);return{id:"delete",before:{document:{assets:c,pages:{[i]:u},pageStates:{[i]:{selectedIds:[...e.selectedIds]}}}},after:{document:{assets:f,pages:{[i]:g},pageStates:{[i]:{selectedIds:p.filter(e=>!t.includes(e)),hoveredId:h.hoveredId&&t.includes(h.hoveredId)?void 0:h.hoveredId}}}}}}function iw(e,t,i){let{selectedIds:n,currentPageId:a,page:{shapes:o}}=e,r=t.map(e=>ti.getBounds(o[e])),l=1===t.length&&"group"===o[t[0]].type,d=s.Aq.getCommonBounds(r),{before:h,after:p}=ti.mutateShapes(e.state,t,e=>{let t=ti.getBounds(e),n=e.parentId!==a;switch(i){case"horizontal":{if(n&&!l){let i=ti.getBounds(o[e.parentId]),n=s.Aq.getRelativeTransformedBoundingBox(d,d,i,!0,!1).minX-i.minX;return ti.getShapeUtil(e).transform(e,A(C({},t),{minX:t.minX+n,maxX:t.maxX+n}),{type:s.NW.TopLeft,scaleX:1,scaleY:1,initialShape:e,transformOrigin:[.5,.5]})}let i=s.Aq.getRelativeTransformedBoundingBox(d,d,t,!0,!1);return ti.getShapeUtil(e).transform(e,i,{type:s.NW.TopLeft,scaleX:-1,scaleY:1,initialShape:e,transformOrigin:[.5,.5]})}case"vertical":{if(n&&!l){let i=ti.getBounds(o[e.parentId]),n=s.Aq.getRelativeTransformedBoundingBox(d,d,i,!1,!0).minY-i.minY;return ti.getShapeUtil(e).transform(e,A(C({},t),{minY:t.minY+n,maxY:t.maxY+n}),{type:s.NW.TopLeft,scaleX:1,scaleY:1,initialShape:e,transformOrigin:[.5,.5]})}let i=s.Aq.getRelativeTransformedBoundingBox(d,d,t,!1,!0);return ti.getShapeUtil(e).transform(e,i,{type:s.NW.TopLeft,scaleX:1,scaleY:-1,initialShape:e,transformOrigin:[.5,.5]})}}},a,!0);return{id:"flip",before:{document:{pages:{[a]:{shapes:h}},pageStates:{[a]:{selectedIds:n}}}},after:{document:{pages:{[a]:{shapes:p}},pageStates:{[a]:{selectedIds:t}}}}}}function ix(e,t,i){let{currentPageId:s,page:n}=e,a=new Set(t.map(t=>e.getShape(t).parentId)),o={before:{},after:{}},r,l,d;return Array.from(a.values()).forEach(a=>{let h=[];if(a===n.id)h=Object.values(n.shapes).sort((e,t)=>e.childIndex-t.childIndex);else{let t=e.getShape(a);if(!t.children)throw Error("No children in parent!");h=t.children.map(t=>e.getShape(t)).sort((e,t)=>e.childIndex-t.childIndex)}let p=h.map(e=>e.id),c=t.filter(e=>p.includes(e)).map(e=>p.indexOf(e)).sort((e,t)=>e-t);if(c.length!==p.length)switch(i){case"toBack":for(let e=0;e<p.length;e++)if(!c.includes(e)){r=e;break}d=(l=h[r].childIndex)/(c.length+1),o=ti.mutateShapes(e.state,c.map(e=>h[e].id).reverse(),(e,t)=>({childIndex:l-(t+1)*d}),s);break;case"toFront":for(let e=p.length-1;e>=0;e--)if(!c.includes(e)){r=e;break}l=h[r].childIndex,d=1,o=ti.mutateShapes(e.state,c.map(e=>h[e].id),(e,t)=>({childIndex:l+(t+1)}),s);break;case"backward":{let t={};for(let e=p.length-1;e>=0;e--)if(c.includes(e)){for(let i=e;i>=0;i--)if(!c.includes(i)){let s=h[i].childIndex,n,a;0===i?(n=s/2,a=s/2/(e-i+1)):(a=(s-(n=h[i-1].childIndex))/(e-i+1),n+=a);for(let s=0;s<e-i;s++)t[h[i+s+1].id]=n+a*s;break}}Object.values(t).length>0&&(o=ti.mutateShapes(e.state,c.map(e=>h[e].id),e=>({childIndex:t[e.id]}),s));break}case"forward":{let t={};for(let e=0;e<p.length;e++)if(c.includes(e)){for(let i=e;i<p.length;i++)if(!c.includes(i)){l=h[i].childIndex;let s=i===p.length-1?1:(h[i+1].childIndex-l)/(i-e+1);for(let n=0;n<i-e;n++)t[h[e+n].id]=l+s*(n+1);break}}Object.values(t).length>0&&(o=ti.mutateShapes(e.state,c.map(e=>h[e].id),e=>({childIndex:t[e.id]}),s))}}}),{id:"move",before:{document:{pages:{[s]:{shapes:o.before}},pageStates:{[s]:{selectedIds:t}}}},after:{document:{pages:{[s]:{shapes:o.after}},pageStates:{[s]:{selectedIds:t}}}}}}function iP(e,t,i){let{currentPageId:s}=e,{before:n,after:a}=ti.mutateShapes(e.state,t,t=>{var i,s;return null==(s=(i=e.getShapeUtil(t)).onDoubleClickBoundsHandle)?void 0:s.call(i,t)},i);return{id:"reset_bounds",before:{document:{pages:{[s]:{shapes:n}},pageStates:{[s]:{selectedIds:t}}}},after:{document:{pages:{[s]:{shapes:a}},pageStates:{[s]:{selectedIds:t}}}}}}var iC=2*Math.PI;function iA(e,t,i){let{currentPageId:s}=e,n=t.map(t=>e.getShape(t)).filter(e=>"isLocked"===i||!e.isLocked),a=n.every(e=>e[i]),o={},r={};return n.forEach(e=>{o[e.id]={[i]:e[i]},r[e.id]={[i]:!a}}),{id:"toggle",before:{document:{pages:{[s]:{shapes:o}},pageStates:{[s]:{selectedIds:t}}}},after:{document:{pages:{[s]:{shapes:r}},pageStates:{[s]:{selectedIds:t}}}}}}function iE(e,t,i){let s=t.map(e=>e.id),n=ti.mutateShapes(e.state,s.filter(t=>!e.getShape(t,i).isLocked),(e,i)=>t[i],i);return{id:"update",before:{document:{pages:{[i]:{shapes:n.before}}}},after:{document:{pages:{[i]:{shapes:n.after}}}}}}var iB={};function iz(e,t){let{document:i,settings:s}=e,{version:n=0}=i;"assets"in i||(i.assets={});let a=new Set;return Object.values(i.pages).forEach(e=>Object.values(e.shapes).forEach(t=>{let{parentId:i,children:s,assetId:n}=t;n&&a.add(n),i===e.id||e.shapes[i]||(console.warn("Encountered a shape with a missing parent!"),t.parentId=e.id),"group"===t.type&&s&&s.forEach(i=>{e.shapes[i]||(console.warn("Encountered a parent with a missing child!",t.id,i),null==s||s.splice(s.indexOf(i),1))})})),Object.keys(i.assets).forEach(e=>{a.has(e)||delete i.assets[e]}),n===t||(n<14&&Object.values(i.pages).forEach(e=>{Object.values(e.shapes).filter(e=>"text"===e.type).forEach(e=>"script"===e.style.font)}),n<=13&&Object.values(i.pages).forEach(e=>{Object.values(e.bindings).forEach(e=>{Object.assign(e,e.meta)}),Object.values(e.shapes).forEach(e=>{Object.entries(e.style).forEach(([t,i])=>{"string"==typeof i&&(e.style[t]=i.toLowerCase())}),"arrow"===e.type&&e.decorations&&Object.entries(e.decorations).forEach(([t,i])=>{"Arrow"===i&&(e.decorations=A(C({},e.decorations),{[t]:"arrow"}))})})}),n<=13.1&&(i.name="New Document"),n<15&&(i.assets={}),Object.values(i.pages).forEach(e=>{Object.values(e.shapes).forEach(e=>{n<15.2&&("image"===e.type||"video"===e.type)&&(e.style.isFilled=!0),n<15.3&&("rectangle"===e.type||"triangle"===e.type||"ellipse"===e.type||"arrow"===e.type)&&(e.label=e.text||"",e.labelPoint=[.5,.5])})}),n<15.4&&(s.dockPosition="bottom"),n<15.5&&(s.exportBackground="transparent"),Object.values(i.pageStates).forEach(e=>{e.selectedIds=e.selectedIds.filter(t=>void 0!==i.pages[e.id].shapes[t]),e.bindingId=void 0,e.editingId=void 0,e.hoveredId=void 0,e.pointedId=void 0}),i.version=t),e}E(iB,{fileToBase64:()=>iR,fileToText:()=>iq,getImageSizeFromSrc:()=>iH,getVideoSizeFromSrc:()=>iU,loadFileHandle:()=>ij,migrate:()=>iz,openAssetsFromFileSystem:()=>iL,openFromFileSystem:()=>iF,saveFileHandle:()=>iT,saveToFileSystem:()=>iO});var iM={mode:"readwrite"},iD=e=>z(void 0,null,function*(){return(yield e.queryPermission(iM))==="granted"||(yield e.requestPermission(iM))==="granted"});function ij(){return z(this,null,function*(){if("undefined"!=typeof Window&&"_location"in Window)return(yield(0,u.Jt)(`Tldraw_file_handle_${window.location.origin}`))||null})}function iT(e){return z(this,null,function*(){return(0,u.hZ)(`Tldraw_file_handle_${window.location.origin}`,e)})}function iO(e,t,i){return z(this,null,function*(){let s={name:e.name||"New Document",fileHandle:null!=t?t:null,document:e,assets:{}},n=new Blob([JSON.stringify(s,null,2)],{type:"application/vnd.Tldraw+json"});if(t&&!(yield iD(t)))return null;let a=!f.supported&&(null==i?void 0:i.length)?i:`${s.name}`,o=yield(0,f.fileSave)(n,{fileName:`${a}${em}`,description:"Tldraw File",extensions:[`${em}`]},t);return yield iT(o),o})}function iF(){return z(this,null,function*(){var e;let t=yield(0,f.fileOpen)({description:"Tldraw File",extensions:[`${em}`],multiple:!1});if(!t)return null;let i=JSON.parse((yield new Promise(e=>{let i=new FileReader;i.onloadend=()=>{i.readyState===FileReader.DONE&&e(i.result)},i.readAsText(t,"utf8")}))),s=null!=(e=t.handle)?e:null;return yield iT(s),{fileHandle:s,document:i.document}})}function iL(){return z(this,null,function*(){return(0,f.fileOpen)({description:"Image or Video",extensions:[...eS,...eI],multiple:!0})})}function iR(e){return new Promise((t,i)=>{if(e){let s=new FileReader;s.readAsDataURL(e),s.onload=()=>t(s.result),s.onerror=e=>i(e),s.onabort=e=>i(e)}})}function iq(e){return new Promise((t,i)=>{if(e){let s=new FileReader;s.readAsText(e),s.onload=()=>t(s.result),s.onerror=e=>i(e),s.onabort=e=>i(e)}})}function iH(e){return new Promise((t,i)=>{let s=new Image;s.onload=()=>t([s.width,s.height]),s.onerror=()=>i(Error("Could not get image size")),s.src=e})}function iU(e){return new Promise((t,i)=>{let s=document.createElement("video");s.onloadedmetadata=()=>t([s.videoWidth,s.videoHeight]),s.onerror=()=>i(Error("Could not get video size")),s.src=e})}((e,t,i,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of I(t))!w.call(e,n)&&(i||"default"!==n)&&v(e,n,{get:()=>t[n],enumerable:!(s=y(t,n))||s.enumerable})})(iB,f);var iK=class{constructor(e){this.app=e}},i_=class extends iK{constructor(e,t,i,n=!1){var a,o,r;super(e),B(this,"type","arrow"),B(this,"performanceMode"),B(this,"status","translatingHandle"),B(this,"newStartBindingId",s.Aq.uniqueId()),B(this,"draggedBindingId",s.Aq.uniqueId()),B(this,"didBind",!1),B(this,"initialShape"),B(this,"handleId"),B(this,"bindableShapeIds"),B(this,"initialBinding"),B(this,"startBindingShapeId"),B(this,"isCreate"),B(this,"start",()=>{}),B(this,"update",()=>{var e,t,i;let{initialShape:n}=this,{currentPoint:a,shiftKey:o,altKey:r,metaKey:l,currentGrid:d,settings:{showGrid:p}}=this.app,c=this.app.getShape(n.id);if(c.isLocked)return;let{handles:u}=n,g=this.handleId;if(!u[g].canBind)return;let m=h.l.sub(a,h.l.add(u[g].point,n.point));if(o){let e=r?h.l.med(u.start.point,u.end.point):u["start"===g?"end":"start"].point,t=u[g].point,i=h.l.add(t,m),n=h.l.angle(e,i),a=h.l.rotWith(i,e,s.Aq.snapAngleToSegments(n,24)-n);m=h.l.add(m,h.l.sub(a,i))}let f=h.l.add(u[g].point,m),v={[g]:A(C({},u[g]),{point:p?h.l.snap(f,d):h.l.toFixed(f),bindingId:void 0})},b=im.arrow,y=null==(e=b.onHandleChange)?void 0:e.call(b,n,v);if(!y)return;let S={shape:s.Aq.deepMerge(c,y),bindings:{}},I,k=S.shape.handles[this.handleId],w=S.shape.handles["start"===this.handleId?"end":"start"];if(this.startBindingShapeId){let e,t=this.app.page.shapes[this.startBindingShapeId],i=ti.getShapeUtil(t),o=i.getCenter(t),r=S.shape.handles.start,d=S.shape.handles.end,p=h.l.add(r.point,S.shape.point);h.l.isEqual(p,o)&&p[1]++;let u=i.hitTestPoint(t,a),g=h.l.uni(h.l.sub(p,o)),m=void 0!==this.app.getBinding(this.newStartBindingId);l||i.hitTestPoint(t,h.l.add(S.shape.point,d.point))||(e=this.findBindingPoint(c,t,"start",this.newStartBindingId,o,o,g,u)),e&&!m?(this.didBind=!0,S.bindings[this.newStartBindingId]=e,S.shape=s.Aq.deepMerge(S.shape,{handles:{start:{bindingId:e.id}}})):!e&&m&&(this.didBind=!1,S.bindings[this.newStartBindingId]=void 0,S.shape=s.Aq.deepMerge(n,{handles:{start:{bindingId:void 0}}}))}if(!l){let e=h.l.add(w.point,S.shape.point),t=h.l.add(k.point,S.shape.point),i=h.l.uni(h.l.sub(t,e)),s=h.l.add(S.shape.point,S.shape.handles.start.point),n=h.l.add(S.shape.point,S.shape.handles.end.point);for(let a of this.bindableShapeIds.map(e=>this.app.page.shapes[e]).sort((e,t)=>t.childIndex-e.childIndex).filter(e=>{if(e.isLocked)return!1;let t=ti.getShapeUtil(e);return![s,n].every(i=>t.hitTestPoint(e,i))}))if(I=this.findBindingPoint(c,a,this.handleId,this.draggedBindingId,t,e,i,r))break}if(I)this.didBind=!0,S.bindings[this.draggedBindingId]=I,S.shape=s.Aq.deepMerge(S.shape,{handles:{[this.handleId]:{bindingId:this.draggedBindingId}}});else{this.didBind=this.didBind||!1;let e=c.handles[this.handleId].bindingId;void 0!==e&&(S.bindings[e]=void 0,S.shape=s.Aq.deepMerge(S.shape,{handles:{[this.handleId]:{bindingId:void 0}}}))}let x=null==(i=(t=ti.getShapeUtil(S.shape)).onHandleChange)?void 0:i.call(t,S.shape,S.shape.handles);return{document:{pages:{[this.app.currentPageId]:{shapes:{[c.id]:C(C({},S.shape),null!=x?x:{})},bindings:S.bindings}},pageStates:{[this.app.currentPageId]:{bindingId:S.shape.handles[g].bindingId}}}}}),B(this,"cancel",()=>{let{initialShape:e,initialBinding:t,newStartBindingId:i,draggedBindingId:s}=this,n=ti.onSessionComplete(this.app.page.shapes[e.id]),a=this.isCreate||4>h.l.dist(n.handles.start.point,n.handles.end.point),o={};return o[s]=void 0,t&&(o[t.id]=a?void 0:t),i&&(o[i]=void 0),{document:{pages:{[this.app.currentPageId]:{shapes:{[e.id]:a?void 0:e},bindings:o}},pageStates:{[this.app.currentPageId]:{selectedIds:a?[]:[e.id],bindingId:void 0,hoveredId:void 0,editingId:void 0}}}}}),B(this,"complete",()=>{let{initialShape:e,initialBinding:t,newStartBindingId:i,startBindingShapeId:s,handleId:n}=this,a=ti.onSessionComplete(this.app.page.shapes[e.id]),o=a.handles[n].bindingId,r=h.l.dist(a.handles.start.point,a.handles.end.point);if(!(o||t)&&r<4)return this.cancel();let l={},d={};return t&&(l[t.id]=this.isCreate?void 0:t,d[t.id]=void 0),o&&(l[o]=void 0,d[o]=this.app.page.bindings[o]),s&&(l[i]=void 0,d[i]=this.app.page.bindings[i]),{id:"arrow",before:{document:{pages:{[this.app.currentPageId]:{shapes:{[e.id]:this.isCreate?void 0:e},bindings:l}},pageStates:{[this.app.currentPageId]:{selectedIds:this.isCreate?[]:[e.id],bindingId:void 0,hoveredId:void 0,editingId:void 0}}}},after:{document:{pages:{[this.app.currentPageId]:{shapes:{[e.id]:a},bindings:d}},pageStates:{[this.app.currentPageId]:{selectedIds:[e.id],bindingId:void 0,hoveredId:void 0,editingId:void 0}}}}}}),B(this,"findBindingPoint",(e,t,i,s,n,a,o,r)=>{let l=ti.getShapeUtil(t.type).getBindingPoint(t,e,n,a,o,r);if(l)return{id:s,type:"arrow",fromId:e.id,toId:t.id,handleId:i,point:h.l.toFixed(l.point),distance:l.distance}}),this.isCreate=n;let{currentPageId:l}=e.state.appState,d=e.state.document.pages[l];this.handleId=i,this.initialShape=e6(d.shapes[t]),this.bindableShapeIds=ti.getBindableShapeIds(e.state).filter(e=>e!==this.initialShape.id&&e!==this.initialShape.parentId);let p=null==(a=this.initialShape.handles["start"===i?"end":"start"])?void 0:a.bindingId;if(p){let e=null==(o=d.bindings[p])?void 0:o.toId;e&&(this.bindableShapeIds=this.bindableShapeIds.filter(t=>t!==e))}let{originPoint:c}=this.app;if(this.isCreate)this.startBindingShapeId=null==(r=this.bindableShapeIds.map(e=>d.shapes[e]).filter(e=>!e.isLocked&&s.Aq.pointInBounds(c,ti.getShapeUtil(e).getBounds(e))).sort((e,t)=>t.childIndex-e.childIndex)[0])?void 0:r.id,this.startBindingShapeId&&this.bindableShapeIds.splice(this.bindableShapeIds.indexOf(this.startBindingShapeId),1);else{let e=this.initialShape.handles[this.handleId].bindingId;e?this.initialBinding=d.bindings[e]:this.initialShape.handles[this.handleId].bindingId=void 0}}},iN=class extends iK{constructor(e){super(e),B(this,"type","brush"),B(this,"performanceMode"),B(this,"status","brushing"),B(this,"initialSelectedIds"),B(this,"shapesToTest"),B(this,"start",()=>{}),B(this,"update",()=>{let{initialSelectedIds:e,shapesToTest:t,app:{metaKey:i,settings:n,originPoint:a,currentPoint:o}}=this,r=s.Aq.getBoundsFromPoints([a,o]),l=n.isCadSelectMode?!i&&a[0]<o[0]:i,d=new Set,h=new Set(e);t.forEach(({id:e,selectId:t})=>{let i=this.app.getShape(e);if(!d.has(t)){let e=this.app.getShapeUtil(i);(l?s.Aq.boundsContain(r,e.getBounds(i)):e.hitTestBounds(i,r))?(d.add(t),h.has(t)||h.add(t)):h.has(t)&&h.delete(t)}});let p=this.app.selectedIds,c=h.size!==p.length||p.some(e=>!h.has(e)),u=c?Array.from(h.values()):p;return c?{appState:{selectByContain:l},document:{pageStates:{[this.app.currentPageId]:{brush:r,selectedIds:u}}}}:{appState:{selectByContain:l},document:{pageStates:{[this.app.currentPageId]:{brush:r}}}}}),B(this,"cancel",()=>({appState:{selectByContain:!1},document:{pageStates:{[this.app.currentPageId]:{brush:null,selectedIds:Array.from(this.initialSelectedIds.values())}}}})),B(this,"complete",()=>({appState:{selectByContain:!1},document:{pageStates:{[this.app.currentPageId]:{brush:null,selectedIds:[...this.app.selectedIds]}}}}));let{currentPageId:t}=e;this.initialSelectedIds=new Set(this.app.selectedIds),this.shapesToTest=this.app.shapes.filter(e=>!(e.isLocked||e.isHidden||e.parentId!==t||this.initialSelectedIds.has(e.id)||this.initialSelectedIds.has(e.parentId))).map(e=>({id:e.id,bounds:this.app.getShapeUtil(e).getBounds(e),selectId:e.id})),this.update()}},iW=class extends iK{constructor(e,t){var i;super(e),B(this,"type","draw"),B(this,"performanceMode"),B(this,"status","creating"),B(this,"topLeft"),B(this,"points"),B(this,"initialShape"),B(this,"lastAdjustedPoint"),B(this,"shiftedPoints",[]),B(this,"shapeId"),B(this,"isLocked"),B(this,"isExtending"),B(this,"lockedDirection"),B(this,"start",()=>{var e;let t=this.app.originPoint,i=[0,0,null!=(e=t[2])?e:.5];this.points.push(i);let s=[Math.min(this.topLeft[0],t[0]),Math.min(this.topLeft[1],t[1])],n=h.l.sub(s,t);return this.topLeft=s,this.shiftedPoints=this.points.map(e=>h.l.toFixed(h.l.sub(e,n)).concat(e[2])),{document:{pages:{[this.app.currentPageId]:{shapes:{[this.shapeId]:{point:this.topLeft,points:this.shiftedPoints}}}},pageStates:{[this.app.currentPageId]:{selectedIds:[this.shapeId]}}}}}),B(this,"update",()=>{let{shapeId:e}=this,{currentPoint:t,originPoint:i,shiftKey:s,zoom:n}=this.app;if(!this.lockedDirection&&this.points.length>1){let e=h.l.sub(t,i);h.l.len(e)>3/n&&(this.lockedDirection=Math.abs(e[0])>Math.abs(e[1])?"horizontal":"vertical")}if(s){if(!this.isLocked&&this.points.length>2){if(!this.lockedDirection){let e=h.l.sub(t,i);h.l.len(e)>3/n&&(this.lockedDirection=Math.abs(e[0])>Math.abs(e[1])?"horizontal":"vertical")}this.isLocked=!0;let e=[...this.lastAdjustedPoint];"vertical"===this.lockedDirection?e[0]=0:e[1]=0,this.points.push(e.concat(t[2]))}}else this.isLocked&&(this.isLocked=!1);this.isLocked&&("vertical"===this.lockedDirection?t[0]=i[0]:t[1]=i[1]);let a=this.addPoint(t);if(a)return{document:{pages:{[this.app.currentPageId]:{shapes:{[e]:a}}},pageStates:{[this.app.currentPageId]:{selectedIds:[e]}}}}}),B(this,"cancel",()=>{let{shapeId:e}=this,t=this.app.currentPageId;return{document:{pages:{[t]:{shapes:{[e]:this.isExtending?this.initialShape:void 0}}},pageStates:{[t]:{selectedIds:[]}}}}}),B(this,"complete",()=>{let{shapeId:e}=this,t=this.app.currentPageId,i=this.app.getShape(e);return{id:"create_draw",before:{document:{pages:{[t]:{shapes:{[e]:this.isExtending?this.initialShape:void 0}}},pageStates:{[t]:{selectedIds:[]}}}},after:{document:{pages:{[t]:{shapes:{[e]:A(C({},i),{point:h.l.toFixed(i.point),points:i.points.map(e=>h.l.toFixed(e)),isComplete:!0})}}},pageStates:{[this.app.currentPageId]:{selectedIds:[]}}}}}}),B(this,"addPoint",e=>{let{originPoint:t}=this.app,i=h.l.toFixed(h.l.sub(e,t)).concat(e[2]);if(h.l.isEqual(this.lastAdjustedPoint,i))return;this.points.push(i),this.lastAdjustedPoint=i;let s=[...this.topLeft],n=[Math.min(this.topLeft[0],e[0]),Math.min(this.topLeft[1],e[1])],a=h.l.sub(n,t),o;return s[0]!==n[0]||s[1]!==n[1]?(this.topLeft=n,o=this.points.map(e=>h.l.toFixed(h.l.sub(e,a)).concat(e[2]))):o=[...this.shiftedPoints,h.l.sub(i,a).concat(i[2])],this.shiftedPoints=o,{point:this.topLeft,points:o}});let{originPoint:s}=this.app;this.shapeId=t,this.initialShape=this.app.getShape(t),this.topLeft=[...this.initialShape.point];let n=[0,0,null!=(i=s[2])?i:.5],a=h.l.sub(s,this.topLeft),o=this.initialShape.points.map(e=>h.l.sub(e,a).concat(e[2]));this.isExtending=o.length>0;let r=[];if(this.isExtending){let e=o[o.length-1];if(e){r.push(e,e);let t=Math.floor(h.l.dist(e,n)/16);if(t>1)for(let i=0;i<t;i++){let s=i/(t-1);r.push(h.l.lrp(e,n,s).concat(e[2]))}else r.push(n,n)}}else r.push(n);this.points=[...o,...r],this.shiftedPoints=this.points.map(e=>h.l.add(e,a).concat(e[2])),this.lastAdjustedPoint=this.points[this.points.length-1]}},iY=class extends iK{constructor(e,t,i){super(e),B(this,"type","edit"),B(this,"performanceMode"),B(this,"initialShape"),B(this,"initialSelectedIds"),B(this,"currentPageId"),B(this,"isCreating"),B(this,"start",()=>{}),B(this,"update",()=>{}),B(this,"cancel",()=>({document:{pages:{[this.currentPageId]:{shapes:{[this.initialShape.id]:this.isCreating?void 0:this.initialShape}}},pageStates:{[this.currentPageId]:{selectedIds:this.isCreating?[]:this.initialSelectedIds,editingId:void 0}}}})),B(this,"complete",()=>{let e=this.app.getShape(this.initialShape.id);return{id:"edit",before:{document:{pages:{[this.currentPageId]:{shapes:{[this.initialShape.id]:this.isCreating?void 0:this.initialShape}}},pageStates:{[this.currentPageId]:{selectedIds:this.isCreating?[]:this.initialSelectedIds,editingId:void 0}}}},after:{document:{pages:{[this.currentPageId]:{shapes:{[this.initialShape.id]:e}}},pageStates:{[this.currentPageId]:{selectedIds:[e.id],editingId:void 0}}}}}}),this.initialShape=e.getShape(t,e.currentPageId),this.currentPageId=e.currentPageId,this.isCreating=i,this.initialSelectedIds=[...e.selectedIds]}},i$=class extends iK{constructor(e){super(e),B(this,"type","draw"),B(this,"performanceMode"),B(this,"status","creating"),B(this,"isLocked"),B(this,"lockedDirection"),B(this,"erasedShapes",new Set),B(this,"erasedBindings",new Set),B(this,"initialSelectedShapes"),B(this,"erasableShapes"),B(this,"prevPoint"),B(this,"prevEraseShapesSize",0),B(this,"interval"),B(this,"timestamp1",0),B(this,"timestamp2",0),B(this,"prevErasePoint",[]),B(this,"loop",()=>{let e=Date.now(),t=e-this.timestamp1,i=e-this.timestamp2,{eraseLine:s}=this.app.appState,n=[...s],a=!1;t>16&&this.prevErasePoint!==this.prevPoint&&(a=!0,n=[...s,this.prevPoint],this.prevErasePoint=this.prevPoint),i>32&&n.length>1&&(a=!0,n.splice(0,Math.ceil(.1*n.length)),this.timestamp2=e),a&&this.app.patchState({appState:{eraseLine:n}},"eraseline"),this.interval=requestAnimationFrame(this.loop)}),B(this,"start",()=>{}),B(this,"update",()=>{let{page:e,shiftKey:t,originPoint:i,currentPoint:s,zoom:n}=this.app;if(t){let e=h.l.sub(s,i);if(!this.isLocked&&h.l.len(e)>3/n){if(!this.lockedDirection){let e=h.l.sub(s,i);this.lockedDirection=Math.abs(e[0])>Math.abs(e[1])?"horizontal":"vertical"}this.isLocked=!0}}else this.isLocked&&(this.isLocked=!1);this.isLocked&&("vertical"===this.lockedDirection?s[0]=i[0]:s[1]=i[1]);let a=h.l.toFixed(h.l.add(i,h.l.sub(s,i))),o=new Set([]);this.erasableShapes.forEach(e=>{if(!this.erasedShapes.has(e)&&this.app.getShapeUtil(e).hitTestLineSegment(e,this.prevPoint,a)&&(this.erasedShapes.add(e),o.add(e.id),void 0!==e.children))for(let t of e.children)this.erasedShapes.add(this.app.getShape(t)),o.add(t)}),Object.values(e.bindings).forEach(e=>{for(let t of[e.toId,e.fromId])o.has(t)&&this.erasedBindings.add(e)}),this.erasedShapes.forEach(e=>{this.app.getShape(e.id)||(this.erasedShapes.delete(e),this.erasableShapes.delete(e),o.delete(e.id))});let r=Array.from(this.erasedShapes.values());if(this.prevPoint=a,r.length!==this.prevEraseShapesSize)return this.prevEraseShapesSize=r.length,{document:{pages:{[e.id]:{shapes:Object.fromEntries(r.map(e=>[e.id,{isGhost:!0}]))}}}}}),B(this,"cancel",()=>{let{page:e}=this.app;cancelAnimationFrame(this.interval),this.erasedShapes.forEach(e=>{this.app.getShape(e.id)||(this.erasedShapes.delete(e),this.erasableShapes.delete(e))});let t=Array.from(this.erasedShapes.values());return{document:{pages:{[e.id]:{shapes:Object.fromEntries(t.map(e=>[e.id,{isGhost:!1}]))}},pageStates:{[e.id]:{selectedIds:this.initialSelectedShapes.map(e=>e.id)}}},appState:{eraseLine:[]}}}),B(this,"complete",()=>{let{page:e}=this.app;cancelAnimationFrame(this.interval),this.erasedShapes.forEach(e=>{this.app.getShape(e.id)||(this.erasedShapes.delete(e),this.erasableShapes.delete(e))}),this.erasedBindings.forEach(e=>{this.app.getBinding(e.id)||this.erasedBindings.delete(e)});let t=Array.from(this.erasedShapes.values()),i=Array.from(this.erasedBindings.values()),s=t.map(e=>e.id),n=i.map(e=>e.id),a={shapes:Object.fromEntries(t.map(e=>[e.id,e])),bindings:Object.fromEntries(i.map(e=>[e.id,e]))},o={shapes:Object.fromEntries(t.map(e=>[e.id,void 0])),bindings:Object.fromEntries(i.map(e=>[e.id,void 0]))};return this.app.shapes.forEach(e=>{e.handles&&!o.shapes[e.id]&&Object.values(e.handles).forEach(t=>{var i,r;t.bindingId&&n.includes(t.bindingId)&&(a.shapes[e.id]=A(C({},a.shapes[e.id]),{handles:A(C({},null==(i=a.shapes[e.id])?void 0:i.handles),{[t.id]:t})}),s.includes(e.id)||(o.shapes[e.id]=A(C({},o.shapes[e.id]),{handles:A(C({},null==(r=o.shapes[e.id])?void 0:r.handles),{[t.id]:A(C({},t),{bindingId:void 0})})})))})}),{id:"erase",before:{document:{pages:{[e.id]:a},pageStates:{[e.id]:{selectedIds:this.initialSelectedShapes.filter(e=>!!this.app.getShape(e.id)).map(e=>e.id)}}},appState:{eraseLine:[]}},after:{document:{pages:{[e.id]:o},pageStates:{[e.id]:{selectedIds:this.initialSelectedShapes.filter(e=>!!this.app.getShape(e.id)).filter(e=>!s.includes(e.id)).map(e=>e.id)}}},appState:{eraseLine:[]}}}}),this.prevPoint=[...e.originPoint],this.initialSelectedShapes=this.app.selectedIds.map(e=>this.app.getShape(e)),this.erasableShapes=new Set(this.app.shapes.filter(e=>!e.isLocked)),this.interval=this.loop()}},iG=class extends iK{constructor(e,t){var i;super(e),B(this,"type","grid"),B(this,"performanceMode"),B(this,"status","translating"),B(this,"shape"),B(this,"bounds"),B(this,"initialSelectedIds"),B(this,"initialSiblings"),B(this,"grid",{}),B(this,"columns",1),B(this,"rows",1),B(this,"isCopying",!1),B(this,"start",()=>{}),B(this,"update",()=>{let{currentPageId:e,altKey:t,shiftKey:i,currentPoint:n}=this.app,a={},o=s.Aq.getBoundsCenter(this.bounds),r=h.l.sub(n,o);i&&(Math.abs(r[0])<Math.abs(r[1])?r[0]=0:r[1]=0);let l=this.bounds.width+32,d=this.bounds.height+32,p=Math.ceil(r[0]/l),c=Math.ceil(r[1]/d),u=Math.min(p,0),g=Math.min(c,0),m=Math.max(p,1),f=Math.max(c,1),v=new Set;t!==this.isCopying&&(Object.values(this.grid).filter(e=>e!==this.shape.id).forEach(e=>a[e]=void 0),this.grid={"0_0":this.shape.id},this.isCopying=t);for(let e=u;e<m;e++)for(let i=g;i<f;i++){let s=`${e}_${i}`;if(v.add(s),this.grid[s]||0===e&&0===i)continue;let n=this.getClone(h.l.add(this.shape.point,[e*l,i*d]),t);a[n.id]=n,this.grid[s]=n.id}if(Object.entries(this.grid).forEach(([e,t])=>{v.has(e)||(a[t]=void 0,delete this.grid[e])}),0!==Object.values(a).length)return this.initialSiblings&&(a[this.shape.parentId]={children:[...this.initialSiblings,...Object.values(this.grid)]}),{document:{pages:{[e]:{shapes:a}},pageStates:{[e]:{}}}}}),B(this,"cancel",()=>{let{currentPageId:e}=this.app,t={};return Object.values(this.grid).forEach(e=>{t[e]=void 0}),t[this.shape.id]=A(C({},t[this.shape.id]),{point:this.shape.point}),this.initialSiblings&&(t[this.shape.parentId]={children:[...this.initialSiblings,this.shape.id]}),{document:{pages:{[e]:{shapes:t}},pageStates:{[e]:{selectedIds:[this.shape.id]}}}}}),B(this,"complete",()=>{let{currentPageId:e}=this.app,t={},i={},s=[];if(Object.values(this.grid).forEach(e=>{t[e]=void 0,i[e]=this.app.getShape(e),s.push(e)}),t[this.shape.id]=this.shape,this.initialSiblings&&(t[this.shape.parentId]={children:[...this.initialSiblings,this.shape.id]},i[this.shape.parentId]={children:[...this.initialSiblings,...Object.values(this.grid)]}),1!==s.length)return{id:"grid",before:{document:{pages:{[e]:{shapes:t}},pageStates:{[e]:{selectedIds:[],hoveredId:void 0}}}},after:{document:{pages:{[e]:{shapes:i}},pageStates:{[e]:{selectedIds:s,hoveredId:void 0}}}}}}),B(this,"getClone",(e,t)=>{let i=A(C({},this.shape),{id:s.Aq.uniqueId(),point:e});return t||"sticky"===i.type&&(i.text=""),i}),this.shape=this.app.getShape(t),this.grid["0_0"]=this.shape.id,this.bounds=this.app.getShapeBounds(t),this.initialSelectedIds=[...this.app.selectedIds],this.shape.parentId!==this.app.currentPageId&&(this.initialSiblings=null==(i=this.app.getShape(this.shape.parentId).children)?void 0:i.filter(e=>e!==this.shape.id))}},iX={arrow:i_,brush:iN,draw:iW,erase:i$,handle:class extends iK{constructor(e,t,i,s="move_handle"){super(e),B(this,"type","handle"),B(this,"performanceMode"),B(this,"status","translatingHandle"),B(this,"commandId"),B(this,"topLeft"),B(this,"shiftKey",!1),B(this,"initialShape"),B(this,"handleId"),B(this,"start",()=>{}),B(this,"update",()=>{var e,t;let{initialShape:i,app:{currentPageId:s,currentPoint:n}}=this,a=this.app.getShape(i.id);if(a.isLocked)return;let o=a.handles,r=this.handleId,l=h.l.sub(n,o[r].point),d={[r]:A(C({},o[r]),{point:h.l.sub(h.l.add(o[r].point,l),a.point)})},p=null==(t=(e=ti.getShapeUtil(a)).onHandleChange)?void 0:t.call(e,a,d);if(p)return{document:{pages:{[s]:{shapes:{[a.id]:p}}}}}}),B(this,"cancel",()=>{let{initialShape:e,app:{currentPageId:t}}=this;return{document:{pages:{[t]:{shapes:{[e.id]:e}}}}}}),B(this,"complete",()=>{let{initialShape:e,app:{currentPageId:t}}=this;return{id:this.commandId,before:{document:{pages:{[t]:{shapes:{[e.id]:e}}}}},after:{document:{pages:{[t]:{shapes:{[e.id]:ti.onSessionComplete(this.app.getShape(this.initialShape.id))}}}}}}});let{originPoint:n}=e;this.topLeft=[...n],this.handleId=i,this.initialShape=this.app.getShape(t),this.commandId=s}},rotate:class extends iK{constructor(e){super(e),B(this,"type","rotate"),B(this,"status","transforming"),B(this,"performanceMode"),B(this,"delta",[0,0]),B(this,"commonBoundsCenter"),B(this,"initialAngle"),B(this,"initialShapes"),B(this,"changes",{}),B(this,"start",()=>{}),B(this,"update",()=>{let{commonBoundsCenter:e,initialShapes:t,app:{currentPageId:i,currentPoint:n,shiftKey:a}}=this,o={},r=h.l.angle(e,n)-this.initialAngle;return a&&(r=s.Aq.snapAngleToSegments(r,24)),t.forEach(({center:t,shape:i})=>{let{rotation:n=0}=i,l=0;a&&(l=s.Aq.snapAngleToSegments(n,24)-n);let d=ti.getRotatedShapeMutation(i,t,e,a?r+l:r);d&&(o[i.id]=d)}),this.changes=o,{document:{pages:{[i]:{shapes:o}}}}}),B(this,"cancel",()=>{let{initialShapes:e,app:{currentPageId:t}}=this,i={};return e.forEach(({shape:e})=>i[e.id]=e),{document:{pages:{[t]:{shapes:i}}}}}),B(this,"complete",()=>{let{initialShapes:e,app:{currentPageId:t}}=this,i={},s=this.changes;return e.forEach(({shape:{id:e,point:t,rotation:s,handles:n}})=>{i[e]={point:t,rotation:s,handles:n}}),{id:"rotate",before:{document:{pages:{[t]:{shapes:i}}}},after:{document:{pages:{[t]:{shapes:s}}}}}});let{app:{currentPageId:t,pageState:i,originPoint:n}}=this,a=ti.getSelectedBranchSnapshot(e.state,t).filter(e=>!e.isLocked);if(0===a.length)throw Error("No selected shapes!");if(e.rotationInfo.selectedIds===i.selectedIds){if(void 0===e.rotationInfo.center)throw Error("We should have a center for rotation!");this.commonBoundsCenter=e.rotationInfo.center}else this.commonBoundsCenter=s.Aq.getBoundsCenter(s.Aq.getCommonBounds(a.map(ti.getBounds))),e.rotationInfo.selectedIds=i.selectedIds,e.rotationInfo.center=this.commonBoundsCenter;this.initialShapes=a.filter(e=>void 0===e.children).map(e=>({shape:e,center:this.app.getShapeUtil(e).getCenter(e)})),this.initialAngle=h.l.angle(this.commonBoundsCenter,n)}},transform:class extends iK{constructor(e,t=s.NW.BottomRight,i=!1){super(e),this.transformType=t,this.isCreate=i,B(this,"type","transform"),B(this,"performanceMode"),B(this,"status","transforming"),B(this,"scaleX",1),B(this,"scaleY",1),B(this,"initialShapes"),B(this,"initialShapeIds"),B(this,"initialSelectedIds"),B(this,"shapeBounds"),B(this,"hasUnlockedShapes"),B(this,"isAllAspectRatioLocked"),B(this,"initialCommonBounds"),B(this,"snapInfo",{state:"empty"}),B(this,"prevPoint",[0,0]),B(this,"speed",1),B(this,"start",()=>{this.snapInfo={state:"ready",bounds:this.app.shapes.filter(e=>!this.initialShapeIds.includes(e.id)).map(e=>s.Aq.getBoundsWithCenter(ti.getRotatedBounds(e)))}}),B(this,"update",()=>{let{transformType:e,shapeBounds:t,initialCommonBounds:i,isAllAspectRatioLocked:n,app:{currentPageId:a,pageState:{camera:o},viewport:r,currentPoint:l,previousPoint:d,originPoint:p,shiftKey:c,altKey:u,metaKey:g,currentGrid:m,settings:{isSnapping:f,showGrid:v}}}=this,b={},y=u?h.l.mul(h.l.sub(l,p),2):h.l.sub(l,p),S=s.Aq.getTransformedBoundingBox(i,e,y,0,c||n);u&&(S=C(C({},S),s.Aq.centerBounds(S,s.Aq.getBoundsCenter(i)))),v&&(S=C(C({},S),s.Aq.snapBoundsToGrid(S,m)));let I=h.l.dist(l,d)-this.speed;this.speed=this.speed+I*(I>1?.5:.15);let k=[];if((f&&!g||!f&&g)&&this.speed*o.zoom<10&&"ready"===this.snapInfo.state){let t=s.Aq.getSnapPoints(s.Aq.getBoundsWithCenter(S),this.snapInfo.bounds.filter(e=>s.Aq.boundsContain(r,e)||s.Aq.boundsCollide(r,e)),5/o.zoom);t&&(k=t.snapLines,S=s.Aq.getTransformedBoundingBox(i,e,h.l.sub(y,t.offset),0,c||n))}return this.scaleX=S.scaleX,this.scaleY=S.scaleY,t.forEach(({initialShape:e,initialShapeBounds:t,transformOrigin:n})=>{let a=s.Aq.getRelativeTransformedBoundingBox(S,i,t,this.scaleX<0,this.scaleY<0);v&&(a=s.Aq.snapBoundsToGrid(a,m));let o=ti.transform(this.app.getShape(e.id),a,{type:this.transformType,initialShape:e,scaleX:this.scaleX,scaleY:this.scaleY,transformOrigin:n});b[e.id]=o}),{appState:{snapLines:k},document:{pages:{[a]:{shapes:b}}}}}),B(this,"cancel",()=>{let{shapeBounds:e,app:{currentPageId:t}}=this,i={};return this.isCreate?e.forEach(e=>i[e.initialShape.id]=void 0):e.forEach(e=>i[e.initialShape.id]=e.initialShape),{appState:{snapLines:[]},document:{pages:{[t]:{shapes:i}},pageStates:{[t]:{selectedIds:this.isCreate?[]:e.map(e=>e.initialShape.id)}}}}}),B(this,"complete",()=>{let{isCreate:e,shapeBounds:t,hasUnlockedShapes:i,app:{currentPageId:s}}=this;if(!i)return;if(this.isCreate&&2>h.l.dist(this.app.originPoint,this.app.currentPoint))return this.cancel();let n={},a={},o,r;return e?(o=[],r=[],t.forEach(({initialShape:e})=>{n[e.id]=void 0,a[e.id]=this.app.getShape(e.id)})):(o=this.initialSelectedIds,r=this.initialSelectedIds,t.forEach(({initialShape:e})=>{n[e.id]=e,a[e.id]=this.app.getShape(e.id)})),{id:"transform",before:{appState:{snapLines:[]},document:{pages:{[s]:{shapes:n}},pageStates:{[s]:{selectedIds:o,hoveredId:void 0,editingId:void 0}}}},after:{appState:{snapLines:[]},document:{pages:{[s]:{shapes:a}},pageStates:{[s]:{selectedIds:r,hoveredId:void 0,editingId:void 0}}}}}}),this.initialSelectedIds=[...this.app.selectedIds],this.app.rotationInfo.selectedIds=[...this.initialSelectedIds],this.initialShapes=ti.getSelectedBranchSnapshot(this.app.state,this.app.currentPageId).filter(e=>!e.isLocked),this.initialShapeIds=this.initialShapes.map(e=>e.id),this.hasUnlockedShapes=this.initialShapes.length>0,this.isAllAspectRatioLocked=this.initialShapes.every(e=>e.isAspectRatioLocked||ti.getShapeUtil(e).isAspectRatioLocked);let n=Object.fromEntries(this.initialShapes.map(e=>[e.id,ti.getBounds(e)])),a=Object.values(n);this.initialCommonBounds=s.Aq.getCommonBounds(a);let o=s.Aq.getBoundsFromPoints(a.map(s.Aq.getBoundsCenter));this.shapeBounds=this.initialShapes.map(e=>{let t=n[e.id],i=s.Aq.getBoundsCenter(t);return{initialShape:e,initialShapeBounds:t,transformOrigin:[(i[0]-o.minX)/o.width,(i[1]-o.minY)/o.height]}})}},transformSingle:class extends iK{constructor(e,t,i,n=!1){super(e),B(this,"type","transformSingle"),B(this,"status","transforming"),B(this,"performanceMode"),B(this,"transformType"),B(this,"scaleX",1),B(this,"scaleY",1),B(this,"isCreate"),B(this,"initialShape"),B(this,"initialShapeBounds"),B(this,"initialCommonBounds"),B(this,"snapInfo",{state:"empty"}),B(this,"prevPoint",[0,0]),B(this,"speed",1),B(this,"start",()=>{this.snapInfo={state:"ready",bounds:this.app.shapes.filter(e=>e.id!==this.initialShape.id).map(e=>s.Aq.getBoundsWithCenter(ti.getRotatedBounds(e)))}}),B(this,"update",()=>{let{transformType:e,initialShape:t,initialShapeBounds:i,app:{settings:{isSnapping:n,showGrid:a},currentPageId:o,pageState:{camera:r},viewport:l,currentPoint:d,previousPoint:p,originPoint:c,currentGrid:u,shiftKey:g,altKey:m,metaKey:f}}=this;if(t.isLocked)return;let v={},b=m?h.l.mul(h.l.sub(d,c),2):h.l.sub(d,c),y=this.app.getShape(t.id),S=ti.getShapeUtil(y),I=s.Aq.getTransformedBoundingBox(i,e,b,y.rotation,g||y.isAspectRatioLocked||S.isAspectRatioLocked);m&&(I=C(C({},I),s.Aq.centerBounds(I,s.Aq.getBoundsCenter(i)))),a&&(I=C(C({},I),s.Aq.snapBoundsToGrid(I,u)));let k=h.l.dist(d,p)-this.speed;this.speed=this.speed+k*(k>1?.5:.15);let w=[];if((n&&!f||!n&&f)&&!t.rotation&&this.speed*r.zoom<10&&"ready"===this.snapInfo.state){let t=s.Aq.getSnapPoints(s.Aq.getBoundsWithCenter(I),this.snapInfo.bounds.filter(e=>s.Aq.boundsContain(l,e)||s.Aq.boundsCollide(l,e)),5/r.zoom);t&&(w=t.snapLines,I=s.Aq.getTransformedBoundingBox(i,e,h.l.sub(b,t.offset),y.rotation,g||y.isAspectRatioLocked||S.isAspectRatioLocked))}let x=ti.getShapeUtil(y).transformSingle(y,I,{initialShape:t,type:this.transformType,scaleX:I.scaleX,scaleY:I.scaleY,transformOrigin:[.5,.5]});return x&&(v[y.id]=x),a&&x&&x.point&&(x.point=h.l.snap(x.point,u)),{appState:{snapLines:w},document:{pages:{[o]:{shapes:v}}}}}),B(this,"cancel",()=>{let{initialShape:e,app:{currentPageId:t}}=this,i={};return this.isCreate?i[e.id]=void 0:i[e.id]=e,{appState:{snapLines:[]},document:{pages:{[t]:{shapes:i}},pageStates:{[t]:{selectedIds:this.isCreate?[]:[e.id]}}}}}),B(this,"complete",()=>{let{initialShape:e,app:{currentPageId:t}}=this;if(e.isLocked)return;if(this.isCreate&&2>h.l.dist(this.app.originPoint,this.app.currentPoint))return this.cancel();let i={},s={};return i[e.id]=this.isCreate?void 0:e,s[e.id]=ti.onSessionComplete(this.app.getShape(e.id)),{id:"transform_single",before:{appState:{snapLines:[]},document:{pages:{[t]:{shapes:i}},pageStates:{[t]:{selectedIds:this.isCreate?[]:[e.id],editingId:void 0,hoveredId:void 0}}}},after:{appState:{snapLines:[]},document:{pages:{[t]:{shapes:s}},pageStates:{[t]:{selectedIds:[e.id],editingId:void 0,hoveredId:void 0}}}}}}),this.isCreate=n,this.transformType=i;let a=this.app.getShape(t);this.initialShape=a,this.initialShapeBounds=ti.getBounds(a),this.initialCommonBounds=ti.getRotatedBounds(a),this.app.rotationInfo.selectedIds=[a.id]}},translate:class extends iK{constructor(e,t=!1,i=!1){super(e),B(this,"performanceMode"),B(this,"type","translate"),B(this,"status","translating"),B(this,"delta",[0,0]),B(this,"prev",[0,0]),B(this,"prevPoint",[0,0]),B(this,"speed",1),B(this,"cloneInfo",{state:"empty"}),B(this,"snapInfo",{state:"empty"}),B(this,"snapLines",[]),B(this,"isCloning",!1),B(this,"isCreate"),B(this,"link"),B(this,"initialIds"),B(this,"hasUnlockedShapes"),B(this,"initialSelectedIds"),B(this,"initialCommonBounds"),B(this,"initialShapes"),B(this,"initialParentChildren"),B(this,"bindingsToDelete"),B(this,"start",()=>{let{bindingsToDelete:e,initialIds:t,app:{currentPageId:i,page:n}}=this,a=[],o=[];if(Object.values(n.shapes).forEach(e=>{let i=s.Aq.getBoundsWithCenter(ti.getRotatedBounds(e));a.push(i),t.has(e.id)||o.push(i)}),this.snapInfo={state:"ready",bounds:a,others:o},0===e.length)return;let r={};return e.forEach(e=>r[e.id]=void 0),{document:{pages:{[i]:{bindings:r}}}}}),B(this,"update",()=>{let{initialParentChildren:e,initialShapes:t,initialCommonBounds:i,bindingsToDelete:n,app:{pageState:{camera:a},settings:{isSnapping:o,showGrid:r},currentPageId:l,viewport:d,selectedIds:p,currentPoint:c,previousPoint:u,originPoint:g,altKey:m,shiftKey:f,metaKey:v,currentGrid:b}}=this,y={},S={},I={},k=h.l.sub(c,g),w=!1;this.isCreate||(m&&!this.isCloning?(this.isCloning=!0,w=!0):!m&&this.isCloning&&(this.isCloning=!1,w=!0)),f&&(Math.abs(k[0])<Math.abs(k[1])?k[0]=0:k[1]=0);let x=h.l.dist(c,u)-this.speed;if(this.speed=this.speed+x*(x>1?.5:.15),this.snapLines=[],(o&&!v||!o&&v)&&this.speed*a.zoom<10&&"ready"===this.snapInfo.state){let e=s.Aq.getSnapPoints(s.Aq.getBoundsWithCenter(r?s.Aq.snapBoundsToGrid(s.Aq.translateBounds(i,k),b):s.Aq.translateBounds(i,k)),(this.isCloning?this.snapInfo.bounds:this.snapInfo.others).filter(e=>s.Aq.boundsContain(d,e)||s.Aq.boundsCollide(d,e)),5/a.zoom);e&&(this.snapLines=e.snapLines,k=h.l.sub(k,e.offset))}if(this.prev=k,this.isCloning)if(w){if("empty"===this.cloneInfo.state&&this.createCloneInfo(),"empty"===this.cloneInfo.state)throw Error;let{clones:i,clonedBindings:s}=this.cloneInfo;for(let a of(this.isCloning=!0,n.forEach(e=>y[e.id]=e),t.forEach(e=>S[e.id]={point:e.point}),i.forEach(t=>{var i;if(S[t.id]=C({},t),t.parentId!==l&&!p.includes(t.parentId)){let s=(null==(i=S[t.parentId])?void 0:i.children)||e[t.parentId];s.includes(t.id)||(S[t.parentId]=A(C({},S[t.parentId]),{children:[...s,t.id]}))}}),s))y[a.id]=a;I.selectedIds=i.map(e=>e.id),i.forEach(e=>{S[e.id]=A(C({},e),{point:r?h.l.snap(h.l.toFixed(h.l.add(e.point,k)),b):h.l.toFixed(h.l.add(e.point,k))})})}else{if("empty"===this.cloneInfo.state)throw Error;let{clones:e}=this.cloneInfo;e.forEach(e=>{S[e.id]={point:r?h.l.snap(h.l.toFixed(h.l.add(e.point,k)),b):h.l.toFixed(h.l.add(e.point,k))}})}else if(w){if("empty"===this.cloneInfo.state)throw Error;let{clones:i,clonedBindings:s}=this.cloneInfo;for(let a of(this.isCloning=!1,n.forEach(e=>y[e.id]=void 0),i.forEach(t=>{t.parentId!==l&&(S[t.parentId]=A(C({},S[t.parentId]),{children:e[t.parentId]}))}),i.forEach(e=>S[e.id]=void 0),t.forEach(e=>{S[e.id]={point:r?h.l.snap(h.l.toFixed(h.l.add(e.point,k)),b):h.l.toFixed(h.l.add(e.point,k))}}),s))y[a.id]=void 0;I.selectedIds=t.map(e=>e.id)}else t.forEach(e=>{S[e.id]={point:r?h.l.snap(h.l.toFixed(h.l.add(e.point,k)),b):h.l.toFixed(h.l.add(e.point,k))}});return{appState:{snapLines:this.snapLines},document:{pages:{[l]:{shapes:S,bindings:y}},pageStates:{[l]:I}}}}),B(this,"cancel",()=>{let{initialShapes:e,initialSelectedIds:t,bindingsToDelete:i,app:{currentPageId:s}}=this,n={},a={},o={editingId:void 0,hoveredId:void 0};if(i.forEach(e=>n[e.id]=e),this.isCreate?(e.forEach(({id:e})=>a[e]=void 0),o.selectedIds=[]):(e.forEach(({id:e,point:t})=>a[e]=A(C({},a[e]),{point:t})),o.selectedIds=t),"ready"===this.cloneInfo.state){let{clones:e,clonedBindings:t}=this.cloneInfo;e.forEach(e=>a[e.id]=void 0),t.forEach(e=>n[e.id]=void 0)}return{appState:{snapLines:[]},document:{pages:{[s]:{shapes:a,bindings:n}},pageStates:{[s]:o}}}}),B(this,"complete",()=>{let{initialShapes:e,initialParentChildren:t,bindingsToDelete:i,app:{currentPageId:s}}=this,n={},a={},o={},r={};if(this.isCloning){if("empty"===this.cloneInfo.state&&this.createCloneInfo(),"ready"!==this.cloneInfo.state)throw Error;let{clones:e,clonedBindings:i}=this.cloneInfo;e.forEach(e=>{a[e.id]=void 0,r[e.id]=this.app.getShape(e.id),e.parentId!==s&&(a[e.parentId]=A(C({},a[e.parentId]),{children:t[e.parentId]}),r[e.parentId]=A(C({},r[e.parentId]),{children:this.app.getShape(e.parentId).children}))}),i.forEach(e=>{n[e.id]=void 0,o[e.id]=this.app.getBinding(e.id)})}else e.forEach(e=>{a[e.id]=this.isCreate?void 0:A(C({},a[e.id]),{point:e.point}),r[e.id]=C(C({},r[e.id]),this.isCreate?this.app.getShape(e.id):{point:this.app.getShape(e.id).point})});return i.forEach(e=>{for(let t of(n[e.id]=e,[e.toId,e.fromId])){let i=this.app.getShape(t);i.handles&&Object.values(i.handles).filter(t=>t.bindingId===e.id).forEach(i=>{a[t]=A(C({},a[t]),{handles:{}}),r[t]=A(C({},r[t]),{handles:{}}),a[t].handles[i.id]={bindingId:e.id},r[t].handles[i.id]={bindingId:void 0}})}}),{id:"translate",before:{appState:{snapLines:[]},document:{pages:{[s]:{shapes:a,bindings:n}},pageStates:{[s]:{selectedIds:this.isCreate?[]:[...this.initialSelectedIds]}}}},after:{appState:{snapLines:[]},document:{pages:{[s]:{shapes:r,bindings:o}},pageStates:{[s]:{selectedIds:[...this.app.selectedIds]}}}}}}),B(this,"createCloneInfo",()=>{let{initialShapes:e,initialParentChildren:t,app:{selectedIds:i,currentPageId:n,page:a}}=this,o={},r={},l=[],d=[];e.forEach(e=>{let i=s.Aq.uniqueId();t[i]=t[e.id],o[e.id]=i;let a=A(C({},s.Aq.deepClone(e)),{id:i,parentId:e.parentId,childIndex:ti.getChildIndexAbove(this.app.state,e.id,n)});if("video"===a.type){let t=document.getElementById(e.id+"_video");t&&(a.currentTime=(t.currentTime+16)%t.duration)}d.push(a)}),d.forEach(e=>{void 0!==e.children&&(e.children=e.children.map(e=>o[e]))}),d.forEach(e=>{i.includes(e.parentId)&&(e.parentId=o[e.parentId])});let h=new Set(Object.keys(o));Object.values(a.bindings).filter(e=>h.has(e.fromId)||h.has(e.toId)).forEach(e=>{if(h.has(e.fromId)&&h.has(e.toId)){let t=s.Aq.uniqueId(),i=A(C({},s.Aq.deepClone(e)),{id:t,fromId:o[e.fromId]||e.fromId,toId:o[e.toId]||e.toId});r[e.id]=t,l.push(i)}}),d.forEach(e=>{if(e.handles&&e.handles)for(let t in e.handles){let i=e.handles[t];i.bindingId=i.bindingId?r[i.bindingId]:void 0}}),d.forEach(e=>{if(a.shapes[e.id])throw Error("uh oh, we didn't clone correctly")}),this.cloneInfo={state:"ready",clones:d,cloneMap:o,clonedBindings:l}}),this.isCreate=t,this.link=i;let{currentPageId:n,selectedIds:a,page:o}=this.app;this.initialSelectedIds=[...a];let r=(i?ti.getLinkedShapeIds(this.app.state,n,i,!1):a).map(e=>this.app.getShape(e)).filter(e=>!e.isLocked),l=new Set(r.map(e=>e.id));this.hasUnlockedShapes=r.length>0,this.initialShapes=Array.from(new Set(r.filter(e=>!l.has(e.parentId)).flatMap(e=>e.children?[e,...e.children.map(e=>this.app.getShape(e))]:[e])).values()),this.initialIds=new Set(this.initialShapes.map(e=>e.id)),this.bindingsToDelete=[],Object.values(o.bindings).filter(e=>this.initialIds.has(e.fromId)||this.initialIds.has(e.toId)).forEach(e=>{this.initialIds.has(e.fromId)&&(this.initialIds.has(e.toId)||this.bindingsToDelete.push(e))}),this.initialParentChildren={},this.initialShapes.map(e=>e.parentId).filter(e=>e!==o.id).forEach(e=>{this.initialParentChildren[e]=this.app.getShape(e).children}),this.initialCommonBounds=s.Aq.getCommonBounds(this.initialShapes.map(ti.getRotatedBounds)),this.app.rotationInfo.selectedIds=[...this.app.selectedIds]}},grid:iG,edit:iY},iV=e=>iX[e],iZ=class extends M{constructor(e){super(),this.app=e,B(this,"type","select"),B(this,"previous"),B(this,"status","idle"),B(this,"setStatus",e=>{this.status=e,this.app.setStatus(this.status)}),B(this,"onEnter",()=>{this.setStatus("idle")}),B(this,"onExit",()=>{this.setStatus("idle")}),B(this,"onCancel",()=>{"idle"===this.status?this.app.selectTool("select"):this.setStatus("idle"),this.app.cancelSession()}),B(this,"getNextChildIndex",()=>{let{shapes:e,appState:{currentPageId:t}}=this.app;return 0===e.length?1:e.filter(e=>e.parentId===t).sort((e,t)=>t.childIndex-e.childIndex)[0].childIndex+1}),B(this,"onPinchStart",()=>{this.app.cancelSession(),this.setStatus("pinching")}),B(this,"onPinchEnd",()=>{s.Aq.isMobileSafari()&&this.app.undoSelect(),this.setStatus("idle")}),B(this,"onPinch",(e,t)=>{var i;"pinching"===this.status&&(isNaN(e.delta[0])||isNaN(e.delta[1])||(this.app.pinchZoom(e.point,e.delta,e.delta[2]),null==(i=this.onPointerMove)||i.call(this,e,t)))}),B(this,"onKeyDown",e=>"Escape"===e?void this.onCancel():"Meta"===e||"Control"===e||"Alt"===e?void this.app.updateSession():void 0),B(this,"onKeyUp",e=>{if("Meta"===e||"Control"===e||"Alt"===e)return void this.app.updateSession()}),B(this,"onPointerMove",()=>{"creating"===this.status&&this.app.updateSession()}),B(this,"onPointerUp",()=>{if("creating"===this.status){this.app.completeSession();let{isToolLocked:e}=this.app.appState;e||this.app.selectTool("select")}this.setStatus("idle")})}},iJ=class extends iZ{constructor(){super(...arguments),B(this,"type","arrow"),B(this,"onPointerDown",()=>{if("idle"!==this.status)return;let{currentPoint:e,currentGrid:t,settings:{showGrid:i},appState:{currentPageId:n,currentStyle:a}}=this.app,o=this.getNextChildIndex(),r=s.Aq.uniqueId(),l=ip.create({id:r,parentId:n,childIndex:o,point:i?h.A.snap(e,t):e,style:C({},a)});this.app.patchCreate([l]),this.app.startSession("arrow",l.id,"end",!0),this.setStatus("creating")})}},iQ=class extends iZ{constructor(){super(...arguments),B(this,"type","draw"),B(this,"lastShapeId"),B(this,"onEnter",()=>{this.lastShapeId=void 0}),B(this,"onCancel",()=>{"idle"===this.status?this.app.selectTool("select"):this.setStatus("idle"),this.app.cancelSession()}),B(this,"onPointerDown",e=>{if("idle"!==this.status||this.app.readOnly)return;let{currentPoint:t,appState:{currentPageId:i,currentStyle:n}}=this.app,a=this.lastShapeId&&this.app.getShape(this.lastShapeId);if(e.shiftKey&&a)this.app.startSession("draw",a.id),this.setStatus("extending");else{let e=this.getNextChildIndex(),a=s.Aq.uniqueId(),o=ih.create({id:a,parentId:i,childIndex:e,point:t,style:C({},n)});this.lastShapeId=a,this.app.patchCreate([o]),this.app.startSession("draw",a),this.setStatus("creating")}}),B(this,"onPointerMove",()=>{if(!this.app.readOnly)switch(this.status){case"extending":case"creating":this.app.updateSession()}}),B(this,"onPointerUp",()=>{this.app.completeSession(),this.setStatus("idle")})}},i0=class extends iZ{constructor(){super(...arguments),B(this,"type","ellipse"),B(this,"onPointerDown",()=>{if(this.app.readOnly||"idle"!==this.status)return;let{currentPoint:e,currentGrid:t,settings:{showGrid:i},appState:{currentPageId:n,currentStyle:a}}=this.app,o=this.getNextChildIndex(),r=s.Aq.uniqueId(),l=id.create({id:r,parentId:n,childIndex:o,point:i?h.A.snap(e,t):e,style:C({},a)});this.app.patchCreate([l]),this.app.startSession("transformSingle",l.id,s.NW.BottomRight,!0),this.setStatus("creating")})}},i1=class extends iZ{constructor(){super(...arguments),B(this,"type","erase"),B(this,"status","idle"),B(this,"onPointerDown",()=>{this.app.readOnly||"idle"===this.status&&this.setStatus("pointing")}),B(this,"onPointerMove",e=>{if(!this.app.readOnly)switch(this.status){case"pointing":h.A.dist(e.origin,e.point)>3&&(this.app.startSession("erase"),this.app.updateSession(),this.setStatus("erasing"));break;case"erasing":this.app.updateSession()}}),B(this,"onPointerUp",()=>{if(!this.app.readOnly){switch(this.status){case"pointing":{let e=this.app.shapes.filter(e=>!e.isLocked).filter(e=>this.app.getShapeUtil(e).hitTestPoint(e,this.app.currentPoint)).flatMap(e=>e.children?[e.id,...e.children]:e.id);this.app.delete(e);break}case"erasing":this.app.completeSession()}this.setStatus("idle")}}),B(this,"onCancel",()=>{"idle"===this.status?this.previous?this.app.selectTool(this.previous):this.app.selectTool("select"):this.setStatus("idle"),this.app.cancelSession()})}},i2=class extends iZ{constructor(){super(...arguments),B(this,"type","line"),B(this,"onPointerDown",()=>{if(this.app.readOnly||"idle"!==this.status)return;let{currentPoint:e,currentGrid:t,settings:{showGrid:i},appState:{currentPageId:n,currentStyle:a}}=this.app,o=this.getNextChildIndex(),r=s.Aq.uniqueId(),l=ip.create({id:r,parentId:n,childIndex:o,point:i?h.A.snap(e,t):e,decorations:{start:void 0,end:void 0},style:C({},a)});this.app.patchCreate([l]),this.app.startSession("arrow",l.id,"end",!0),this.setStatus("creating")})}},i5=class extends iZ{constructor(){super(...arguments),B(this,"type","rectangle"),B(this,"onPointerDown",()=>{if(this.app.readOnly||"idle"!==this.status)return;let{currentPoint:e,currentGrid:t,settings:{showGrid:i},appState:{currentPageId:n,currentStyle:a}}=this.app,o=this.getNextChildIndex(),r=s.Aq.uniqueId(),l=ir.create({id:r,parentId:n,childIndex:o,point:i?h.A.snap(e,t):e,style:C({},a)});this.app.patchCreate([l]),this.app.startSession("transformSingle",l.id,s.NW.BottomRight,!0),this.setStatus("creating")})}},i3=class extends iZ{constructor(){super(...arguments),B(this,"type","select"),B(this,"pointedId"),B(this,"selectedGroupId"),B(this,"pointedHandleId"),B(this,"pointedBoundsHandle"),B(this,"pointedLinkHandleId"),B(this,"onEnter",()=>{this.setStatus("idle")}),B(this,"onExit",()=>{this.setStatus("idle")}),B(this,"clonePaint",e=>{if(0===this.app.selectedIds.length)return;let t=this.app.selectedIds.map(e=>this.app.getShape(e)),i=s.Aq.expandBounds(s.Aq.getCommonBounds(t.map(ti.getBounds)),16),n=s.Aq.getBoundsCenter(i),a=[i.width,i.height],o=[n[0]+a[0]*Math.floor((e[0]+a[0]/2-n[0])/a[0]),n[1]+a[1]*Math.floor((e[1]+a[1]/2-n[1])/a[1])],r=s.Aq.centerBounds(i,o);this.app.shapes.some(e=>ti.getShapeUtil(e).hitTestBounds(e,r))||this.app.duplicate(this.app.selectedIds,o)}),B(this,"getShapeClone",(e,t)=>{let i=this.app.getShape(e),n=ti.getShapeUtil(i);if(n.canClone){let e=n.getBounds(i),a=n.getCenter(i),o={top:[e.minX,e.minY-(e.height+32)],right:[e.maxX+32,e.minY],bottom:[e.minX,e.maxY+32],left:[e.minX-(e.width+32),e.minY],topLeft:[e.minX-(e.width+32),e.minY-(e.height+32)],topRight:[e.maxX+32,e.minY-(e.height+32)],bottomLeft:[e.minX-(e.width+32),e.maxY+32],bottomRight:[e.maxX+32,e.maxY+32]}[t];if(0!==i.rotation){let t=h.A.add(o,[e.width/2,e.height/2]),s=h.A.rotWith(t,a,i.rotation||0);o=h.A.sub(s,[e.width/2,e.height/2])}let r=s.Aq.uniqueId(),l=A(C({},i),{id:r,point:o});return"sticky"===l.type&&(l.text=""),l}}),B(this,"onCancel",()=>{this.app.session?this.app.cancelSession():this.selectNone(),this.setStatus("idle")}),B(this,"onKeyDown",(e,t,i)=>{switch(e){case"Escape":this.onCancel();break;case"Tab":if(this.app.readOnly)return;if(!this.app.pageState.editingId&&"idle"===this.status&&1===this.app.selectedIds.length){let[e]=this.app.selectedIds,t=this.getShapeClone(e,"right");t&&(this.app.createShapes(t),this.setStatus("idle"),"sticky"===t.type&&(this.app.select(t.id),this.app.setEditingId(t.id)))}break;case"Meta":case"Control":case"Alt":this.app.updateSession();break;case"Enter":{if(this.app.readOnly)return;let{pageState:e}=this.app;1!==e.selectedIds.length||e.editingId||(this.app.setEditingId(e.selectedIds[0]),i.preventDefault())}}}),B(this,"onKeyUp",(e,t)=>"clonePainting"!==this.status||t.altKey&&t.shiftKey?"Meta"===e||"Control"===e||"Alt"===e?void this.app.updateSession():void 0:void this.setStatus("idle")),B(this,"onPointerMove",()=>{let{originPoint:e,currentPoint:t}=this.app;if(this.app.readOnly&&this.app.isPointing)return void(this.app.session?this.app.updateSession():h.A.dist(e,t)>3&&(this.app.startSession("brush"),this.setStatus("brushing")));switch(this.status){case"pointingBoundsHandle":if(!this.pointedBoundsHandle)throw Error("No pointed bounds handle");if(h.A.dist(e,t)>3){if("rotate"===this.pointedBoundsHandle)this.setStatus("rotating"),this.app.startSession("rotate");else if("center"===this.pointedBoundsHandle||"left"===this.pointedBoundsHandle||"right"===this.pointedBoundsHandle)this.setStatus("translating"),this.app.startSession("translate",!1,this.pointedBoundsHandle);else{this.setStatus("transforming");let e=this.app.selectedIds.flatMap(e=>ti.getDocumentBranch(this.app.state,e,this.app.currentPageId));1===e.length?this.app.startSession("transformSingle",e[0],this.pointedBoundsHandle):this.app.startSession("transform",this.pointedBoundsHandle)}this.app.updateSession()}break;case"pointingCanvas":h.A.dist(e,t)>3&&(this.app.startSession("brush"),this.setStatus("brushing"));break;case"pointingClone":h.A.dist(e,t)>3&&(this.setStatus("translatingClone"),this.app.startSession("translate"),this.app.updateSession());break;case"pointingBounds":h.A.dist(e,t)>3&&(this.setStatus("translating"),this.app.startSession("translate"),this.app.updateSession());break;case"pointingHandle":if(!this.pointedHandleId)throw Error("No pointed handle");if(h.A.dist(e,t)>3){this.setStatus("translatingHandle");let e=this.app.getShape(this.app.selectedIds[0]);e&&("bend"===this.pointedHandleId?this.app.startSession("handle",e.id,this.pointedHandleId):this.app.startSession("arrow",e.id,this.pointedHandleId,!1),this.app.updateSession())}break;case"clonePainting":this.clonePaint(t);break;default:this.app.session&&this.app.updateSession()}}),B(this,"onPointerDown",(e,t)=>{if("canvas"===e.target&&"idle"===this.status){let{currentPoint:i}=this.app;if(!e.spaceKey||1!==t.buttons){if("idle"===this.status&&e.altKey&&e.shiftKey){this.setStatus("clonePainting"),this.clonePaint(i);return}if(!e.shiftKey){if(this.app.onShapeBlur(),e.altKey&&this.app.selectedIds.length>0)return void this.app.duplicate(this.app.selectedIds,i);this.selectNone()}this.setStatus("pointingCanvas")}}}),B(this,"onPointerUp",e=>{var t;if("translatingClone"===this.status||"pointingClone"===this.status){this.pointedId&&(this.app.completeSession(),this.app.setEditingId(this.pointedId)),this.setStatus("idle"),this.pointedId=void 0;return}if("pointingBounds"===this.status){if("bounds"===e.target)this.selectNone();else if(this.app.isSelected(e.target))e.shiftKey?this.pointedId!==e.target&&this.deselect(e.target):this.pointedId!==e.target&&this.app.selectedIds.length>1&&this.select(e.target);else if(this.pointedId===e.target){if(this.app.getShape(e.target).isLocked)return;e.shiftKey?this.pushSelect(e.target):this.select(e.target)}}this.setStatus("idle"),this.pointedBoundsHandle=void 0,this.pointedHandleId=void 0,this.pointedId=void 0,(null==(t=this.app.session)?void 0:t.type)!=="edit"&&this.app.completeSession()}),B(this,"onDoubleClickCanvas",()=>{this.app.readOnly}),B(this,"onPointShape",(e,t)=>{if(e.spaceKey&&1===t.buttons||this.app.getShape(e.target).isLocked)return;let{editingId:i,hoveredId:s}=this.app.pageState;if(i&&e.target!==i&&this.app.onShapeBlur(),("idle"===this.status||"pointingBounds"===this.status)&&e.metaKey&&e.shiftKey&&s){this.pointedId=s,this.app.isSelected(s)?this.deselect(s):(this.pushSelect(s),this.setStatus("pointingBounds"));return}if("pointingBounds"===this.status){let{parentId:t}=this.app.getShape(e.target);this.pointedId=t===this.app.currentPageId?e.target:t;return}if("idle"===this.status){if(this.setStatus("pointingBounds"),e.metaKey){e.shiftKey||this.selectNone(),this.app.startSession("brush"),this.setStatus("brushing");return}let t,{parentId:i}=this.app.getShape(e.target);i===this.app.currentPageId?(t=e.target,this.selectedGroupId=void 0):i===this.selectedGroupId?t=e.target:(t=i,this.selectedGroupId=void 0),this.app.isSelected(t)||(this.pointedId=t,e.shiftKey?this.pushSelect(t):this.select(t))}}),B(this,"onDoubleClickShape",e=>{if(this.app.readOnly)return;let t=this.app.getShape(e.target);if(t.isLocked)return void this.app.select(e.target);ti.getShapeUtil(t.type).canEdit&&(t.parentId===this.app.currentPageId||t.parentId===this.selectedGroupId)&&this.app.setEditingId(e.target),t.parentId!==this.app.currentPageId&&(this.selectedGroupId=t.parentId),this.app.select(e.target)}),B(this,"onRightPointShape",e=>{this.app.isSelected(e.target)||this.app.select(e.target)}),B(this,"onHoverShape",e=>{this.app.setHoveredId(e.target)}),B(this,"onUnhoverShape",e=>{let{currentPageId:t}=this.app;requestAnimationFrame(()=>{t===this.app.currentPageId&&this.app.pageState.hoveredId===e.target&&this.app.setHoveredId(void 0)})}),B(this,"onPointBounds",e=>{if(e.metaKey){e.shiftKey||this.selectNone(),this.app.startSession("brush"),this.setStatus("brushing");return}this.setStatus("pointingBounds")}),B(this,"onRightPointBounds",(e,t)=>{t.stopPropagation()}),B(this,"onReleaseBounds",()=>{("translating"===this.status||"brushing"===this.status)&&this.app.completeSession(),this.setStatus("idle")}),B(this,"onPointBoundsHandle",e=>{this.pointedBoundsHandle=e.target,this.setStatus("pointingBoundsHandle")}),B(this,"onDoubleClickBoundsHandle",e=>{switch(e.target){case"center":case"left":case"right":this.app.select(...ti.getLinkedShapeIds(this.app.state,this.app.currentPageId,e.target,e.shiftKey));break;default:if(1===this.app.selectedIds.length){this.app.resetBounds(this.app.selectedIds);let e=this.app.getShape(this.app.selectedIds[0]);"label"in e&&this.app.setEditingId(e.id)}}}),B(this,"onReleaseBoundsHandle",()=>{this.setStatus("idle")}),B(this,"onPointHandle",e=>{this.pointedHandleId=e.target,this.setStatus("pointingHandle")}),B(this,"onDoubleClickHandle",e=>{if("bend"===e.target){let{selectedIds:e}=this.app;if(1!==e.length)return;let t=this.app.getShape(e[0]);ti.getShapeUtil(t.type).canEdit&&(t.parentId===this.app.currentPageId||t.parentId===this.selectedGroupId)&&this.app.setEditingId(t.id);return}this.app.toggleDecoration(e.target)}),B(this,"onReleaseHandle",()=>{this.setStatus("idle")}),B(this,"onShapeClone",e=>{let t=this.app.selectedIds[0],i=this.getShapeClone(t,e.target);"left"===e.target||"right"===e.target||"top"===e.target||"bottom"===e.target?i&&(this.app.createShapes(i),this.pointedId=i.id,this.setStatus("pointingClone")):(this.setStatus("gridCloning"),this.app.startSession("grid",t))})}deselect(e){this.app.select(...this.app.selectedIds.filter(t=>t!==e))}select(e){this.app.select(e)}pushSelect(e){let t=this.app.getShape(e);this.app.select(...this.app.selectedIds.filter(e=>e!==t.parentId),e)}selectNone(){this.app.selectNone()}},i4=class extends iZ{constructor(){super(...arguments),B(this,"type","sticky"),B(this,"shapeId"),B(this,"onPointerDown",()=>{if(!this.app.readOnly){if("creating"===this.status){this.setStatus("idle"),this.app.appState.isToolLocked||this.app.selectTool("select");return}if("idle"===this.status){let{currentPoint:e,currentGrid:t,settings:{showGrid:i},appState:{currentPageId:n,currentStyle:a}}=this.app,o=this.getNextChildIndex(),r=s.Aq.uniqueId();this.shapeId=r;let l=ig.create({id:r,parentId:n,childIndex:o,point:i?h.A.snap(e,t):e,style:C({},a)}),d=ig.getBounds(l);l.point=h.A.sub(l.point,[d.width/2,d.height/2]),this.app.patchCreate([l]),this.app.startSession("translate"),this.setStatus("creating")}}}),B(this,"onPointerUp",()=>{this.app.readOnly||"creating"===this.status&&(this.setStatus("idle"),this.app.completeSession(),this.app.selectTool("select"),this.app.setEditingId(this.shapeId))})}},i8=class extends iZ{constructor(){super(...arguments),B(this,"type","text"),B(this,"stopEditingShape",()=>{this.setStatus("idle"),this.app.appState.isToolLocked||this.app.selectTool("select")}),B(this,"onKeyUp",()=>{}),B(this,"onKeyDown",()=>{}),B(this,"onPointerDown",()=>{if("creating"===this.status)return void this.stopEditingShape();if("idle"===this.status){let{currentPoint:e,currentGrid:t,settings:{showGrid:i}}=this.app;this.app.createTextShapeAtPoint(i?h.A.snap(e,t):e,void 0,!0),this.setStatus("creating");return}}),B(this,"onPointerUp",()=>{}),B(this,"onPointShape",e=>{if(this.app.readOnly)return;let t=this.app.getShape(e.target);"text"===t.type&&(this.setStatus("idle"),this.app.setEditingId(t.id))}),B(this,"onShapeBlur",()=>{this.app.readOnly||this.stopEditingShape()})}},i6=class extends iZ{constructor(){super(...arguments),B(this,"type","triangle"),B(this,"onPointerDown",()=>{if(this.app.readOnly||"idle"!==this.status)return;let{currentPoint:e,currentGrid:t,settings:{showGrid:i},appState:{currentPageId:n,currentStyle:a}}=this.app,o=this.getNextChildIndex(),r=s.Aq.uniqueId(),l=il.create({id:r,parentId:n,childIndex:o,point:i?h.A.snap(e,t):e,style:C({},a)});this.app.patchCreate([l]),this.app.startSession("transformSingle",l.id,s.NW.BottomRight,!0),this.setStatus("creating")})}},i9=s.Aq.uniqueId(),i7=class extends iy{constructor(e,t={}){super(i7.defaultState,e,i7.version,(e,t,i)=>iz(A(C({},t),{document:A(C(C({},t.document),e.document),{version:i})}),i7.version)),B(this,"callbacks",{}),B(this,"tools",{select:new i3(this),erase:new i1(this),text:new i8(this),draw:new iQ(this),ellipse:new i0(this),rectangle:new i5(this),triangle:new i6(this),line:new i2(this),arrow:new iJ(this),sticky:new i4(this)}),B(this,"currentTool",this.tools.select),B(this,"session"),B(this,"readOnly",!1),B(this,"isDirty",!1),B(this,"isCreating",!1),B(this,"originPoint",[0,0]),B(this,"currentPoint",[0,0]),B(this,"previousPoint",[0,0]),B(this,"shiftKey",!1),B(this,"altKey",!1),B(this,"metaKey",!1),B(this,"ctrlKey",!1),B(this,"spaceKey",!1),B(this,"isPointing",!1),B(this,"isForcePanning",!1),B(this,"isPastePrevented",!1),B(this,"editingStartTime",-1),B(this,"fileSystemHandle",null),B(this,"viewport",s.Aq.getBoundsFromPoints([[0,0],[100,100]])),B(this,"rendererBounds",s.Aq.getBoundsFromPoints([[0,0],[100,100]])),B(this,"selectHistory",{stack:[[]],pointer:0}),B(this,"clipboard"),B(this,"rotationInfo",{selectedIds:[],center:[0,0]}),B(this,"migrate",e=>iz(e,i7.version)),B(this,"onReady",()=>{var e,t;this.loadDocument(this.document),ij().then(e=>{this.fileSystemHandle=e});try{this.patchState(A(C({},iz(this.state,i7.version)),{appState:{status:"idle"}}))}catch(e){console.error("The data appears to be corrupted. Resetting!",e),localStorage.setItem(this.document.id+"_corrupted",JSON.stringify(this.document)),this.patchState(A(C({},i7.defaultState),{appState:A(C({},i7.defaultState.appState),{status:"idle"})}))}null==(t=(e=this.callbacks).onMount)||t.call(e,this)}),B(this,"cleanup",(e,t)=>{var i;let n=C({},e);n.document!==t.document&&Object.entries(n.document.pages).forEach(([e,i])=>{if(void 0===i){delete n.document.pages[e],delete n.document.pageStates[e];return}let a=t.document.pages[e],o={};if(!a||i.shapes!==a.shapes||i.bindings!==a.bindings){i.shapes=C({},i.shapes),i.bindings=C({},i.bindings);let t=new Set;Object.entries(i.shapes).forEach(([s,r])=>{var l;let d;r?d=r.parentId:(d=null==(l=null==a?void 0:a.shapes[s])?void 0:l.parentId,delete i.shapes[s]),i.id===n.appState.currentPageId&&(null==a?void 0:a.shapes[s])!==r&&(o[s]=r),d&&d!==e&&void 0!==i.shapes[d]&&t.add(i.shapes[d])}),Object.keys(i.bindings).forEach(e=>{i.bindings[e]||delete i.bindings[e]}),n.document.pages[e]=i;let r=ti.getRelatedBindings(n,Object.keys(o),e),l=new Set;r.forEach(t=>{if(!i.bindings[t.id])return;let s=i.shapes[t.toId],a=i.shapes[t.fromId];if(!(s&&a))return void delete n.document.pages[e].bindings[t.id];if(l.has(a))return;let o=ti.updateArrowBindings(i,a);if(l.add(a),o){let e=C(C({},a),o);i.shapes[a.id]=e}}),t.forEach(e=>{if(!e)throw Error("no group!");let t=e.children.filter(e=>void 0!==i.shapes[e]),n=s.Aq.getCommonBounds(t.map(e=>i.shapes[e]).filter(Boolean).map(e=>ti.getRotatedBounds(e)));i.shapes[e.id]=A(C({},e),{point:[n.minX,n.minY],size:[n.width,n.height],children:t})})}let r=C({},n.document.pageStates[e]);r.brush||delete r.brush,r.hoveredId&&!i.shapes[r.hoveredId]&&delete r.hoveredId,r.bindingId&&!i.bindings[r.bindingId]&&(ti.warn(`Could not find the binding of ${e}`),delete r.bindingId),r.editingId&&!i.shapes[r.editingId]&&(ti.warn("Could not find the editing shape!"),delete r.editingId),n.document.pageStates[e]=r}),Object.keys(null!=(i=n.document.assets)?i:{}).forEach(e=>{var t,i;(null==(t=n.document.assets)?void 0:t[e])||null==(i=n.document.assets)||delete i[e]});let a=n.appState.currentPageId,o=n.document.pageStates[a];if(n.room&&n.room!==t.room){let e=A(C({},n.room),{users:C({},n.room.users)});t.room&&Object.values(t.room.users).filter(Boolean).forEach(t=>{void 0===e.users[t.id]&&delete e.users[t.id]}),n.room=e}return n.room&&(n.room.users[n.room.userId]=A(C({},n.room.users[n.room.userId]),{point:this.currentPoint,selectedIds:o.selectedIds})),this.readOnly&&(n.document.pages=t.document.pages),n}),B(this,"broadcastPatch",(e,t)=>{var i,s,n,a,o,r,l,d,h;let p={},c={},u={},g=null==(n=null==(s=null==(i=null==e?void 0:e.document)?void 0:i.pages)?void 0:s[this.currentPageId])?void 0:n.shapes,m=null==(r=null==(o=null==(a=null==e?void 0:e.document)?void 0:a.pages)?void 0:o[this.currentPageId])?void 0:r.bindings,f=null==(l=null==e?void 0:e.document)?void 0:l.assets;g&&Object.keys(g).forEach(e=>{p[e]=this.getShape(e,this.currentPageId)}),m&&Object.keys(m).forEach(e=>{c[e]=this.getBinding(e,this.currentPageId)}),f&&Object.keys(f).forEach(e=>{u[e]=this.document.assets[e]}),null==(h=(d=this.callbacks).onChangePage)||h.call(d,this,p,c,u,t)}),B(this,"onPatch",(e,t,i)=>{var s,n,a,o,r,l;(this.callbacks.onChangePage&&(null==(n=null==(s=null==t?void 0:t.document)?void 0:s.pages)?void 0:n[this.currentPageId])||(null==(a=null==t?void 0:t.document)?void 0:a.assets))&&((null==(o=null==t?void 0:t.document)?void 0:o.assets)||this.session&&"brush"!==this.session.type&&"erase"!==this.session.type&&"draw"!==this.session.type)&&this.broadcastPatch(t,!1),null==(l=(r=this.callbacks).onPatch)||l.call(r,this,t,i)}),B(this,"onCommand",(e,t,i)=>{var s,n;this.clearSelectHistory(),this.isDirty=!0,null==(n=(s=this.callbacks).onCommand)||n.call(s,this,t,i)}),B(this,"onReplace",()=>{this.clearSelectHistory(),this.isDirty=!1}),B(this,"onUndo",()=>{var e,t;this.rotationInfo.selectedIds=[...this.selectedIds],null==(t=(e=this.callbacks).onUndo)||t.call(e,this)}),B(this,"onRedo",()=>{var e,t;this.rotationInfo.selectedIds=[...this.selectedIds],null==(t=(e=this.callbacks).onRedo)||t.call(e,this)}),B(this,"onPersist",(e,t)=>{var i,s;null==(s=(i=this.callbacks).onPersist)||s.call(i,this),this.broadcastPatch(t,!0)}),B(this,"prevSelectedIds",this.selectedIds),B(this,"onStateDidChange",(e,t)=>{var i,s,n,a;null==(s=(i=this.callbacks).onChange)||s.call(i,this,t),this.room&&this.selectedIds!==this.prevSelectedIds&&(null==(a=(n=this.callbacks).onChangePresence)||a.call(n,this,A(C({},this.room.users[this.room.userId]),{selectedIds:this.selectedIds,session:!!this.session})),this.prevSelectedIds=this.selectedIds)}),B(this,"preventPaste",()=>{if(this.isPastePrevented)return;let e=e=>e.stopImmediatePropagation();document.addEventListener("paste",e,{capture:!0}),window.addEventListener("pointerup",()=>{setTimeout(()=>{document.removeEventListener("paste",e,{capture:!0}),this.isPastePrevented=!1},50)},{once:!0}),this.isPastePrevented=!0}),B(this,"justSent",!1),B(this,"getReservedContent",(e,t=this.currentPageId)=>{let{bindings:i}=this.document.pages[t],s={},n={},a=Object.values(i),o=[new Map(a.map(e=>[e.toId,e])),new Map(a.map(e=>[e.fromId,e]))],r=[];this.session&&e.forEach(e=>r.push(e)),this.pageState.editingId&&r.push(this.pageState.editingId);let l=new Set(r),d=new Set;for(;r.length>0;){let e=r.pop();if(!e)break;if(d.has(e))continue;d.add(e);let i=this.getShape(e);s[e]=i,i.parentId!==t&&r.push(i.parentId),i.children&&r.push(...i.children),o.map(e=>e.get(i.id)).filter(Boolean).forEach(e=>{n[e.id]=e,r.push(e.toId,e.fromId)})}return{reservedShapes:s,reservedBindings:n,strongReservedShapeIds:l}}),B(this,"replacePageContent",(e,t,i,n=this.currentPageId)=>{if(this.justSent)return this.justSent=!1,this;let a=this.document.pages[this.currentPageId];return Object.values(e).forEach(t=>{t.parentId===n||a.shapes[t.parentId]||e[t.parentId]||(console.warn("Added a shape without a parent on the page"),t.parentId=n)}),this.useStore.setState(a=>{let{hoveredId:o,editingId:r,bindingId:l,selectedIds:d}=a.document.pageStates[n],h=[...d],p=r&&a.document.pages[this.currentPageId].shapes[r];p&&h.push(p.id);let{reservedShapes:c,reservedBindings:u,strongReservedShapeIds:g}=this.getReservedContent(h,this.currentPageId);Object.values(c).filter(e=>!("text"in e)).forEach(t=>{let i=e[t.id];if(i){if(!("arrow"===t.type||g.has(t.id))){e[t.id]=i;return}"decorations"in i&&"decorations"in t&&(e[t.id]=A(C({},t),{decorations:i.decorations})),t.style=i.style}});let m=C(C({},e),c);p&&(m[p.id]=p);let f=C(C({},t),u),v=C({},i),b=A(C({},a),{document:A(C({},a.document),{pages:{[n]:A(C({},a.document.pages[n]),{shapes:m,bindings:f})},assets:v,pageStates:A(C({},a.document.pageStates),{[n]:A(C({},a.document.pageStates[n]),{selectedIds:d.filter(e=>void 0!==m[e]),hoveredId:o?void 0===m[o]?void 0:o:void 0,editingId:r,bindingId:l?void 0===f[l]?void 0:l:void 0})})})}),y=b.document.pages[n],S=ti.getRelatedBindings(b,Object.keys(m),n),I=new Set;return S.forEach(e=>{if(!y.bindings[e.id])return;let t=y.shapes[e.fromId];if(I.has(t))return;let i=ti.updateArrowBindings(y,t);if(I.add(t),i){let e=C(C({},t),i);y.shapes[t.id]=e}}),Object.values(m).forEach(e=>{if("group"!==e.type)return;let t=e.children.filter(e=>void 0!==y.shapes[e]),i=s.Aq.getCommonBounds(t.map(e=>y.shapes[e]).filter(Boolean).map(e=>ti.getRotatedBounds(e)));y.shapes[e.id]=A(C({},e),{point:[i.minX,i.minY],size:[i.width,i.height],children:t})}),this.state.document=b.document,b},!0),this}),B(this,"updateBounds",e=>{this.rendererBounds=e;let{point:t,zoom:i}=this.camera;this.updateViewport(t,i),!this.readOnly&&this.session&&this.session.update()}),B(this,"updateViewport",(e,t)=>{let{width:i,height:s}=this.rendererBounds,[n,a]=h.l.sub(h.l.div([0,0],t),e),[o,r]=h.l.sub(h.l.div([i,s],t),e);this.viewport={minX:n,minY:a,maxX:o,maxY:r,height:o-n,width:r-a}}),B(this,"setEditingId",(e,t=!1)=>{if(!this.readOnly){if(e)this.startSession("edit",e,t);else{if(!this.pageState.editingId)return;this.completeSession()}this.editingStartTime=performance.now(),this.patchState({document:{pageStates:{[this.currentPageId]:{editingId:e}}}},"set_editing_id")}}),B(this,"setHoveredId",e=>{this.patchState({document:{pageStates:{[this.currentPageId]:{hoveredId:e}}}},"set_hovered_id")}),B(this,"setSetting",(e,t)=>{if(this.session)return this;let i={settings:{[e]:"function"==typeof t?t(this.settings[e]):t}};return this.patchState(i,`settings:${e}`),this.persist(i),this}),B(this,"toggleFocusMode",()=>{if(this.session)return this;let e={settings:{isFocusMode:!this.settings.isFocusMode}};return this.patchState(e,"settings:toggled_focus_mode"),this.persist(e),this}),B(this,"togglePenMode",()=>{if(this.session)return this;let e={settings:{isPenMode:!this.settings.isPenMode}};return this.patchState(e,"settings:toggled_pen_mode"),this.persist(e),this}),B(this,"toggleDarkMode",()=>{if(this.session)return this;let e={settings:{isDarkMode:!this.settings.isDarkMode}};return this.patchState(e,"settings:toggled_dark_mode"),this.persist(e),this}),B(this,"toggleZoomSnap",()=>{if(this.session)return this;let e={settings:{isZoomSnap:!this.settings.isZoomSnap}};return this.patchState(e,"settings:toggled_zoom_snap"),this.persist(e),this}),B(this,"toggleDebugMode",()=>{if(this.session)return this;let e={settings:{isDebugMode:!this.settings.isDebugMode}};return this.patchState(e,"settings:toggled_debug"),this.persist(e),this}),B(this,"setMenuOpen",e=>{let t={appState:{isMenuOpen:e}};return this.patchState(t,"ui:toggled_menu_opened"),this.persist(t),this}),B(this,"setIsLoading",e=>{let t={appState:{isLoading:e}};return this.patchState(t,"ui:toggled_is_loading"),this.persist(t),this}),B(this,"setDisableAssets",e=>(this.patchState({appState:{disableAssets:e}},"ui:toggled_disable_images"),this)),B(this,"toggleGrid",()=>{if(this.session)return this;let e={settings:{showGrid:!this.settings.showGrid}};return this.patchState(e,"settings:toggled_grid"),this.persist(e),this}),B(this,"selectTool",e=>{if(this.readOnly||this.session)return this;this.isPointing=!1;let t=this.tools[e];return t===this.currentTool?(this.patchState({appState:{isToolLocked:!1}}),this):(this.currentTool.onExit(),t.previous=this.currentTool.type,this.currentTool=t,this.currentTool.onEnter(),this.patchState({appState:{activeTool:e,isToolLocked:!1}},`selected_tool:${e}`))}),B(this,"toggleToolLock",()=>this.session?this:this.patchState({appState:{isToolLocked:!this.appState.isToolLocked}},"toggled_tool_lock")),B(this,"resetDocument",()=>(this.session||(this.session=void 0,this.currentTool=this.tools.select,i7.defaultDocument.pages.page.name="Page 1",this.resetHistory().clearSelectHistory().loadDocument(i7.defaultDocument).persist({})),this)),B(this,"updateUsers",(e,t=!1)=>{this.patchState({room:{users:Object.fromEntries(e.map(e=>[e.id,e]))}},t?"room:self:update":"room:user:update")}),B(this,"removeUser",e=>{this.patchState({room:{users:{[e]:void 0}}})}),B(this,"mergeDocument",e=>{if(this.document.id!==e.id)return this.replaceState(A(C({},iz(A(C({},this.state),{document:e}),i7.version)),{appState:A(C({},this.appState),{currentPageId:Object.keys(e.pages)[0]})})),this;let t=C({},this.document.pageStates),i=A(C({},this.appState),{currentPageId:e.pages[this.currentPageId]?this.currentPageId:Object.keys(e.pages)[0],pages:Object.values(e.pages).map((e,t)=>({id:e.id,name:e.name,childIndex:e.childIndex||t}))});this.resetHistory(),Object.keys(this.document.pages).forEach(i=>{e.pages[i]||(i===this.appState.currentPageId&&(this.cancelSession(),this.selectNone()),t[i]=void 0)}),this.session&&this.selectedIds.filter(t=>!e.pages[this.currentPageId].shapes[t]).forEach(t=>e.pages[this.currentPageId].shapes[t]=this.page.shapes[t]),Object.entries(t).forEach(([t,i])=>{i.selectedIds=i.selectedIds.filter(i=>!!e.pages[t].shapes[i])});let{editingId:s}=this.pageState;return s&&(e.pages[this.currentPageId].shapes[s]=this.page.shapes[s],t[this.currentPageId].selectedIds=[s]),this.replaceState(A(C({},iz(A(C({},this.state),{document:A(C({},e),{pageStates:t})}),i7.version)),{appState:i}),"merge")}),B(this,"updateDocument",(e,t="updated_document")=>{let i=this.state,s=A(C({},i),{document:A(C({},i.document),{assets:e.assets})});e.pages[this.currentPageId]||(s.appState=A(C({},i.appState),{currentPageId:Object.keys(e.pages)[0]}));let n=1;for(let t of Object.values(e.pages))t!==i.document.pages[t.id]&&(s.document.pages[t.id]=t,t.name||(s.document.pages[t.id].name=`Page ${n+1}`,n++));for(let t of Object.values(e.pageStates))if(t!==i.document.pageStates[t.id]){s.document.pageStates[t.id]=t;let i=e.pages[t.id];for(let e of["bindingId","editingId","hoveredId","pointedId"])i.shapes[e]||(t[e]=void 0);t.selectedIds=t.selectedIds.filter(t=>!!e.pages[i.id].shapes[t])}return this.replaceState(iz(s,s.document.version||0),`${t}:${e.id}`)}),B(this,"loadRoom",e=>(this.patchState({room:{id:e,userId:i9,users:{[i9]:{id:i9,color:ev[Math.floor(Math.random()*ev.length)],point:[100,100],selectedIds:[],activeShapes:[]}}}}),this)),B(this,"loadDocument",e=>{this.setIsLoading(!0),this.selectNone(),this.resetHistory(),this.clearSelectHistory(),this.session=void 0;let t=A(C({},i7.defaultState),{settings:C({},this.state.settings),document:e,appState:A(C(C({},i7.defaultState.appState),this.state.appState),{currentPageId:Object.keys(e.pages)[0],disableAssets:this.disableAssets})});this.replaceState(iz(t,i7.version),"loaded_document");let{point:i,zoom:s}=this.camera;return this.updateViewport(i,s),this.setIsLoading(!1),this}),B(this,"loadPageFromURL",(e,t)=>{let i=e.id,s=A(C({},this.state.document),{pageStates:A(C({},this.state.document.pageStates),{[i]:t}),pages:A(C({},this.document.pages),{[i]:e})});this.loadDocument(s),this.persist({})}),B(this,"newProject",()=>{this.isLocal&&(this.fileSystemHandle=null,this.resetDocument())}),B(this,"saveProject",()=>z(this,null,function*(){if(this.readOnly)return;let e=yield iO(iz(this.state,i7.version).document,this.fileSystemHandle);return this.fileSystemHandle=e,this.persist({}),this.isDirty=!1,this})),B(this,"saveProjectAs",e=>z(this,null,function*(){try{let t=yield iO(this.document,null,e);this.fileSystemHandle=t,this.persist({}),this.isDirty=!1}catch(e){console.error(e.message)}return this})),B(this,"openProject",()=>z(this,null,function*(){if(this.isLocal)try{let e=yield iF();if(!e)throw Error();let{fileHandle:t,document:i}=e;this.loadDocument(i),this.fileSystemHandle=t,this.zoomToFit(),this.persist({})}catch(e){console.error(e)}finally{this.persist({})}})),B(this,"openAsset",()=>z(this,null,function*(){if(!this.disableAssets)try{let e=yield iL();if(Array.isArray(e))this.addMediaFromFiles(e,this.centerPoint);else{if(!e)return;this.addMediaFromFiles([e])}}catch(e){console.error(e)}finally{this.persist({})}})),B(this,"signOut",()=>{}),B(this,"getAppState",()=>this.appState),B(this,"getPage",(e=this.currentPageId)=>ti.getPage(this.state,e||this.currentPageId)),B(this,"getShapes",(e=this.currentPageId)=>ti.getShapes(this.state,e||this.currentPageId)),B(this,"getBindings",(e=this.currentPageId)=>ti.getBindings(this.state,e||this.currentPageId)),B(this,"getShape",(e,t=this.currentPageId)=>ti.getShape(this.state,e,t)),B(this,"getShapeBounds",(e,t=this.currentPageId)=>ti.getBounds(this.getShape(e,t))),B(this,"getBinding",(e,t=this.currentPageId)=>ti.getBinding(this.state,e,t)),B(this,"getPageState",(e=this.currentPageId)=>ti.getPageState(this.state,e||this.currentPageId)),B(this,"getPagePoint",(e,t=this.currentPageId)=>{let{camera:i}=this.getPageState(t);return h.l.sub(h.l.div(e,i.zoom),i.point)}),B(this,"createPage",(e,t)=>{if(this.readOnly)return this;let{width:i,height:n}=this.rendererBounds;return this.setState(function(e,t,i=s.Aq.uniqueId(),n="Page"){let{currentPageId:a}=e,o=Object.values(e.state.document.pages).sort((e,t)=>{var i,s;return(null!=(i=e.childIndex)?i:0)-(null!=(s=t.childIndex)?s:0)}),r=o[o.length-1],l=(null==r?void 0:r.childIndex)?(null==r?void 0:r.childIndex)+1:1,d={id:i,name:function(e,t){var i;let s=e,n=new Set(t);for(;n.has(s);)s=(null==(i=/^.*(\d+)$/.exec(s))?void 0:i[1])?s.replace(/(\d+)(?=\D?)$/,e=>(+e+1).toString()):`${s} 1`;return s}(n,o.map(e=>{var t;return null!=(t=e.name)?t:""})),childIndex:l,shapes:{},bindings:{}};return{id:"create_page",before:{appState:{currentPageId:a},document:{pages:{[i]:void 0},pageStates:{[i]:void 0}}},after:{appState:{currentPageId:d.id},document:{pages:{[i]:d},pageStates:{[i]:{id:i,selectedIds:[],camera:{point:t,zoom:1},editingId:void 0,bindingId:void 0,hoveredId:void 0,pointedId:void 0}}}}}}(this,[-i/2,-n/2],e,t))}),B(this,"changePage",e=>this.setState({id:"change_page",before:{appState:{currentPageId:this.currentPageId}},after:{appState:{currentPageId:e}}})),B(this,"movePage",(e,t)=>this.readOnly?this:this.setState(function(e,t,i){let{pages:s}=e.document,n=s[t],a=Object.values(s).sort((e,t)=>{var i,s;return(null!=(i=e.childIndex)?i:0)-(null!=(s=t.childIndex)?s:0)}),o=a.indexOf(n),r=[...a];return r.splice(o,1),r.splice(i>o?i-1:i,0,n),{id:"move_page",before:{document:{pages:Object.fromEntries(a.map(e=>[e.id,{childIndex:e.childIndex}]))}},after:{document:{pages:Object.fromEntries(r.map((e,t)=>[e.id,{childIndex:t}]))}}}}(this,e,t))),B(this,"renamePage",(e,t)=>this.readOnly?this:this.setState(function(e,t,i){let{page:s}=e;return{id:"rename_page",before:{document:{pages:{[t]:{name:s.name}}}},after:{document:{pages:{[t]:{name:i}}}}}}(this,e,t))),B(this,"duplicatePage",e=>this.readOnly?this:this.setState(function(e,t){let{currentPageId:i,pageState:{camera:n}}=e,a=e.document.pages[t],o=s.Aq.uniqueId(),r=A(C({},a),{id:o,name:a.name+" Copy",shapes:Object.fromEntries(Object.entries(a.shapes).map(([e,t])=>[e,A(C({},t),{parentId:t.parentId===a.id?o:t.parentId})]))});return{id:"duplicate_page",before:{appState:{currentPageId:i},document:{pages:{[o]:void 0},pageStates:{[o]:void 0}}},after:{appState:{currentPageId:o},document:{pages:{[o]:r},pageStates:{[o]:A(C({},a),{id:o,selectedIds:[],camera:C({},n),editingId:void 0,bindingId:void 0,hoveredId:void 0,pointedId:void 0})}}}}}(this,e))),B(this,"deletePage",e=>this.readOnly||Object.values(this.document.pages).length<=1?this:this.setState(function(e,t){let{currentPageId:i,document:{pages:s,pageStates:n}}=e,a=Object.values(s).sort((e,t)=>(e.childIndex||0)-(t.childIndex||0)),o=a.findIndex(e=>e.id===t),r;return r=t===i?o===a.length-1?a[a.length-2].id:a[o+1].id:i,{id:"delete_page",before:{appState:{currentPageId:t},document:{pages:{[t]:C({},s[t])},pageStates:{[t]:C({},n[t])}}},after:{appState:{currentPageId:r},document:{pages:{[t]:void 0},pageStates:{[t]:void 0}}}}}(this,e||this.currentPageId))),B(this,"cut",(e=this.selectedIds,t)=>(null==t||t.preventDefault(),this.copy(e,t),this.readOnly||this.delete(e),this)),B(this,"copy",(e=this.selectedIds,t)=>{var i;null==t||t.preventDefault(),this.clipboard=this.getContent(e);let s=`<tldraw>${JSON.stringify(C({type:"tldr/clipboard"},this.clipboard))}</tldraw>`;return function(e){z(this,null,function*(){return(0,u.hZ)(ib,e)})}(s),t&&(null==(i=t.clipboardData)||i.setData("text/html",s)),navigator.clipboard&&window.ClipboardItem&&navigator.clipboard.write([new ClipboardItem({"text/html":new Blob([s],{type:"text/html"})})]),this}),B(this,"paste",(e,t)=>z(this,null,function*(){var i,n;if(this.readOnly)return;let a=[],o=[],r,l=e=>z(this,null,function*(){let t=document.createElement("div");t.innerHTML=e;let i=t.firstChild;i.style.setProperty("background-color","transparent");let s=yield ti.getImageForSvg(i,"svg",{scale:1,quality:1});if(s){let e=new File([s],"image.svg");a.push(e)}else d(e)}),d=t=>{let i=this.getPagePoint(null!=e?e:this.centerPoint,this.currentPageId),n=t.includes(`
`);o.push(ti.getShapeUtil("text").getShape({id:s.Aq.uniqueId(),type:"text",parentId:this.appState.currentPageId,text:ti.normalizeText(t.trim()),point:i,style:A(C({},this.appState.currentStyle),{textAlign:n?"start":this.appState.currentStyle.textAlign})}))},p=e=>{var t;try{let i=null==(t=e.match(/<tldraw>(.*)<\/tldraw>/))?void 0:t[1];if(!i)return;let s=JSON.parse(i);if("tldr/clipboard"===s.type){r=s;return}throw Error("Not tldraw data!")}catch(t){d(e)}};if(void 0!==t){let e=Array.from(null!=(n=null==(i=t.clipboardData)?void 0:i.items)?n:[]);yield Promise.all(e.map(e=>z(this,null,function*(){var t;let{type:i,kind:s}=e;switch(s){case"string":{let s=yield new Promise(t=>e.getAsString(t));switch(i){case"text/html":if(null==(t=s.match(/<tldraw>(.*)<\/tldraw>/))?void 0:t[1])return void p(s);break;case"text/plain":s.startsWith("<svg")?yield l(s):d(s)}break}case"file":{let t=e.getAsFile();t&&a.push(t)}}})))}if(r)return this.insertContent(r,{point:e,select:!0}),this;if(a.length)return this.addMediaFromFiles(a,e),this;if(o.length){let t=this.getPagePoint(null!=e?e:this.centerPoint,this.currentPageId),i=h.l.add(t,[0,0]);return o.forEach((e,t)=>{let s=ti.getBounds(e);0===t&&(i[0]-=s.width/2,i[1]-=s.height/2),e.point=[...i],i[0]+=s.width}),this.createShapes(...o),this}return this.clipboard?this.insertContent(this.clipboard):(function(){return z(this,null,function*(){return(0,u.Jt)(ib)})})().then(e=>{e&&p(e)}),this})),B(this,"getSvg",(...e)=>z(this,[...e],function*(e=this.selectedIds.length?this.selectedIds:Object.keys(this.page.shapes),t={}){if(0===e.length)return;let i=document.createElementNS("http://www.w3.org/2000/svg","svg"),n=document.createElementNS("http://www.w3.org/2000/svg","defs"),a=document.createElementNS("http://www.w3.org/2000/svg","style");if("undefined"!=typeof window&&window.focus(),t.includeFonts)try{let{fonts:e}=yield fetch(i7.assetSrc,{mode:"no-cors"}).then(e=>e.json());a.textContent=`
          @font-face {
            font-family: 'Caveat Brush';
            src: url(data:application/x-font-woff;charset=utf-8;base64,${e.caveat}) format('woff');
            font-weight: 500;
            font-style: normal;
          }
          @font-face {
            font-family: 'Source Code Pro';
            src: url(data:application/x-font-woff;charset=utf-8;base64,${e.source_code_pro}) format('woff');
            font-weight: 500;
            font-style: normal;
          }
          @font-face {
            font-family: 'Source Sans Pro';
            src: url(data:application/x-font-woff;charset=utf-8;base64,${e.source_sans_pro}) format('woff');
            font-weight: 500;
            font-style: normal;
          }
          @font-face {
            font-family: 'Crimson Pro';
            src: url(data:application/x-font-woff;charset=utf-8;base64,${e.crimson_pro}) format('woff');
            font-weight: 500;
            font-style: normal;
          }
          `}catch(e){ti.warn("Could not find tldraw-assets.json file.")}else a.textContent="@import url('https://fonts.googleapis.com/css2?family=Caveat+Brush&family=Source+Code+Pro&family=Source+Sans+Pro&family=Crimson+Pro&display=block');";n.append(a),i.append(n);let o=e.map(e=>this.getShape(e,this.currentPageId)).sort((e,t)=>e.childIndex-t.childIndex),r=s.Aq.getCommonBounds(o.map(ti.getRotatedBounds)),l=t.padding||16,d=e=>{let t=ti.getShapeUtil(e),i=t.getBounds(e),s=t.getSvgElement(e,this.settings.isDarkMode);if(s)return"image"===e.type?s.setAttribute("xlink:href",this.document.assets[e.assetId].src):"video"===e.type&&s.setAttribute("xlink:href",this.serializeVideo(e.id)),s.setAttribute("transform",`translate(${(l+e.point[0]-r.minX).toFixed(2)}, ${(l+e.point[1]-r.minY).toFixed(2)}) rotate(${(180*(e.rotation||0)/Math.PI).toFixed(2)}, ${(i.width/2).toFixed(2)}, ${(i.height/2).toFixed(2)})`),s};o.forEach(e=>{var t;if(null==(t=e.children)?void 0:t.length){let t=document.createElementNS("http://www.w3.org/2000/svg","g");e.children.forEach(e=>{let i=d(this.getShape(e,this.currentPageId));i&&t.append(i)}),i.append(t);return}let s=d(e);s&&i.append(s)}),i.setAttribute("viewBox",[0,0,r.width+2*l,r.height+2*l].join(" ")),i.setAttribute("width",(r.width+2*l).toString()),i.setAttribute("height",(r.height+2*l).toString());let h=this.settings.exportBackground,p="#212529",c="rgb(248, 249, 250)";switch(h){case"auto":i.style.setProperty("background-color",this.settings.isDarkMode?p:c);break;case"dark":i.style.setProperty("background-color",p);break;case"light":i.style.setProperty("background-color",c);break;default:i.style.setProperty("background-color","transparent")}return i.querySelectorAll(".tl-fill-hitarea, .tl-stroke-hitarea, .tl-binding-indicator").forEach(e=>e.remove()),i})),B(this,"copySvg",(...e)=>z(this,[...e],function*(e=this.selectedIds.length?this.selectedIds:Object.keys(this.page.shapes)){if(0===e.length)return;let t=yield this.getSvg(e);if(!t)return;let i=ti.getSvgString(t,1);this.clipboard=this.getContent(e);let s=JSON.stringify(C({type:"tldr/clipboard"},this.clipboard));return navigator.clipboard&&window.ClipboardItem&&navigator.clipboard.write([new ClipboardItem({"text/html":new Blob([s],{type:"text/html"}),"text/plain":new Blob([i],{type:"text/plain"})})]),i})),B(this,"getContent",e=>{let t=this.getPage(this.currentPageId);if(e&&0===e.length||(e||(e=this.selectedIds),0===e.length&&(e=Object.keys(t.shapes)),0===e.length))return;let i=e.map(e=>t.shapes[e]).flatMap(e=>{var i;return[e,...(null!=(i=e.children)?i:[]).map(e=>t.shapes[e])]}).map(e6),s=new Set(i.map(e=>e.id));i.forEach(e=>{e.parentId===this.currentPageId&&(e.parentId="currentPageId")});let n=Object.values(t.bindings).filter(e=>{if(s.has(e.fromId)||s.has(e.toId))return!0;if(s.has(e.fromId)){let t=i.find(t=>t.id===e.fromId).handles;t&&Object.values(t).forEach(t=>{t.bindingId===e.id&&(t.bindingId=void 0)})}if(s.has(e.toId)){let t=i.find(t=>t.id===e.toId).handles;t&&Object.values(t).forEach(t=>{t.bindingId===e.id&&(t.bindingId=void 0)})}return!1}).map(e6),a=[...new Set(i.map(e=>{if(e.assetId)return this.document.assets[e.assetId]}).filter(Boolean).map(e6))];return{shapes:i,bindings:n,assets:a}}),B(this,"copyJson",(e=this.selectedIds)=>{let t=this.getContent(e);return t&&ti.copyStringToClipboard(JSON.stringify(t)),this}),B(this,"exportJson",(e=this.selectedIds)=>{let t=this.getContent(e);if(t){let e=new Blob([JSON.stringify(t)],{type:"application/json"}),i=URL.createObjectURL(e),s=document.createElement("a");s.href=i,s.download="export.json",s.click()}return this}),B(this,"insertContent",(e,t={})=>this.setState(function(e,t,i={}){let{currentPageId:n}=e,{point:a,select:o,overwrite:r}=i,l=e.document.pages[n],d={shapes:{},bindings:{}},p={},c={shapes:{},bindings:{}};if(r){for(let e of t.shapes)d.shapes[e.id]=l.shapes[e.id],c.shapes[e.id]=e;if(t.bindings)for(let e of t.bindings)d.bindings[e.id]=l.bindings[e.id],c.bindings[e.id]=e;if(t.assets)for(let e of t.assets)p[e.id]=e}else{let i={},o=ti.getTopChildIndex(e.state,n),r=t.shapes.sort((e,t)=>e.childIndex-t.childIndex).map(e=>{let t=s.Aq.uniqueId();return i[e.id]=t,A(C({},s.Aq.deepClone(e)),{id:t})}),l=new Set;for(;r.length>0;){let e=r.shift();if(!e)break;if(l.add(e.id),"currentPageId"===e.parentId)e.parentId=n,e.childIndex=o++;else{e.parentId=i[e.parentId];let t=c.shapes[e.parentId];if(!t){l.has(e.id)&&(e.parentId="currentPageId"),r.push(e);continue}t.children.push(e.id)}e.children&&(e.children=[]),d.shapes[e.id]=void 0,c.shapes[e.id]=e}Object.values(c.shapes).forEach(e=>{e.children&&0===e.children.length&&(delete d.shapes[e.id],delete c.shapes[e.id])}),t.bindings&&t.bindings.forEach(e=>{let t=s.Aq.uniqueId();i[e.id]=t;let n=i[e.toId],a=i[e.fromId];if(!n||!a){if(a){let t=c.shapes[a].handles;t&&Object.values(t).forEach(t=>{t.bindingId===e.id&&(t.bindingId=void 0)})}if(n){let t=c.shapes[n].handles;t&&Object.values(t).forEach(t=>{t.bindingId===e.id&&(t.bindingId=void 0)})}return}let o=c.shapes[a].handles;o&&Object.values(o).forEach(i=>{i.bindingId===e.id&&(i.bindingId=t)}),c.shapes[n].handles&&Object.values(c.shapes[n].handles).forEach(i=>{i.bindingId===e.id&&(i.bindingId=t)});let r=A(C({},s.Aq.deepClone(e)),{id:t,toId:n,fromId:a});d.bindings[r.id]=void 0,c.bindings[r.id]=r});let u=Object.values(c.shapes);if(u.length>0)if(a){let e=s.Aq.getCommonBounds(u.map(e=>ti.getBounds(e))),t=s.Aq.getBoundsCenter(e);u.forEach(e=>{e.point&&(e.point=h.l.sub(a,h.l.sub(t,e.point)))})}else{let t=s.Aq.getCommonBounds(u.map(ti.getBounds));if(!(s.Aq.boundsContain(e.viewport,t)||s.Aq.boundsCollide(e.viewport,t))){let i=h.l.toFixed(e.getPagePoint(e.centerPoint)),n=s.Aq.centerBounds(t,i),a=h.l.sub(s.Aq.getBoundsCenter(n),s.Aq.getBoundsCenter(t));u.forEach(e=>{e.point=h.l.toFixed(h.l.add(e.point,a))})}}if(t.assets)for(let e of t.assets)p[e.id]=e}let u=document.createElement("textarea");return Object.values(c.shapes).forEach(e=>{"text"in e&&(u.innerHTML=e.text,e.text=u.value),"label"in e&&(u.innerHTML=e.label,e.label=u.value)}),u.remove(),{id:"insert",before:{document:{pages:{[n]:d},pageStates:{[n]:{selectedIds:[...e.selectedIds]}}}},after:{document:{pages:{[n]:c},assets:p,pageStates:{[n]:{selectedIds:o?Object.keys(c.shapes):[...e.selectedIds]}}}}}}(this,e,t),"insert_content")),B(this,"getImage",(...e)=>z(this,[...e],function*(e="png",t={}){let{ids:i=this.selectedIds.length?this.selectedIds:Object.keys(this.page.shapes)}=t,s=yield this.getSvg(i,{includeFonts:"svg"!==e});if(!s)return;if("svg"===e)return new Blob([ti.getSvgString(s,1)],{type:"image/svg+xml"});let n=yield ti.getImageForSvg(s,e,t);if(n)return n})),B(this,"copyImage",(...e)=>z(this,[...e],function*(e="png",t={}){if("svg"===e)return void this.copySvg(t.ids);if(!(navigator.clipboard&&window.ClipboardItem))return void console.warn("Sorry, your browser does not support copying images.");let i=yield this.getImage(e,t);i&&navigator.clipboard.write([new ClipboardItem({[i.type]:i})])})),B(this,"exportImage",(...e)=>z(this,[...e],function*(e="png",t={}){var i;let{pageId:s=this.currentPageId}=t,n=yield this.getImage(e,t);if(!n)return;let a=null!=(i=this.document.pages[s].name)?i:"export";if(this.callbacks.onExport)this.callbacks.onExport(this,{name:a,type:e,blob:n});else{let t=URL.createObjectURL(n),i=document.createElement("a");i.href=t,i.download=`${a}.${e}`,i.click()}})),B(this,"setCamera",(e,t,i)=>(this.updateViewport(e,t),this.patchState({document:{pageStates:{[this.currentPageId]:{camera:{point:e,zoom:t}}}}},i),this)),B(this,"resetCamera",()=>this.setCamera(this.centerPoint,1,"reset_camera")),B(this,"pan",e=>{let{camera:t}=this.pageState;return this.setCamera(h.l.toFixed(h.l.sub(t.point,e)),t.zoom,"panned")}),B(this,"pinchZoom",(e,t,i)=>{let{camera:s}=this.pageState,n=h.l.sub(s.point,h.l.div(t,s.zoom)),a=h.l.sub(h.l.div(e,s.zoom),n),o=h.l.sub(h.l.div(e,i),n);return this.setCamera(h.l.toFixed(h.l.add(n,h.l.sub(o,a))),i,"pinch_zoomed")}),B(this,"zoomTo",(e,t=this.centerPoint)=>{let{zoom:i,point:s}=this.camera,n=h.l.sub(h.l.div(t,i),s),a=h.l.sub(h.l.div(t,e),s);return this.setCamera(h.l.toFixed(h.l.add(s,h.l.sub(a,n))),e,"zoomed_camera")}),B(this,"zoomIn",()=>{let e=Math.round(100*this.camera.zoom/25),t=ti.getCameraZoom((e+1)*.25);return this.zoomTo(t)}),B(this,"zoomOut",()=>{let e=Math.round(100*this.camera.zoom/25),t=ti.getCameraZoom((e-1)*.25);return this.zoomTo(t)}),B(this,"zoomToFit",()=>{let{shapes:e,pageState:{camera:t}}=this;if(0===e.length)return this;let{rendererBounds:i}=this,n=s.Aq.getCommonBounds(e.map(ti.getBounds)),a=ti.getCameraZoom(Math.min((i.width-128)/n.width,(i.height-128)/n.height));a=t.zoom===a||t.zoom<1?Math.min(1,a):a;let o=(i.width-n.width*a)/2/a,r=(i.height-n.height*a)/2/a;return this.setCamera(h.l.toFixed(h.l.sub([o,r],[n.minX,n.minY])),a,"zoomed_to_fit")}),B(this,"zoomToSelection",()=>{if(0===this.selectedIds.length)return this;let{rendererBounds:e}=this,t=ti.getSelectedBounds(this.state),i=ti.getCameraZoom(Math.min((e.width-128)/t.width,(e.height-128)/t.height));i=this.camera.zoom===i||this.camera.zoom<1?Math.min(1,i):i;let s=(e.width-t.width*i)/2/i,n=(e.height-t.height*i)/2/i;return this.setCamera(h.l.toFixed(h.l.sub([s,n],[t.minX,t.minY])),i,"zoomed_to_selection")}),B(this,"zoomToContent",()=>{let e=this.shapes,t=this.pageState;if(0===e.length)return this;let{rendererBounds:i}=this,{zoom:n}=t.camera,a=s.Aq.getCommonBounds(e.map(ti.getBounds)),o=(i.width-a.width*n)/2/n,r=(i.height-a.height*n)/2/n;return this.setCamera(h.l.toFixed(h.l.sub([o,r],[a.minX,a.minY])),this.camera.zoom,"zoomed_to_content")}),B(this,"resetZoom",()=>this.zoomTo(1)),B(this,"zoomBy",s.Aq.throttle((e,t)=>{let{zoom:i}=this.camera,s=ti.getCameraZoom(i-e*i);return this.zoomTo(s,t)},16)),B(this,"clearSelectHistory",()=>(this.selectHistory.pointer=0,this.selectHistory.stack=[this.selectedIds],this)),B(this,"addToSelectHistory",e=>(this.selectHistory.pointer<this.selectHistory.stack.length&&(this.selectHistory.stack=this.selectHistory.stack.slice(0,this.selectHistory.pointer+1)),this.selectHistory.pointer++,this.selectHistory.stack.push(e),this)),B(this,"setSelectedIds",(e,t=!1)=>{let i=t?[...this.pageState.selectedIds,...e]:[...e];return this.patchState({appState:{activeTool:"select"},document:{pageStates:{[this.currentPageId]:{selectedIds:i}}}},"selected")}),B(this,"undoSelect",()=>(this.selectHistory.pointer>0&&(this.selectHistory.pointer--,this.setSelectedIds(this.selectHistory.stack[this.selectHistory.pointer])),this)),B(this,"redoSelect",()=>(this.selectHistory.pointer<this.selectHistory.stack.length-1&&(this.selectHistory.pointer++,this.setSelectedIds(this.selectHistory.stack[this.selectHistory.pointer])),this)),B(this,"select",(...e)=>(e.forEach(e=>{if(!this.page.shapes[e])throw Error(`That shape does not exist on page ${this.currentPageId}`)}),this.setSelectedIds(e),this.addToSelectHistory(e),this)),B(this,"selectAll",(e=this.currentPageId)=>(this.session||(this.setSelectedIds(Object.values(this.document.pages[e].shapes).filter(t=>t.parentId===e).map(e=>e.id)),this.addToSelectHistory(this.selectedIds),this.selectTool("select")),this)),B(this,"selectNone",()=>(this.setSelectedIds([]),this.addToSelectHistory(this.selectedIds),this)),B(this,"startSession",(e,...t)=>{var i,s;if(this.readOnly&&"brush"!==e)return this;this.session&&(ti.warn(`Already in a session! (${this.session.constructor.name})`),this.cancelSession());let n=iV(e);this.session=new n(this,...t);let a=this.session.start();return a&&this.patchState(a,`session:start_${this.session.constructor.name}`),null==(s=(i=this.callbacks).onSessionStart)||s.call(i,this,this.session.constructor.name),this}),B(this,"updateSession",()=>{let{session:e}=this;if(!e)return this;let t=e.update();return t?this.patchState(t,`session:${null==e?void 0:e.constructor.name}`):this}),B(this,"cancelSession",()=>{var e,t;let{session:i}=this;if(!i)return this;this.session=void 0;let s=i.cancel();return s&&this.patchState(s,`session:cancel:${i.constructor.name}`),this.setEditingId(),null==(t=(e=this.callbacks).onSessionEnd)||t.call(e,this,i.constructor.name),this}),B(this,"completeSession",()=>{var e,t,i,s,n,a,o,r,l;let{session:d}=this;if(!d)return this;this.session=void 0;let h=d.complete();return void 0===h?(this.isCreating=!1,this.patchState({appState:{status:"idle"},document:{pageStates:{[this.currentPageId]:{editingId:void 0,bindingId:void 0,hoveredId:void 0}}}},`session:complete:${d.constructor.name}`)):"after"in h?(this.isCreating&&(h.before={appState:A(C({},h.before.appState),{status:"idle"}),document:{pages:{[this.currentPageId]:{shapes:Object.fromEntries(this.selectedIds.map(e=>[e,void 0]))}},pageStates:{[this.currentPageId]:{selectedIds:[],editingId:null,bindingId:null,hoveredId:null}}}},this.appState.isToolLocked&&(((null==(i=null==(t=null==(e=h.after)?void 0:e.document)?void 0:t.pageStates)?void 0:i[this.currentPageId])||{}).selectedIds=[]),this.isCreating=!1),h.after.appState=A(C({},h.after.appState),{status:"idle"}),h.after.document=A(C({},h.after.document),{pageStates:A(C({},null==(s=h.after.document)?void 0:s.pageStates),{[this.currentPageId]:A(C({},((null==(n=h.after.document)?void 0:n.pageStates)||{})[this.currentPageId]),{editingId:null})})}),this.setState(h,`session:complete:${d.constructor.name}`)):this.patchState(A(C({},h),{appState:A(C({},h.appState),{status:"idle"}),document:A(C({},h.document),{pageStates:{[this.currentPageId]:A(C({},null==(o=null==(a=h.document)?void 0:a.pageStates)?void 0:o[this.currentPageId]),{editingId:null})}})}),`session:complete:${d.constructor.name}`),null==(l=(r=this.callbacks).onSessionEnd)||l.call(r,this,d.constructor.name),this}),B(this,"createShapes",(...e)=>0===e.length?this:this.create(e.map(e=>ti.getShapeUtil(e.type).create(C({parentId:this.currentPageId},e))))),B(this,"updateShapes",(...e)=>{let t=this.document.pages[this.currentPageId].shapes,i=e.filter(e=>t[e.id]);return 0===i.length?this:this.setState(iE(this,i,this.currentPageId),"updated_shapes")}),B(this,"create",(e=[],t=[])=>0===e.length?this:this.setState(iS(this,e,t))),B(this,"patchCreate",(e=[],t=[])=>0===e.length?this:this.patchState(iS(this,e,t).after)),B(this,"delete",(e=this.selectedIds)=>{var t,i;if(0===e.length||this.session)return this;let s=ik(this,e);if(this.callbacks.onAssetDelete&&(null==(t=s.before.document)?void 0:t.assets)&&(null==(i=s.after.document)?void 0:i.assets)){let e=Object.keys(s.before.document.assets).filter(e=>!!s.before.document.assets[e]),t=Object.keys(s.after.document.assets).filter(e=>!!s.after.document.assets[e]);e.filter(e=>!t.includes(e)).forEach(e=>this.callbacks.onAssetDelete(this,e))}return this.setState(s)}),B(this,"deleteAll",()=>(this.selectAll(),this.delete(),this)),B(this,"style",(e,t=this.selectedIds)=>this.setState(function(e,t,i){let{currentPageId:s,selectedIds:n}=e,a=t.flatMap(t=>ti.getDocumentBranch(e.state,t,s)).filter(t=>!e.getShape(t).isLocked),o={},r={};return a.map(t=>e.getShape(t)).filter(e=>!e.isLocked).forEach(t=>{o[t.id]={style:C({},Object.fromEntries(Object.keys(i).map(e=>[e,t.style[e]])))},r[t.id]={style:i},"text"===t.type&&(o[t.id].point=t.point,r[t.id].point=h.l.toFixed(h.l.add(t.point,h.l.sub(e.getShapeUtil(t).getCenter(t),e.getShapeUtil(t).getCenter(A(C({},t),{style:C(C({},t.style),i)}))))))}),{id:"style",before:{document:{pages:{[s]:{shapes:o}},pageStates:{[s]:{selectedIds:n}}},appState:{currentStyle:C({},e.appState.currentStyle)}},after:{document:{pages:{[s]:{shapes:r}},pageStates:{[s]:{selectedIds:t}}},appState:{currentStyle:i}}}}(this,t,e))),B(this,"align",(e,t=this.selectedIds)=>t.length<2?this:this.setState(function(e,t,i){let{currentPageId:n}=e,a=t.map(t=>e.getShape(t)),o=a.map(e=>({id:e.id,point:[...e.point],bounds:ti.getBounds(e)})),r=s.Aq.getCommonBounds(o.map(({bounds:e})=>e)),l=r.minX+r.width/2,d=r.minY+r.height/2,p=Object.fromEntries(o.map(({id:e,point:t,bounds:s})=>[e,{prev:t,next:{top:[t[0],r.minY],centerVertical:[t[0],d-s.height/2],bottom:[t[0],r.maxY-s.height],left:[r.minX,t[1]],centerHorizontal:[l-s.width/2,t[1]],right:[r.maxX-s.width,t[1]]}[i]}])),{before:c,after:u}=ti.mutateShapes(e.state,t,e=>p[e.id]?{point:p[e.id].next}:e,n,!1);return a.forEach(t=>{if("group"===t.type){let i=h.l.sub(u[t.id].point,c[t.id].point);t.children.forEach(t=>{let s=e.getShape(t);c[s.id]={point:s.point},u[s.id]={point:h.l.add(s.point,i)}}),delete c[t.id],delete u[t.id]}}),{id:"align",before:{document:{pages:{[n]:{shapes:c}},pageStates:{[n]:{selectedIds:t}}}},after:{document:{pages:{[n]:{shapes:u}},pageStates:{[n]:{selectedIds:t}}}}}}(this,t,e))),B(this,"distribute",(e,t=this.selectedIds)=>t.length<3?this:this.setState(function(e,t,i){let{currentPageId:n}=e,a=t.map(t=>e.getShape(t)),o=Object.fromEntries((function(e,t){let i=e.map(e=>{let t=ti.getShapeUtil(e);return{id:e.id,point:[...e.point],bounds:t.getBounds(e),center:t.getCenter(e)}}),n=i.length,a=s.Aq.getCommonBounds(i.map(({bounds:e})=>e)),o=[];switch(t){case"horizontal":{let e=i.reduce((e,t)=>e+t.bounds.width,0);if(e>a.width){let e=i.sort((e,t)=>e.bounds.minX-t.bounds.minX)[0],t=i.sort((e,t)=>t.bounds.maxX-e.bounds.maxX)[0],s=i.filter(i=>i!==e&&i!==t).sort((e,t)=>e.center[0]-t.center[0]),a=(t.center[0]-e.center[0])/(n-1),r=e.center[0]+a;s.forEach(({id:e,point:t,bounds:i},s)=>{o.push({id:e,prev:t,next:[r+a*s-i.width/2,i.minY]})})}else{let t=i.sort((e,t)=>e.center[0]-t.center[0]),s=a.minX,r=(a.width-e)/(n-1);t.forEach(({id:e,point:t,bounds:i})=>{o.push({id:e,prev:t,next:[s,i.minY]}),s+=i.width+r})}break}case"vertical":{let e=i.reduce((e,t)=>e+t.bounds.height,0);if(e>a.height){let e=i.sort((e,t)=>e.bounds.minY-t.bounds.minY)[0],t=i.sort((e,t)=>t.bounds.maxY-e.bounds.maxY)[0],s=i.filter(i=>i!==e&&i!==t).sort((e,t)=>e.center[1]-t.center[1]),a=(t.center[1]-e.center[1])/(n-1),r=e.center[1]+a;s.forEach(({id:e,point:t,bounds:i},s)=>{o.push({id:e,prev:t,next:[i.minX,r+a*s-i.height/2]})})}else{let t=i.sort((e,t)=>e.center[1]-t.center[1]),s=a.minY,r=(a.height-e)/(n-1);t.forEach(({id:e,point:t,bounds:i})=>{o.push({id:e,prev:t,next:[i.minX,s]}),s+=i.height+r})}}}return o})(a,i).map(e=>[e.id,e])),{before:r,after:l}=ti.mutateShapes(e.state,t.filter(e=>void 0!==o[e]),e=>{var t;return{point:null==(t=o[e.id])?void 0:t.next}},n);return a.forEach(t=>{if("group"===t.type){let i=h.A.sub(l[t.id].point,r[t.id].point);t.children.forEach(t=>{let s=e.getShape(t);r[s.id]={point:s.point},l[s.id]={point:h.A.add(s.point,i)}}),delete r[t.id],delete l[t.id]}}),{id:"distribute",before:{document:{pages:{[n]:{shapes:r}},pageStates:{[n]:{selectedIds:t}}}},after:{document:{pages:{[n]:{shapes:l}},pageStates:{[n]:{selectedIds:t}}}}}}(this,t,e))),B(this,"stretch",(e,t=this.selectedIds)=>t.length<2?this:this.setState(function(e,t,i){let{currentPageId:n,selectedIds:a}=e,o=t.map(t=>e.getShape(t)),r=o.map(e=>ti.getBounds(e)),l=s.Aq.getCommonBounds(r),d=t.flatMap(t=>{let i=e.getShape(t);return i.children?i.children:i.id}).filter(t=>!e.getShape(t).isLocked),{before:h,after:p}=ti.mutateShapes(e.state,d,e=>{let t=ti.getBounds(e);switch(i){case"horizontal":{let i=A(C({},t),{minX:l.minX,maxX:l.maxX,width:l.width});return ti.getShapeUtil(e).transformSingle(e,i,{type:s.NW.TopLeft,scaleX:i.width/t.width,scaleY:1,initialShape:e,transformOrigin:[.5,.5]})}case"vertical":{let i=A(C({},t),{minY:l.minY,maxY:l.maxY,height:l.height});return ti.getShapeUtil(e).transformSingle(e,i,{type:s.NW.TopLeft,scaleX:1,scaleY:i.height/t.height,initialShape:e,transformOrigin:[.5,.5]})}}},n);return o.forEach(e=>{"group"===e.type&&(delete h[e.id],delete p[e.id])}),{id:"stretch",before:{document:{pages:{[n]:{shapes:h}},pageStates:{[n]:{selectedIds:a}}}},after:{document:{pages:{[n]:{shapes:p}},pageStates:{[n]:{selectedIds:t}}}}}}(this,t,e))),B(this,"flipHorizontal",(e=this.selectedIds)=>0===e.length?this:this.setState(iw(this,e,"horizontal"))),B(this,"flipVertical",(e=this.selectedIds)=>0===e.length?this:this.setState(iw(this,e,"vertical"))),B(this,"moveToPage",(e,t=this.currentPageId,i=this.selectedIds)=>{if(0===i.length)return this;let{rendererBounds:n}=this;return this.setState(function(e,t,i,n,a){let{page:o}=e,r={before:{shapes:{},bindings:{}},after:{shapes:{},bindings:{}}},l={before:{shapes:{},bindings:{}},after:{shapes:{},bindings:{}}},d=new Set,p=new Set;t.map(t=>e.getShape(t,n)).filter(e=>!e.isLocked).forEach(t=>{d.add(t.id),p.add(t),void 0!==t.children&&t.children.forEach(t=>{d.add(t),p.add(e.getShape(t,n))})});let c=ti.getTopChildIndex(e.state,a),u=Array.from(p.values());u.forEach((t,i)=>{if(r.before.shapes[t.id]=t,r.after.shapes[t.id]=void 0,l.before.shapes[t.id]=void 0,l.after.shapes[t.id]=t,!d.has(t.parentId)&&(l.after.shapes[t.id]=A(C({},t),{parentId:a,childIndex:c+i}),t.parentId!==n)){let i=e.getShape(t.parentId,n);r.before.shapes[i.id]={children:i.children},r.after.shapes[i.id]={children:i.children.filter(e=>e!==t.id)}}}),Object.values(o.bindings).filter(e=>d.has(e.fromId)||d.has(e.toId)).forEach(t=>{r.before.bindings[t.id]=t,r.after.bindings[t.id]=void 0;let i=e.getShape(t.fromId,n);if(d.has(t.fromId)&&d.has(t.toId))l.before.bindings[t.id]=void 0,l.after.bindings[t.id]=t;else if(d.has(t.fromId)){let s=e.getShape(t.fromId,n),a=Object.values(i.handles).find(e=>e.bindingId===t.id).id,o=l.after.shapes[s.id];o.handles=A(C({},o.handles),{[a]:A(C({},o.handles[a]),{bindingId:void 0})})}else{let s=e.getShape(t.fromId,n),a=Object.values(i.handles).find(e=>e.bindingId===t.id);r.before.shapes[s.id]={handles:{[a.id]:{bindingId:t.id}}},r.after.shapes[s.id]={handles:{[a.id]:{bindingId:void 0}}}}});let g=e.state.document.pageStates[a],m=s.Aq.getCommonBounds(u.map(e=>ti.getBounds(e))),f=ti.getCameraZoom(i.width<i.height?(i.width-128)/m.width:(i.height-128)/m.height),v=(i.width-m.width*f)/2/f,b=(i.height-m.height*f)/2/f,y=h.l.toFixed(h.l.add([-m.minX,-m.minY],[v,b]));return{id:"move_to_page",before:{appState:{currentPageId:n},document:{pages:{[n]:r.before,[a]:l.before},pageStates:{[n]:{selectedIds:t},[a]:{selectedIds:g.selectedIds,camera:g.camera}}}},after:{appState:{currentPageId:a},document:{pages:{[n]:r.after,[a]:l.after},pageStates:{[n]:{selectedIds:[]},[a]:{selectedIds:t,camera:{zoom:f,point:y}}}}}}}(this,i,n,t,e)),this}),B(this,"moveToBack",(e=this.selectedIds)=>0===e.length?this:this.setState(ix(this,e,"toBack"))),B(this,"moveBackward",(e=this.selectedIds)=>0===e.length?this:this.setState(ix(this,e,"backward"))),B(this,"moveForward",(e=this.selectedIds)=>0===e.length?this:this.setState(ix(this,e,"forward"))),B(this,"moveToFront",(e=this.selectedIds)=>0===e.length?this:this.setState(ix(this,e,"toFront"))),B(this,"nudge",(e,t=!1,i=this.selectedIds)=>{if(0===i.length)return this;let s=t?this.settings.showGrid?4*this.currentGrid:10:this.settings.showGrid?this.currentGrid:1;return this.setState(function(e,t,i){let{currentPageId:s,selectedIds:n}=e;e.rotationInfo.selectedIds=[...n];let a={shapes:{},bindings:{}},o={shapes:{},bindings:{}},r=t.flatMap(t=>{let i=e.getShape(t);return i.children?i.children:i.id}).filter(t=>!e.getShape(t).isLocked),l=ti.mutateShapes(e.state,r,e=>({point:h.l.toFixed(h.l.add(e.point,i))}),s);return a.shapes=l.before,o.shapes=l.after,ti.getBindings(e.state,s).filter(e=>t.includes(e.fromId)&&!t.includes(e.toId)).forEach(t=>{for(let i of(a.bindings[t.id]=t,o.bindings[t.id]=void 0,[t.toId,t.fromId])){let s=e.getShape(i);s.handles&&Object.values(s.handles).filter(e=>e.bindingId===t.id).forEach(e=>{var s,n;a.shapes[i]=A(C({},a.shapes[i]),{handles:A(C({},null==(s=a.shapes[i])?void 0:s.handles),{[e.id]:{bindingId:t.id}})}),o.shapes[i]=A(C({},o.shapes[i]),{handles:A(C({},null==(n=o.shapes[i])?void 0:n.handles),{[e.id]:{bindingId:void 0}})})})}}),{id:"translate",before:{document:{pages:{[s]:a},pageStates:{[s]:{selectedIds:t}}}},after:{document:{pages:{[s]:o},pageStates:{[s]:{selectedIds:t}}}}}}(this,i,h.l.mul(e,s)))}),B(this,"duplicate",(e=this.selectedIds,t)=>this.readOnly||0===e.length?this:this.setState(function(e,t,i){let{selectedIds:n,currentPageId:a,page:o,shapes:r}=e,l={shapes:{},bindings:{}},d={shapes:{},bindings:{}},p={},c=t.map(t=>e.getShape(t)).filter(e=>!t.includes(e.parentId));c.forEach(t=>{let i=s.Aq.uniqueId();if(l.shapes[i]=void 0,d.shapes[i]=A(C({},s.Aq.deepClone(t)),{id:i,childIndex:ti.getChildIndexAbove(e.state,t.id,a)}),t.children&&(d.shapes[i].children=[]),t.parentId!==a){let s=e.getShape(t.parentId);l.shapes[s.id]=A(C({},l.shapes[s.id]),{children:s.children}),d.shapes[s.id]=A(C({},d.shapes[s.id]),{children:[...(d.shapes[s.id]||s).children,i]})}p[t.id]=i}),c.forEach(t=>{t.children&&t.children.forEach(i=>{var n,o;let r=e.getShape(i),h=s.Aq.uniqueId(),c=p[t.id];l.shapes[h]=void 0,d.shapes[h]=A(C({},s.Aq.deepClone(r)),{id:h,parentId:c,childIndex:ti.getChildIndexAbove(e.state,r.id,a)}),p[i]=h,null==(o=null==(n=d.shapes[p[t.id]])?void 0:n.children)||o.push(h)})});let u=new Set(Object.keys(p));Object.values(o.bindings).filter(e=>u.has(e.fromId)||u.has(e.toId)).forEach(e=>{if(u.has(e.fromId))if(u.has(e.toId)){let t=s.Aq.uniqueId(),i=A(C({},s.Aq.deepClone(e)),{id:t,fromId:p[e.fromId],toId:p[e.toId]});l.bindings[t]=void 0,d.bindings[t]=i,Object.values(d.shapes[i.fromId].handles).forEach(i=>{i.bindingId===e.id&&(i.bindingId=t)})}else Object.values(d.shapes[p[e.fromId]].handles).forEach(t=>{t.bindingId===e.id&&(t.bindingId=void 0)})});let g=Object.values(d.shapes);if(i){let e=s.Aq.getCommonBounds(g.map(e=>ti.getBounds(e))),t=s.Aq.getBoundsCenter(e);g.forEach(e=>{e.point&&(e.point=h.l.sub(i,h.l.sub(t,e.point)))})}else{let e=[16,16];g.forEach(t=>{t.point&&(t.point=h.l.add(t.point,e))})}return g.forEach(e=>{e.isLocked&&(e.isLocked=!1)}),{id:"duplicate",before:{document:{pages:{[a]:l},pageStates:{[a]:{selectedIds:n}}}},after:{document:{pages:{[a]:d},pageStates:{[a]:{selectedIds:Array.from(u.values()).map(e=>p[e])}}}}}}(this,e,t))),B(this,"resetBounds",(e=this.selectedIds)=>{let t=iP(this,e,this.currentPageId);return this.setState(iP(this,e,this.currentPageId),t.id)}),B(this,"toggleHidden",(e=this.selectedIds)=>0===e.length?this:this.setState(iA(this,e,"isHidden"))),B(this,"toggleLocked",(e=this.selectedIds)=>0===e.length?this:this.setState(iA(this,e,"isLocked"))),B(this,"toggleAspectRatioLocked",(e=this.selectedIds)=>0===e.length?this:this.setState(iA(this,e,"isAspectRatioLocked"))),B(this,"toggleDecoration",(e,t=this.selectedIds)=>0===t.length||"start"!==e&&"end"!==e?this:this.setState(function(e,t,i){let{currentPageId:s,selectedIds:n}=e;return{id:"toggle_decorations",before:{document:{pages:{[s]:{shapes:Object.fromEntries(t.map(t=>{var s;return[t,{decorations:{[i]:null==(s=e.getShape(t).decorations)?void 0:s[i]}}]}))}},pageStates:{[s]:{selectedIds:n}}}},after:{document:{pages:{[s]:{shapes:Object.fromEntries(t.filter(t=>!e.getShape(t).isLocked).map(t=>{var s;return[t,{decorations:{[i]:(null==(s=e.getShape(t).decorations)?void 0:s[i])?void 0:"arrow"}}]}))}},pageStates:{[s]:{selectedIds:t}}}}}}(this,t,e))),B(this,"setShapeProps",(e,t=this.selectedIds)=>this.setState(function(e,t,i){let{currentPageId:s,selectedIds:n}=e,a=t.map(t=>e.getShape(t)).filter(e=>!!i.isLocked||!e.isLocked),o={},r={},l=Object.keys(i);return a.forEach(e=>{o[e.id]=Object.fromEntries(l.map(t=>[t,e[t]])),r[e.id]=i}),{id:"set_props",before:{document:{pages:{[s]:{shapes:o}},pageStates:{[s]:{selectedIds:n}}}},after:{document:{pages:{[s]:{shapes:r}},pageStates:{[s]:{selectedIds:n}}}}}}(this,t,e))),B(this,"rotate",(e=-.5*Math.PI,t=this.selectedIds)=>{if(0===t.length)return this;let i=function(e,t,i=-iC/4){let{currentPageId:n}=e,a={},o={},r=t.flatMap(t=>{let i=e.getShape(t);return i.children?i.children.map(t=>e.getShape(t)):i}).filter(e=>!e.isLocked),l=s.Aq.getBoundsCenter(s.Aq.getCommonBounds(r.map(e=>ti.getBounds(e))));return r.forEach(e=>{let t=ti.getRotatedShapeMutation(e,ti.getCenter(e),l,i);t&&(a[e.id]=ti.getBeforeShape(e,t),o[e.id]=t)}),{id:"rotate",before:{document:{pages:{[n]:{shapes:a}},pageStates:{[n]:{selectedIds:t}}}},after:{document:{pages:{[n]:{shapes:o}},pageStates:{[n]:{selectedIds:t}}}}}}(this,t,e);return i?this.setState(i):this}),B(this,"group",(e=this.selectedIds,t=s.Aq.uniqueId(),i=this.currentPageId)=>{if(this.readOnly)return this;if(1===e.length&&"group"===this.getShape(e[0],i).type)return this.ungroup(e,i);if(e.length<2)return this;let n=function(e,t,i,n){var a,o;if(t.length<2)return;let r={},l={},d={},h={},p=[...t],c=[],u=[],g=[];for(let i of t){let t=e.getShape(i);if(!t.isLocked)if(void 0===t.children)c.push(t);else{let i=t.children.filter(t=>!e.getShape(t).isLocked);g.push(t),p.push(...i),c.push(...i.map(t=>e.getShape(t)).filter(Boolean))}}if(c.every(e=>e.parentId===c[0].parentId)&&c[0].parentId!==n&&(null==(a=e.getShape(c[0].parentId).children)?void 0:a.length)===p.length)return;let m=ti.flattenPage(e.state,n),f=Object.fromEntries(c.map(e=>[e.id,m.indexOf(e)])),v=c.sort((e,t)=>f[e.id]-f[t.id]),b=(v.filter(e=>e.parentId===n)[0]||v[0]).childIndex,y=s.Aq.getCommonBounds(c.map(e=>ti.getBounds(e)));for(r[i]=void 0,l[i]=ti.getShapeUtil("group").create({id:i,childIndex:b,parentId:n,point:[y.minX,y.minY],size:[y.width,y.height],children:v.map(e=>e.id)}),v.forEach((t,s)=>{if(t.parentId!==n){let i=e.getShape(t.parentId);g.push(i)}r[t.id]=A(C({},r[t.id]),{parentId:t.parentId,childIndex:t.childIndex}),l[t.id]=A(C({},l[t.id]),{parentId:i,childIndex:s+1})});g.length>0;){let t=g.pop();if(!t)break;let i=((null==(o=r[t.id])?void 0:o.children)||t.children).filter(e=>e&&!(p.includes(e)||u.includes(e)));0===i.length?(r[t.id]=t,l[t.id]=void 0,t.parentId!==n&&(u.push(t.id),g.push(e.getShape(t.parentId)))):(r[t.id]=A(C({},r[t.id]),{children:t.children}),l[t.id]=A(C({},l[t.id]),{children:i}))}let{bindings:S}=e,I=new Set(u);return S.forEach(t=>{for(let i of[t.toId,t.fromId])if(I.has(i)){d[t.id]=t,h[t.id]=void 0;let s=e.getShape(i);s.handles&&Object.values(s.handles).filter(e=>e.bindingId===t.id).forEach(e=>{var s,n;r[i]=A(C({},r[i]),{handles:A(C({},null==(s=r[i])?void 0:s.handles),{[e.id]:{bindingId:t.id}})}),u.includes(i)||(l[i]=A(C({},l[i]),{handles:A(C({},null==(n=l[i])?void 0:n.handles),{[e.id]:{bindingId:void 0}})}))})}}),{id:"group",before:{document:{pages:{[n]:{shapes:r,bindings:d}},pageStates:{[n]:{selectedIds:t}}}},after:{document:{pages:{[n]:{shapes:l,bindings:d}},pageStates:{[n]:{selectedIds:[i]}}}}}}(this,e,t,i);return n?this.setState(n):this}),B(this,"ungroup",(e=this.selectedIds,t=this.currentPageId)=>{if(this.readOnly)return this;let i=e.map(e=>this.getShape(e,t)).filter(e=>"group"===e.type);if(0===i.length)return this;let s=function(e,t,i,s){let{bindings:n}=e,a={},o={},r={},l={},d=t.filter(e=>!i.find(t=>t.id===e));return i.filter(e=>!e.isLocked).forEach(t=>{let i=[],h=[];a[t.id]=t,o[t.id]=void 0,t.children.forEach(t=>{d.push(t);let n=e.getShape(t,s);i.push(n)});let p=t.childIndex,c=(ti.getChildIndexAbove(e.state,t.id,s)-p)/i.length;i.sort((e,t)=>e.childIndex-t.childIndex).forEach((e,t)=>{a[e.id]={parentId:e.parentId,childIndex:e.childIndex},o[e.id]={parentId:s,childIndex:p+c*t}}),n.filter(e=>e.toId===t.id||e.fromId===t.id).forEach(t=>{for(let i of[t.toId,t.fromId])if(void 0===o[i]){r[t.id]=t,l[t.id]=void 0;let n=e.getShape(i,s);n.handles&&Object.values(n.handles).filter(e=>e.bindingId===t.id).forEach(e=>{var s,n;a[i]=A(C({},a[i]),{handles:A(C({},null==(s=a[i])?void 0:s.handles),{[e.id]:{bindingId:t.id}})}),h.includes(i)||(o[i]=A(C({},o[i]),{handles:A(C({},null==(n=o[i])?void 0:n.handles),{[e.id]:{bindingId:void 0}})}))})}})}),{id:"ungroup",before:{document:{pages:{[s]:{shapes:a,bindings:r}},pageStates:{[s]:{selectedIds:t}}}},after:{document:{pages:{[s]:{shapes:o,bindings:r}},pageStates:{[s]:{selectedIds:d}}}}}}(this,e,i,t);return s?this.setState(s):this}),B(this,"cancel",()=>{var e,t;return null==(t=(e=this.currentTool).onCancel)||t.call(e),this}),B(this,"addMediaFromFiles",(e,...t)=>z(this,[e,...t],function*(e,t=this.centerPoint){this.setIsLoading(!0);let i=[],n=this.getPagePoint(t);for(let n of e){let e=s.Aq.uniqueId(),a=n.name.match(/\.[0-9a-z]+$/i);if(!a)throw Error("No extension");let o=eS.includes(a[0].toLowerCase()),r=eI.includes(a[0].toLowerCase());if(!(o||r))throw Error("Wrong extension");let l=o?"image":"video",d=o?"image":"video",p;try{if(this.callbacks.onAssetCreate){let t=yield this.callbacks.onAssetCreate(this,n,e);if(!t)throw Error("Asset creation callback returned false");p=t}else p=yield iR(n);if("string"==typeof p){let s=[0,0];if(o){if(".svg"==a[0]){let e,t=yield iq(n),i=this.getViewboxFromSVG(t);i&&(e=i.split(" "),s[0]=parseFloat(e[2]),s[1]=parseFloat(e[3]))}h.l.isEqual(s,[0,0])&&(s=yield iH(p))}else s=yield iU(p);let r=Object.values(this.document.assets).find(e=>e.type===d&&e.src===p),c;if(r)c=r.id;else{let t={id:c=e,type:d,name:n.name,src:p,size:s};this.patchState({document:{assets:{[c]:t}}})}i.push(this.getImageOrVideoShapeAtPoint(e,l,t,s,c))}}catch(e){console.warn(e)}}if(i.length){let e=h.l.add(n,[0,0]);i.forEach((t,i)=>{let s=ti.getBounds(t);0===i&&(e[0]-=s.width/2,e[1]-=s.height/2),t.point=[...e],e[0]+=s.width});let t=s.Aq.getCommonBounds(i.map(ti.getBounds));this.createShapes(...i),s.Aq.boundsContain(this.viewport,t)||(this.zoomToSelection(),this.zoom>1&&this.resetZoom())}return this.setIsLoading(!1),this})),B(this,"getViewboxFromSVG",e=>{if("string"==typeof e){let t=e.match(/.*?viewBox=["'](-?[\d.]+[, ]+-?[\d.]+[, ][\d.]+[, ][\d.]+)["']/);return t&&t.length>=2?t[1]:null}return this.setIsLoading(!1),null}),B(this,"onKeyDown",(e,t,i)=>{var s,n;switch(i.key){case"/":if("idle"===this.status&&!this.pageState.editingId){let{shiftKey:e,metaKey:i,altKey:s,ctrlKey:n,spaceKey:a}=this;this.onPointerDown({target:"canvas",pointerId:0,origin:t.point,point:t.point,delta:[0,0],pressure:.5,shiftKey:e,ctrlKey:n,metaKey:i,altKey:s,spaceKey:a},{shiftKey:e,altKey:s,ctrlKey:n,pointerId:0,clientX:t.point[0],clientY:t.point[1]})}break;case"Escape":this.cancel();break;case"Meta":this.metaKey=!0;break;case"Alt":this.altKey=!0;break;case"Control":this.ctrlKey=!0;break;case" ":this.isForcePanning=!0,this.spaceKey=!0}return null==(n=(s=this.currentTool).onKeyDown)||n.call(s,e,t,i),this}),B(this,"onKeyUp",(e,t,i)=>{var s,n;if(t){switch(i.key){case"/":{let{currentPoint:e,shiftKey:t,metaKey:i,altKey:s,ctrlKey:n,spaceKey:a}=this;this.onPointerUp({target:"canvas",pointerId:0,origin:e,point:e,delta:[0,0],pressure:.5,shiftKey:t,ctrlKey:n,metaKey:i,altKey:s,spaceKey:a},{shiftKey:t,altKey:s,ctrlKey:n,pointerId:0,clientX:e[0],clientY:e[1]});break}case"Meta":this.metaKey=!1;break;case"Alt":this.altKey=!1;break;case"Control":this.ctrlKey=!1;break;case" ":this.isForcePanning=!1,this.spaceKey=!1}null==(n=(s=this.currentTool).onKeyUp)||n.call(s,e,t,i)}}),B(this,"refreshBoundingBoxes",()=>{let e=this.shapes.map(e=>[e.id,C({point:[...e.point]},"label"in e&&{label:""})]),t=this.shapes.map(e=>[e.id,C({point:[...e.point]},"label"in e&&{label:e.label})]);eB(),this.patchState({document:{pages:{[this.currentPageId]:{shapes:Object.fromEntries(e)}}}}),this.patchState({document:{pages:{[this.currentPageId]:{shapes:Object.fromEntries(t)}}}})}),B(this,"onDragOver",e=>{e.preventDefault()}),B(this,"onDrop",e=>z(this,null,function*(){var t;return e.preventDefault(),this.disableAssets||(null==(t=e.dataTransfer.files)?void 0:t.length)&&this.addMediaFromFiles(Object.values(e.dataTransfer.files),[e.clientX,e.clientY]),this})),B(this,"onPinchStart",(e,t)=>{var i,s;null==(s=(i=this.currentTool).onPinchStart)||s.call(i,e,t)}),B(this,"onPinchEnd",(e,t)=>{var i,s;return null==(s=(i=this.currentTool).onPinchEnd)?void 0:s.call(i,e,t)}),B(this,"onPinch",(e,t)=>{var i,s;return null==(s=(i=this.currentTool).onPinch)?void 0:s.call(i,e,t)}),B(this,"onPan",(e,t)=>{if("pinching"===this.appState.status)return;let i=h.l.div(e.delta,this.camera.zoom),s=this.camera.point,n=h.l.sub(s,i);h.l.isEqual(n,s)||(this.pan(i),this.isForcePanning||this.onPointerMove(e,t),ey&&this.isForcePanning&&this.preventPaste())}),B(this,"onZoom",(e,t)=>{if("idle"!==this.state.appState.status)return;let i=e.delta[2]/50;this.zoomBy(i,e.point),this.onPointerMove(e,t)}),B(this,"updateInputs",e=>{this.currentPoint=this.getPagePoint(e.point).concat(e.pressure),this.shiftKey=e.shiftKey,this.altKey=e.altKey,this.ctrlKey=e.ctrlKey,this.metaKey=e.metaKey}),B(this,"onPointerMove",(e,t)=>{var i,s,n,a,o;if(this.previousPoint=this.currentPoint,this.updateInputs(e,t),this.isForcePanning&&this.isPointing){null==(i=this.onPan)||i.call(this,A(C({},e),{delta:h.l.neg(e.delta)}),t);return}if(null==(n=(s=this.currentTool).onPointerMove)||n.call(s,e,t),this.state.room){let{users:t,userId:i}=this.state.room;null==(o=(a=this.callbacks).onChangePresence)||o.call(a,this,A(C({},t[i]),{point:this.getPagePoint(e.point),session:!!this.session}))}}),B(this,"onPointerDown",(e,t)=>{var i,s;if(4===t.buttons)this.isForcePanning=!0;else if(this.isPointing)return;this.isPointing=!0,this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),this.isForcePanning||null==(s=(i=this.currentTool).onPointerDown)||s.call(i,e,t)}),B(this,"onPointerUp",(e,t)=>{var i,s;this.isPointing=!1,this.shiftKey||(this.isForcePanning=!1),this.updateInputs(e,t),null==(s=(i=this.currentTool).onPointerUp)||s.call(i,e,t)}),B(this,"onPointCanvas",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onPointCanvas)||s.call(i,e,t)}),B(this,"onDoubleClickCanvas",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onDoubleClickCanvas)||s.call(i,e,t)}),B(this,"onRightPointCanvas",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onRightPointCanvas)||s.call(i,e,t)}),B(this,"onDragCanvas",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onDragCanvas)||s.call(i,e,t)}),B(this,"onReleaseCanvas",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onReleaseCanvas)||s.call(i,e,t)}),B(this,"onPointShape",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onPointShape)||s.call(i,e,t)}),B(this,"onReleaseShape",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onReleaseShape)||s.call(i,e,t)}),B(this,"onDoubleClickShape",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onDoubleClickShape)||s.call(i,e,t)}),B(this,"onRightPointShape",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onRightPointShape)||s.call(i,e,t)}),B(this,"onDragShape",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onDragShape)||s.call(i,e,t)}),B(this,"onHoverShape",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onHoverShape)||s.call(i,e,t)}),B(this,"onUnhoverShape",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onUnhoverShape)||s.call(i,e,t)}),B(this,"onPointBounds",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onPointBounds)||s.call(i,e,t)}),B(this,"onDoubleClickBounds",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onDoubleClickBounds)||s.call(i,e,t)}),B(this,"onRightPointBounds",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onRightPointBounds)||s.call(i,e,t)}),B(this,"onDragBounds",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onDragBounds)||s.call(i,e,t)}),B(this,"onHoverBounds",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onHoverBounds)||s.call(i,e,t)}),B(this,"onUnhoverBounds",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onUnhoverBounds)||s.call(i,e,t)}),B(this,"onReleaseBounds",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onReleaseBounds)||s.call(i,e,t)}),B(this,"onPointBoundsHandle",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onPointBoundsHandle)||s.call(i,e,t)}),B(this,"onDoubleClickBoundsHandle",(e,t)=>{var i,s;if(this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onDoubleClickBoundsHandle)||s.call(i,e,t),1!==this.selectedIds.length)return;let n=this.getShape(this.selectedIds[0]);if("image"===n.type||"video"===n.type){let e=this.document.assets[n.assetId],t=ti.getShapeUtil(n),i=t.getCenter(n),s=t.getCenter(A(C({},n),{size:e.size})),a=h.l.sub(s,i);this.updateShapes({id:n.id,point:h.l.sub(n.point,a),size:e.size})}}),B(this,"onRightPointBoundsHandle",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onRightPointBoundsHandle)||s.call(i,e,t)}),B(this,"onDragBoundsHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onDragBoundsHandle)||s.call(i,e,t)}),B(this,"onHoverBoundsHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onHoverBoundsHandle)||s.call(i,e,t)}),B(this,"onUnhoverBoundsHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onUnhoverBoundsHandle)||s.call(i,e,t)}),B(this,"onReleaseBoundsHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onReleaseBoundsHandle)||s.call(i,e,t)}),B(this,"onPointHandle",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onPointHandle)||s.call(i,e,t)}),B(this,"onDoubleClickHandle",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onDoubleClickHandle)||s.call(i,e,t)}),B(this,"onRightPointHandle",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onRightPointHandle)||s.call(i,e,t)}),B(this,"onDragHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onDragHandle)||s.call(i,e,t)}),B(this,"onHoverHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onHoverHandle)||s.call(i,e,t)}),B(this,"onUnhoverHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onUnhoverHandle)||s.call(i,e,t)}),B(this,"onReleaseHandle",(e,t)=>{var i,s;this.updateInputs(e,t),null==(s=(i=this.currentTool).onReleaseHandle)||s.call(i,e,t)}),B(this,"onShapeChange",e=>{let t=iE(this,[C(C({},this.document.pages[this.currentPageId].shapes[e.id]),e)],this.currentPageId).after;return this.patchState(t,"patched_shapes")}),B(this,"onShapeBlur",()=>{var e,t;if(performance.now()-this.editingStartTime<50)return;let{editingId:i}=this.pageState,{isToolLocked:s}=this.getAppState();if(i){let e=this.getShape(i);this.setEditingId(),"text"===e.type&&(e.text.trim().length<=0?this.patchState(ik(this,[i]).after,"delete_empty_text"):s||this.select(i))}null==(t=(e=this.currentTool).onShapeBlur)||t.call(e)}),B(this,"onShapeClone",(e,t)=>{var i,s;this.originPoint=this.getPagePoint(e.point).concat(e.pressure),this.updateInputs(e,t),null==(s=(i=this.currentTool).onShapeClone)||s.call(i,e,t)}),B(this,"onRenderCountChange",e=>{let t=this.getAppState();t.isEmptyCanvas&&e.length>0?this.patchState({appState:{isEmptyCanvas:!1}},"empty_canvas:false"):!t.isEmptyCanvas&&e.length<=0&&this.patchState({appState:{isEmptyCanvas:!0}},"empty_canvas:true")}),B(this,"onError",()=>{}),B(this,"getShapeUtil",ti.getShapeUtil),this.callbacks=t}setStatus(e){return this.patchState({appState:{status:e}},`set_status:${e}`)}get isMenuOpen(){return this.appState.isMenuOpen}get isLoading(){return this.appState.isLoading}get disableAssets(){return this.appState.disableAssets}get history(){return this.stack.slice(0,this.pointer+1)}set history(e){this.replaceHistory(e)}get document(){return this.state.document}get settings(){return this.state.settings}get appState(){return this.state.appState}get currentPageId(){return this.state.appState.currentPageId}get page(){return this.state.document.pages[this.currentPageId]}get shapes(){return Object.values(this.page.shapes)}get bindings(){return Object.values(this.page.bindings)}get assets(){return Object.values(this.document.assets)}get pageState(){return this.state.document.pageStates[this.currentPageId]}get camera(){return this.pageState.camera}get zoom(){return this.pageState.camera.zoom}get selectedIds(){return this.pageState.selectedIds}createTextShapeAtPoint(e,t,i){let{shapes:n,appState:{currentPageId:a,currentStyle:o}}=this,r=0===n.length?1:n.filter(e=>e.parentId===a).sort((e,t)=>t.childIndex-e.childIndex)[0].childIndex+1,l=im.text,d=l.create({id:t||s.Aq.uniqueId(),parentId:a,childIndex:r,point:e,style:C({},o)}),p=l.getBounds(d);return d.point=h.l.sub(d.point,[p.width/2,p.height/2]),i?this.patchCreate([ti.getShapeUtil(d.type).create(d)]):this.createShapes(d),this.setEditingId(d.id,!0),this}getImageOrVideoShapeAtPoint(e,t,i,s,n){let{shapes:a,appState:{currentPageId:o,currentStyle:r}}=this,l=0===a.length?1:a.filter(e=>e.parentId===o).sort((e,t)=>t.childIndex-e.childIndex)[0].childIndex+1,d=im[t];if(s[0]>this.viewport.width){let e=s[1]/s[0];s[0]=this.viewport.width-128/this.camera.zoom*2,s[1]=s[0]*e,(s[1]<32||s[1]<32)&&(s[1]=32,s[0]=s[1]/e)}else if(s[1]>this.viewport.height){let e=s[0]/s[1];s[1]=this.viewport.height-128/this.camera.zoom*2,s[0]=s[1]*e,(s[1]<32||s[1]<32)&&(s[0]=32,s[1]=s[0]/e)}return d.create({id:e,parentId:o,childIndex:l,point:i,size:s,style:C({},r),assetId:n})}isSelected(e){return this.selectedIds.includes(e)}serializeVideo(e){let t=document.getElementById(e+"_video");if(t){let e=document.createElement("canvas");return e.width=t.videoWidth,e.height=t.videoHeight,e.getContext("2d").drawImage(t,0,0),e.toDataURL("image/png")}throw Error("Video with id "+e+" not found")}serializeImage(e){let t=document.getElementById(e+"_image");if(t){let e=document.createElement("canvas");return e.width=t.width,e.height=t.height,e.getContext("2d").drawImage(t,0,0),e.toDataURL("image/png")}throw Error("Image with id "+e+" not found")}patchAssets(e){this.document.assets=C(C({},this.document.assets),e)}get room(){return this.state.room}get isLocal(){return void 0===this.state.room||"local"===this.state.room.id}get status(){return this.appState.status}get currentUser(){if(this.state.room)return this.state.room.users[this.state.room.userId]}get centerPoint(){let{width:e,height:t}=this.rendererBounds;return h.l.toFixed([e/2,t/2])}get currentGrid(){let{zoom:e}=this.camera;return e<.15?128:e<1?32:8}},se=i7;B(se,"version",15.5),B(se,"defaultDocument",{id:"doc",name:"New Document",version:i7.version,pages:{page:{id:"page",name:"Page 1",childIndex:1,shapes:{},bindings:{}}},pageStates:{page:{id:"page",selectedIds:[],camera:{point:[0,0],zoom:1}}},assets:{}}),B(se,"defaultState",{settings:{isCadSelectMode:!1,isPenMode:!1,isDarkMode:!1,isZoomSnap:!1,isFocusMode:!1,isSnapping:!1,isDebugMode:!1,isReadonlyMode:!1,keepStyleMenuOpen:!1,nudgeDistanceLarge:16,nudgeDistanceSmall:1,showRotateHandles:!0,showBindingHandles:!0,showCloneHandles:!1,showGrid:!1,language:"en",dockPosition:"bottom",exportBackground:"transparent"},appState:{status:"idle",activeTool:"select",hoveredId:void 0,currentPageId:"page",currentStyle:e5,isToolLocked:!1,isMenuOpen:!1,isEmptyCanvas:!1,eraseLine:[],snapLines:[],isLoading:!1,disableAssets:!1},document:i7.defaultDocument}),B(se,"assetSrc","tldraw-assets.json");var st=a.ErrorBoundary,si="undefined"!=typeof window&&!!window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches;function ss({id:e,document:t,currentPageId:i,autofocus:s=!0,showMenu:a=!0,showMultiplayerMenu:o=!0,showPages:r=!0,showTools:l=!0,showZoom:d=!0,showStyles:h=!0,showUI:p=!0,readOnly:c=!1,disableAssets:u=!1,darkMode:g=si,onMount:m,onChange:f,onChangePresence:v,onNewProject:b,onSaveProject:y,onSaveProjectAs:S,onOpenProject:I,onOpenMedia:k,onUndo:w,onRedo:x,onPersist:P,onPatch:C,onCommand:A,onChangePage:E,onAssetCreate:B,onAssetDelete:z,onAssetUpload:M,onSessionStart:D,onSessionEnd:j,onExport:T}){let[O,F]=n.useState(e),[L,R]=n.useState(()=>new se(e,{onMount:m,onChange:f,onChangePresence:v,onNewProject:b,onSaveProject:y,onSaveProjectAs:S,onOpenProject:I,onOpenMedia:k,onUndo:w,onRedo:x,onPersist:P,onPatch:C,onCommand:A,onChangePage:E,onAssetDelete:z,onAssetCreate:B,onAssetUpload:M,onSessionStart:D,onSessionEnd:j})),[q,H]=n.useState(null),[U,K]=n.useState(null),[_,N]=n.useState(null),[W,Y]=n.useState(null),$=n.useCallback((e,t,i,s)=>{Y(()=>e),H(()=>s),K(()=>t),N(()=>i)},[]);return n.useLayoutEffect(()=>{if(e===O)return;let t=new se(e,{onMount:m,onChange:f,onChangePresence:v,onNewProject:b,onSaveProject:y,onSaveProjectAs:S,onOpenProject:I,onOpenMedia:k,onUndo:w,onRedo:x,onPersist:P,onPatch:C,onCommand:A,onChangePage:E,onAssetDelete:z,onAssetCreate:B,onAssetUpload:M,onExport:T,onSessionStart:D,onSessionEnd:j});F(e),R(t)},[O,e]),n.useEffect(()=>{t&&(t.id===L.document.id?L.updateDocument(t):L.loadDocument(t))},[t,L]),n.useEffect(()=>{L.setDisableAssets(u)},[L,u]),n.useEffect(()=>{i&&L.changePage(i)},[i,L]),n.useEffect(()=>{L.readOnly=c,c||(L.selectNone(),L.cancelSession(),L.setEditingId())},[L,c]),n.useEffect(()=>{g!==L.settings.isDarkMode&&L.toggleDarkMode()},[L,g]),n.useEffect(()=>{L.callbacks={onMount:m,onChange:f,onChangePresence:v,onNewProject:b,onSaveProject:y,onSaveProjectAs:S,onOpenProject:I,onOpenMedia:k,onUndo:w,onRedo:x,onPersist:P,onPatch:C,onCommand:A,onChangePage:E,onAssetDelete:z,onAssetCreate:B,onAssetUpload:M,onExport:T,onSessionStart:D,onSessionEnd:j}},[m,f,v,b,y,S,I,k,w,x,P,C,A,E,z,B,M,T,D,j]),n.useLayoutEffect(()=>{var e;if("undefined"!=typeof window&&(null==(e=window.document)?void 0:e.fonts))return window.document.fonts.addEventListener("loadingdone",t),()=>{window.document.fonts.removeEventListener("loadingdone",t)};function t(){L.refreshBoundingBoxes()}},[L]),n.createElement(ee.Provider,{value:L},n.createElement(ea.Provider,{value:{onYes:U,onCancel:q,onNo:_,dialogState:W,setDialogState:Y,openDialog:$}},n.createElement(sn,{key:O||"Tldraw",id:O,autofocus:s,showPages:r,showMenu:a,showMultiplayerMenu:o,showStyles:h,showZoom:d,showTools:l,showUI:p,readOnly:c})))}var sn=n.memo(function({id:e,autofocus:t,showPages:i,showMenu:a,showMultiplayerMenu:r,showZoom:l,showStyles:d,showTools:h,readOnly:p,showUI:c}){var u,g,m;let f=et(),[v,b]=n.useState(null),y=n.useRef(null),S=f.useStore(),{document:I,settings:k,appState:w,room:x}=S,P="select"===S.appState.activeTool,A=I.pages[w.currentPageId],E=I.pageStates[A.id],B=I.assets,{selectedIds:z}=E,M=1===z.length&&A.shapes[z[0]]&&ti.getShapeUtil(A.shapes[z[0]].type).hideBounds,D=1===z.length&&A.shapes[z[0]]&&ti.getShapeUtil(A.shapes[z[0]].type).hideResizeHandles,j=n.useMemo(()=>({isDarkMode:k.isDarkMode}),[k.isDarkMode]),T=k.isCadSelectMode?!w.selectByContain:w.selectByContain,O=n.useMemo(()=>{let{selectByContain:e}=w,{isDarkMode:t,isCadSelectMode:i}=k;if(t){let t=i?e?"69, 155, 255":"105, 209, 73":"180, 180, 180";return{brushFill:`rgba(${t}, ${i?.08:.05})`,brushStroke:`rgba(${t}, ${i?.5:.25})`,brushDashStroke:`rgba(${t}, .6)`,selected:"rgba(38, 150, 255, 1.000)",selectFill:"rgba(38, 150, 255, 0.05)",background:"#212529",foreground:"#49555f"}}let s=i?e?"0, 89, 242":"51, 163, 23":"0,0,0";return{brushFill:`rgba(${s}, ${i?.08:.05})`,brushStroke:`rgba(${s}, ${i?.4:.25})`,brushDashStroke:`rgba(${s}, .6)`}},[k.isDarkMode,k.isCadSelectMode,w.selectByContain]),F=void 0!==f.session,L=F&&(null==(u=f.session)?void 0:u.constructor.name)!=="BrushSession"||!P||M||!!E.editingId,R=F||!P,q=F&&"brushing"!==S.appState.status||!P,H=F||!P||E.camera.zoom<.2,U=(m=k.language,n.useMemo(()=>{var e,t;let i;return e=null!=m?m:navigator.language.split(/[-_]/)[0],i=en.find(t=>t.locale===e),{locale:e,label:null!=(t=null==i?void 0:i.label)?t:e,messages:C(C({},es),null==i?void 0:i.messages)}},[m]));return n.useLayoutEffect(()=>{let e=y.current;e&&(k.isDarkMode?e.classList.add(ed):e.classList.remove(ed))},[k.isDarkMode]),n.useEffect(()=>{let e=!1,t=!1,i=y.current;if(!i)return;let s=s=>{" "!==s.key||t||(t=!0,e?i.setAttribute("style","cursor: grabbing !important"):i.setAttribute("style","cursor: grab !important"))},n=e=>{" "===e.key&&(t=!1,i.setAttribute("style","cursor: initial"))},a=s=>{e=!0,1===s.button&&i.setAttribute("style","cursor: grabbing !important"),0===s.button&&t&&i.setAttribute("style","cursor: grabbing !important")},o=()=>{e=!1,t?i.setAttribute("style","cursor: grab !important"):i.setAttribute("style","cursor: initial")};return i.addEventListener("keydown",s),i.addEventListener("keyup",n),i.addEventListener("pointerdown",a),i.addEventListener("pointerup",o),()=>{i.removeEventListener("keydown",s),i.removeEventListener("keyup",n),i.removeEventListener("pointerdown",a),i.removeEventListener("pointerup",o)}},[y.current]),n.createElement(ei.Provider,{value:y},n.createElement(o.A,{locale:U.locale,messages:U.messages},n.createElement(so,{ref:y,tabIndex:-0},n.createElement(ep,null),n.createElement(sa,{focusableRef:y,autofocus:t}),n.createElement(st,{FallbackComponent:n.createElement("span",null)},n.createElement(s.A4,{id:e,containerRef:y,shapeUtils:im,page:A,pageState:E,assets:B,snapLines:w.snapLines,eraseLine:w.eraseLine,grid:8,users:null==x?void 0:x.users,userId:null==x?void 0:x.userId,theme:O,meta:j,hideBounds:L,hideHandles:R,hideResizeHandles:D,hideIndicators:q,hideBindingHandles:!k.showBindingHandles,hideCloneHandles:H,hideRotateHandles:!k.showRotateHandles,hideGrid:!k.showGrid,showDashedBrush:T,performanceMode:null==(g=f.session)?void 0:g.performanceMode,onPinchStart:f.onPinchStart,onPinchEnd:f.onPinchEnd,onPinch:f.onPinch,onPan:f.onPan,onZoom:f.onZoom,onPointerDown:f.onPointerDown,onPointerMove:f.onPointerMove,onPointerUp:f.onPointerUp,onPointCanvas:f.onPointCanvas,onDoubleClickCanvas:f.onDoubleClickCanvas,onRightPointCanvas:f.onRightPointCanvas,onDragCanvas:f.onDragCanvas,onReleaseCanvas:f.onReleaseCanvas,onPointShape:f.onPointShape,onDoubleClickShape:f.onDoubleClickShape,onRightPointShape:f.onRightPointShape,onDragShape:f.onDragShape,onHoverShape:f.onHoverShape,onUnhoverShape:f.onUnhoverShape,onReleaseShape:f.onReleaseShape,onPointBounds:f.onPointBounds,onDoubleClickBounds:f.onDoubleClickBounds,onRightPointBounds:f.onRightPointBounds,onDragBounds:f.onDragBounds,onHoverBounds:f.onHoverBounds,onUnhoverBounds:f.onUnhoverBounds,onReleaseBounds:f.onReleaseBounds,onPointBoundsHandle:f.onPointBoundsHandle,onDoubleClickBoundsHandle:f.onDoubleClickBoundsHandle,onRightPointBoundsHandle:f.onRightPointBoundsHandle,onDragBoundsHandle:f.onDragBoundsHandle,onHoverBoundsHandle:f.onHoverBoundsHandle,onUnhoverBoundsHandle:f.onUnhoverBoundsHandle,onReleaseBoundsHandle:f.onReleaseBoundsHandle,onPointHandle:f.onPointHandle,onDoubleClickHandle:f.onDoubleClickHandle,onRightPointHandle:f.onRightPointHandle,onDragHandle:f.onDragHandle,onHoverHandle:f.onHoverHandle,onUnhoverHandle:f.onUnhoverHandle,onReleaseHandle:f.onReleaseHandle,onError:f.onError,onRenderCountChange:f.onRenderCountChange,onShapeChange:f.onShapeChange,onShapeBlur:f.onShapeBlur,onShapeClone:f.onShapeClone,onBoundsChange:f.updateBounds,onKeyDown:f.onKeyDown,onKeyUp:f.onKeyUp,onDragOver:f.onDragOver,onDrop:f.onDrop})))))}),sa=n.memo(function({focusableRef:e,autofocus:t}){return function(e){let t=et(),i=n.useCallback((i=!1)=>{let s=e.current;return!!i&&(!!t.isMenuOpen||!!t.settings.keepStyleMenuOpen)||(null==s||s.focus(),s&&(document.activeElement===s||s.contains(document.activeElement)))},[e]);n.useEffect(()=>{if(!t)return;let e=e=>{if(i(!0)){if(t.readOnly)return void t.copy(void 0,e);t.cut(void 0,e)}},s=e=>{i(!0)&&t.copy(void 0,e)},n=e=>{!i(!0)||t.readOnly||t.paste(void 0,e)};return document.addEventListener("cut",e),document.addEventListener("copy",s),document.addEventListener("paste",n),()=>{document.removeEventListener("cut",e),document.removeEventListener("copy",s),document.removeEventListener("paste",n)}},[t]),(0,l.vC)("v,1",()=>{i(!0)&&t.selectTool("select")},[t,e.current]),(0,l.vC)("d,p,2",()=>{i(!0)&&t.selectTool("draw")},void 0,[t]),(0,l.vC)("e,3",()=>{i(!0)&&t.selectTool("erase")},void 0,[t]),(0,l.vC)("r,4",()=>{i(!0)&&t.selectTool("rectangle")},void 0,[t]),(0,l.vC)("o,5",()=>{i(!0)&&t.selectTool("ellipse")},void 0,[t]),(0,l.vC)("g,6",()=>{i()&&t.selectTool("triangle")},void 0,[t]),(0,l.vC)("l,7",()=>{i(!0)&&t.selectTool("line")},void 0,[t]),(0,l.vC)("a,8",()=>{i(!0)&&t.selectTool("arrow")},void 0,[t]),(0,l.vC)("t,9",()=>{i(!0)&&t.selectTool("text")},void 0,[t]),(0,l.vC)("s,0",()=>{i(!0)&&t.selectTool("sticky")},void 0,[t]),(0,l.vC)("ctrl+shift+d,⌘+shift+d",e=>{i(!0)&&(t.toggleDarkMode(),e.preventDefault())},void 0,[t]),(0,l.vC)("ctrl+.,⌘+.",()=>{i(!0)&&t.toggleFocusMode()},void 0,[t]),(0,l.vC)("ctrl+shift+g,⌘+shift+g",()=>{i(!0)&&t.toggleGrid()},void 0,[t]);let{onNewProject:s,onOpenProject:a,onSaveProject:o,onSaveProjectAs:r,onOpenMedia:d}=function(){let e=et(),{openDialog:t}=eo();return{onNewProject:n.useCallback(i=>z(this,null,function*(){var s,n;i&&e.callbacks.onOpenProject&&i.preventDefault(),null==(n=(s=e.callbacks).onNewProject)||n.call(s,e,t)}),[e,t]),onSaveProject:n.useCallback(t=>{var i,s;t&&e.callbacks.onOpenProject&&t.preventDefault(),null==(s=(i=e.callbacks).onSaveProject)||s.call(i,e)},[e]),onSaveProjectAs:n.useCallback(t=>{var i,s;t&&e.callbacks.onOpenProject&&t.preventDefault(),null==(s=(i=e.callbacks).onSaveProjectAs)||s.call(i,e)},[e]),onOpenProject:n.useCallback(i=>z(this,null,function*(){var s,n;i&&e.callbacks.onOpenProject&&i.preventDefault(),null==(n=(s=e.callbacks).onOpenProject)||n.call(s,e,t)}),[e,t]),onOpenMedia:n.useCallback(t=>z(this,null,function*(){var i,s;t&&e.callbacks.onOpenMedia&&t.preventDefault(),null==(s=(i=e.callbacks).onOpenMedia)||s.call(i,e)}),[e])}}();(0,l.vC)("ctrl+n,⌘+n",e=>{e.preventDefault(),i()&&s(e)},void 0,[t]),(0,l.vC)("ctrl+s,⌘+s",e=>{i()&&o(e)},void 0,[t]),(0,l.vC)("ctrl+shift+s,⌘+shift+s",e=>{i()&&r(e)},void 0,[t]),(0,l.vC)("ctrl+o,⌘+o",e=>{i()&&a(e)},void 0,[t]),(0,l.vC)("ctrl+u,⌘+u",e=>{i()&&d(e)},void 0,[t]),(0,l.vC)("⌘+z,ctrl+z",e=>{e.preventDefault(),i(!0)&&(t.session?t.cancelSession():t.undo())},void 0,[t]),(0,l.vC)("ctrl+shift+z,⌘+shift+z",()=>{i(!0)&&(t.session?t.cancelSession():t.redo())},void 0,[t]),(0,l.vC)("⌘+u,ctrl+u",()=>{i()&&t.undoSelect()},void 0,[t]),(0,l.vC)("ctrl+shift-u,⌘+shift+u",()=>{i()&&t.redoSelect()},void 0,[t]),(0,l.vC)("ctrl+=,⌘+=,ctrl+num_add,⌘+num_add",e=>{i(!0)&&(t.zoomIn(),e.preventDefault())},void 0,[t]),(0,l.vC)("ctrl+-,⌘+-,ctrl+num_subtract,⌘+num_subtract",e=>{i(!0)&&(t.zoomOut(),e.preventDefault())},void 0,[t]),(0,l.vC)("shift+0,ctrl+numpad_0,⌘+numpad_0",()=>{i(!0)&&t.resetZoom()},void 0,[t]),(0,l.vC)("shift+1",()=>{i(!0)&&t.zoomToFit()},void 0,[t]),(0,l.vC)("shift+2",()=>{i(!0)&&t.zoomToSelection()},void 0,[t]),(0,l.vC)("ctrl+d,⌘+d",e=>{i()&&(t.duplicate(),e.preventDefault())},void 0,[t]),(0,l.vC)("shift+h",()=>{i(!0)&&t.flipHorizontal()},void 0,[t]),(0,l.vC)("shift+v",()=>{i(!0)&&t.flipVertical()},void 0,[t]),(0,l.vC)("escape",()=>{i(!0)&&t.cancel()},void 0,[t]),(0,l.vC)("backspace,del",()=>{i()&&t.delete()},void 0,[t]),(0,l.vC)("⌘+a,ctrl+a",()=>{i(!0)&&t.selectAll()},void 0,[t]),(0,l.vC)("up",()=>{i()&&t.nudge([0,-1],!1)},void 0,[t]),(0,l.vC)("right",()=>{i()&&t.nudge([1,0],!1)},void 0,[t]),(0,l.vC)("down",()=>{i()&&t.nudge([0,1],!1)},void 0,[t]),(0,l.vC)("left",()=>{i()&&t.nudge([-1,0],!1)},void 0,[t]),(0,l.vC)("shift+up",()=>{i()&&t.nudge([0,-1],!0)},void 0,[t]),(0,l.vC)("shift+right",()=>{i()&&t.nudge([1,0],!0)},void 0,[t]),(0,l.vC)("shift+down",()=>{i()&&t.nudge([0,1],!0)},void 0,[t]),(0,l.vC)("shift+left",()=>{i()&&t.nudge([-1,0],!0)},void 0,[t]),(0,l.vC)("⌘+shift+l,ctrl+shift+l",()=>{i()&&t.toggleLocked()},void 0,[t]),(0,l.vC)("⌘+shift+c,ctrl+shift+c",e=>{i()&&(t.copySvg(),e.preventDefault())},void 0,[t]),(0,l.vC)("⌘+g,ctrl+g",e=>{i()&&(t.group(),e.preventDefault())},void 0,[t]),(0,l.vC)("⌘+shift+g,ctrl+shift+g",e=>{i()&&(t.ungroup(),e.preventDefault())},void 0,[t]),(0,l.vC)("[",()=>{i(!0)&&t.moveBackward()},void 0,[t]),(0,l.vC)("]",()=>{i(!0)&&t.moveForward()},void 0,[t]),(0,l.vC)("shift+[",()=>{i(!0)&&t.moveToBack()},void 0,[t]),(0,l.vC)("shift+]",()=>{i(!0)&&t.moveToFront()},void 0,[t]),(0,l.vC)("ctrl+shift+backspace,⌘+shift+backspace",e=>{i()&&(t.settings.isDebugMode&&t.resetDocument(),e.preventDefault())},void 0,[t]),(0,l.vC)("alt+command+l,alt+ctrl+l",e=>{i(!0)&&(t.style({textAlign:"start"}),e.preventDefault())},void 0,[t]),(0,l.vC)("alt+command+t,alt+ctrl+t",e=>{i(!0)&&(t.style({textAlign:"middle"}),e.preventDefault())},void 0,[t]),(0,l.vC)("alt+command+r,alt+ctrl+r",e=>{i(!0)&&(t.style({textAlign:"end"}),e.preventDefault())},void 0,[t])}(e),n.useLayoutEffect(()=>{if(Z.get(J))return;let e=document.createElement("style");return e.innerHTML=Q,e.setAttribute("id",J),document.head.appendChild(e),Z.set(J,e),()=>{e&&document.head.contains(e)&&(document.head.removeChild(e),Z.delete(J))}},[]),n.useEffect(()=>{var i;t&&(null==(i=e.current)||i.focus())},[t]),null}),so=er("div",{position:"absolute",height:"100%",width:"100%",minHeight:0,minWidth:0,maxHeight:"100%",maxWidth:"100%",overflow:"hidden",boxSizing:"border-box",outline:"none","& .tl-container":{position:"absolute",top:0,left:0,height:"100%",width:"100%",zIndex:1},"& input, textarea, button, select, label, button":{webkitTouchCallout:"none",webkitUserSelect:"none","-webkit-tap-highlight-color":"transparent","tap-highlight-color":"transparent"}});er("div",{position:"absolute",top:0,left:0,height:"100%",width:"100%",padding:"8px 8px 0 8px",display:"flex",alignItems:"flex-start",justifyContent:"flex-start",pointerEvents:"none",zIndex:2,"& > *":{pointerEvents:"all"}}),er("div",{flexGrow:2})}}]);
//# sourceMappingURL=1988a2b6-89598c43bba6f1a4.js.map