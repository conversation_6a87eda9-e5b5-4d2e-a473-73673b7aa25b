.uppy-DragDrop-container{display:flex;align-items:center;justify-content:center;max-width:100%;font-family:Inter,sans-serif;font-size:var(--chakra-fontSizes-md);background-color:var(--chakra-colors-white);border-radius:var(--chakra-radii-md);cursor:pointer}.uppy-DragDrop-container::-moz-focus-inner{border:0}.uppy-DragDrop-container:focus{outline:none;box-shadow:var(--chakra-shadows-outline)}.uppy-DragDrop-inner{margin:0;padding:80px 20px;line-height:1.4;text-align:center}.uppy-DragDrop-arrow{visibility:hidden}.uppy-DragDrop--isDragDropSupported{background:var(--chakra-colors-gray-50);box-shadow:var(--chakra-shadows-inner);border:1px dashed var(--chakra-colors-gray-400)}.uppy-DragDrop--isDraggingOver{background:var(--chakra-colors-trueblue-100);border:2px dashed var(--chakra-colors-trueblue-500)}.uppy-DragDrop--isDraggingOver .uppy-DragDrop-arrow{fill:var(--chakra-colors-gray-500)}.uppy-DragDrop-label{display:block;margin-bottom:5px;font-size:var(--chakra-fontSizes-md)}.uppy-DragDrop-browse{color:var(--chakra-colors-trueblue-400);cursor:pointer}.uppy-DragDrop-note{color:var(--chakra-colors-gray-500);font-size:var(--chakra-fontSizes-sm)}.math-node{min-width:1em;min-height:1em;font-size:.95em;font-family:Consolas,Ubuntu Mono,monospace;cursor:auto}.math-node.empty-math .math-render:before{content:"(empty)";color:red}.math-node .math-render.parse-error:before{content:"(math error)";color:red;cursor:help}.math-node.ProseMirror-selectednode{outline:none}.math-node .math-src{display:none;color:rgb(132,33,162);-moz-tab-size:4;tab-size:4}.math-node.ProseMirror-selectednode .math-src{display:inline}.math-node.ProseMirror-selectednode .math-render{display:none}math-inline{display:inline;white-space:nowrap}math-inline .math-render{display:inline-block;font-size:.85em;cursor:pointer}math-inline .math-src .ProseMirror{display:inline;border-right:1px solid transparent;border-left:1px solid transparent}math-inline .math-src:after,math-inline .math-src:before{content:"$";color:#b0b0b0}math-display,math-display .math-render{display:block}math-display.ProseMirror-selectednode{background-color:#eee}math-display .math-src .ProseMirror{width:100%;display:block}math-display .math-src:after,math-display .math-src:before{content:"$$";text-align:left;color:#b0b0b0}math-display .katex-display{margin:0}
/*# sourceMappingURL=5208669d04c64ca3.css.map*/