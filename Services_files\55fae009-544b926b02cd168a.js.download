"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5088],{9014:(t,e,r)=>{r.d(e,{ZW:()=>rt,cM:()=>ek,yX:()=>rM});var n=r(48970),a=r(31269),i=r(509),o=r(88187),s=r(94420),u=r(31617),l=r(72956),c=r(30227),f=r(55729),d=r(62372),p=r(68990),g=r(59153),h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function v(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var m=function(){return(m=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)};function b(t,e,r){if(r||2==arguments.length)for(var n,a=0,i=e.length;a<i;a++)!n&&a in e||(n||(n=Array.prototype.slice.call(e,0,a)),n[a]=e[a]);return t.concat(n||Array.prototype.slice.call(e))}function x(t,e){var r;return m({events:{},props:((r={})[t]=Boolean,r),name:t},e)}var E=(0,n.Ay)().browser.webkit,S=E&&function(){var t="undefined"==typeof window?{userAgent:""}:window.navigator,e=/applewebkit\/([^\s]+)/g.exec(t.userAgent.toLowerCase());return!!e&&605>parseFloat(e[1])}(),R="moveable-",M="\n{\n	position: absolute;\n	width: 1px;\n	height: 1px;\n	left: 0;\n	top: 0;\n    z-index: 3000;\n    --moveable-color: #4af;\n    --zoom: 1;\n    --zoompx: 1px;\n    will-change: transform;\n}\n.control-box {\n    z-index: 0;\n}\n.line, .control {\n    position: absolute;\n	left: 0;\n    top: 0;\n    will-change: transform;\n}\n.control {\n	width: 14px;\n	height: 14px;\n	border-radius: 50%;\n	border: 2px solid #fff;\n	box-sizing: border-box;\n    background: #4af;\n    background: var(--moveable-color);\n	margin-top: -7px;\n    margin-left: -7px;\n    border: 2px solid #fff;\n    z-index: 10;\n}\n.padding {\n    position: absolute;\n    top: 0px;\n    left: 0px;\n    width: 100px;\n    height: 100px;\n    transform-origin: 0 0;\n}\n.line {\n	width: 1px;\n    height: 1px;\n    background: #4af;\n    background: var(--moveable-color);\n	transform-origin: 0px 50%;\n}\n.line.dashed {\n    box-sizing: border-box;\n    background: transparent;\n}\n.line.dashed.horizontal {\n    border-top: 1px dashed #4af;\n    border-top-color: #4af;\n    border-top-color: var(--moveable-color);\n}\n.line.dashed.vertical {\n    border-left: 1px dashed #4af;\n    border-left-color: #4af;\n    border-left-color: var(--moveable-color);\n}\n.line.vertical {\n    transform: translateX(-50%);\n}\n.line.horizontal {\n    transform: translateY(-50%);\n}\n.line.vertical.bold {\n    width: 2px;\n}\n.line.horizontal.bold {\n    height: 2px;\n}\n\n.control.origin {\n	border-color: #f55;\n	background: #fff;\n	width: 12px;\n	height: 12px;\n	margin-top: -6px;\n    margin-left: -6px;\n	pointer-events: none;\n}\n".concat([0,15,30,45,60,75,90,105,120,135,150,165].map(function(t){var e,r,n;return'\n.direction[data-rotation="'.concat(t,'"] {\n	').concat((e='data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="'.concat(32,'px" height="').concat(32,'px" viewBox="0 0 32 32" ><path d="M 16,5 L 12,10 L 14.5,10 L 14.5,22 L 12,22 L 16,27 L 20,22 L 17.5,22 L 17.5,10 L 20, 10 L 16,5 Z" stroke-linejoin="round" stroke-width="1.2" fill="black" stroke="white" style="transform:rotate(').concat(t,'deg);transform-origin: 16px 16px"></path></svg>'),n="ns-resize",135==(r=45*Math.round(t/45)%180)?n="nwse-resize":45===r?n="nesw-resize":90===r&&(n="ew-resize"),"cursor:".concat(n,";cursor: url('").concat(e,"') 16 16, ").concat(n,";")),"\n}\n")}).join("\n"),"\n.group {\n    z-index: -1;\n}\n.area {\n    position: absolute;\n}\n.area-pieces {\n    position: absolute;\n    top: 0;\n    left: 0;\n    display: none;\n}\n.area.avoid, .area.pass {\n    pointer-events: none;\n}\n.area.avoid+.area-pieces {\n    display: block;\n}\n.area-piece {\n    position: absolute;\n}\n\n").concat(S?':global svg *:before {\n	content:"";\n	transform-origin: inherit;\n}':"","\n"),D=[[0,1,2],[1,0,3],[2,0,3],[3,1,2]],y=["n","w","s","e","nw","ne","sw","se"],C={n:[0,1],s:[2,3],w:[2,0],e:[1,3],nw:[0],ne:[1],sw:[2],se:[3]},B={n:0,s:180,w:270,e:90,nw:315,ne:45,sw:225,se:135},G=["isMoveableElement","updateRect","updateTarget","destroy","dragStart","isInside","hitTest","setState","getRect","request","isDragging","getManager","forceUpdate"];function z(t,e,r,n,a){var i=e.gesto.move(r,t.inputEvent),o=i.originalDatas||i.datas,s=o.draggable||(o.draggable={});return m(m({},a?eD(e,i):i),{isDrag:!0,isPinch:!!n,parentEvent:!0,datas:s,originalDatas:t.originalDatas})}var w=function(){function t(){this.prevX=0,this.prevY=0,this.startX=0,this.startY=0,this.isDrag=!1,this.isFlag=!1,this.datas={draggable:{}}}var e=t.prototype;return e.dragStart=function(t,e){this.isDrag=!1,this.isFlag=!1;var r=e.originalDatas;return this.datas=r,r.draggable||(r.draggable={}),m(m({},this.move(t,e.inputEvent)),{type:"dragstart"})},e.drag=function(t,e){return this.move([t[0]-this.prevX,t[1]-this.prevY],e)},e.move=function(t,e){var r,n;return this.isFlag?(r=this.prevX+t[0],n=this.prevY+t[1],this.isDrag=!0):(this.prevX=t[0],this.prevY=t[1],this.startX=t[0],this.startY=t[1],r=t[0],n=t[1],this.isFlag=!0),this.prevX=r,this.prevY=n,{type:"drag",clientX:r,clientY:n,inputEvent:e,isDrag:this.isDrag,distX:r-this.startX,distY:n-this.startY,deltaX:t[0],deltaY:t[1],datas:this.datas.draggable,originalDatas:this.datas,parentEvent:!0,parentGesto:this}},t}();function O(t,e,r){var n=r.originalDatas;n.groupable=n.groupable||{};var a=n.groupable;a.childDatas=a.childDatas||[];var i=a.childDatas;return t.moveables.map(function(t,n){return i[n]=i[n]||{},i[n][e]=i[n][e]||{},m(m({},r),{datas:i[n][e],originalDatas:i[n]})})}function P(t,e,r,n,a,i){var o=!!r.match(/Start$/g),s=!!r.match(/End$/g),u=a.isPinch,l=a.datas,c=O(t,e.name,a),f=t.moveables,d=c.map(function(t,a){var c=f[a],d=t;o?d=new w().dragStart(n,t):(c.state.gesto||(c.state.gesto=l.childGestos[a]),d=z(t,c.state,n,u,i));var p=e[r](c,m(m({},d),{parentFlag:!0}));return s&&(c.state.gesto=null),p});return o&&(l.childGestos=f.map(function(t){return t.state.gesto})),d}function T(t,e,r,n,a,i){void 0===a&&(a=function(t,e){return e});var o=!!r.match(/End$/g),s=O(t,e.name,n),u=t.moveables;return s.map(function(t,n){var s=u[n],l=t;l=a(s,t);var c=e[r](s,m(m({},l),{parentFlag:!0}));return c&&i&&i(s,t,c,n),o&&(s.state.gesto=null),c})}function k(t,e,r,n){void 0===n&&(n="");var a=t.state,o=a.renderPoses,s=a.rotation,u=a.direction,l=t.props,c=l.renderDirections,f=void 0===c?e:c,d=l.zoom,p={};if(!f)return[];var g=u>0?1:-1,h=!0===f?y:f,v=s/Math.PI*180;return h.forEach(function(t){p[t]=!0}),h.map(function(t){var e=C[t];if(!e||!p[t])return null;var a=((0,i.nF)(v,15)+g*B[t]+720)%180;return r.createElement("div",{className:tQ("control","direction",t,n),"data-rotation":a,"data-direction":t,key:"direction-".concat(t),style:ei.apply(void 0,b([s,d],e.map(function(t){return o[t]}),!1))})})}function I(t,e,r,n,a,o){for(var s=[],u=6;u<arguments.length;u++)s[u-6]=arguments[u];var l=(0,i.Mu)(r,n),c=e?(0,i.nF)(l/Math.PI*180,15)%180:-1;return t.createElement("div",{key:"line".concat(o),className:tQ.apply(void 0,b(["line","direction",e],s,!1)),"data-rotation":c,"data-line-index":o,"data-direction":e,style:ea(r,n,a,l)})}function A(t,e){return k(t,["nw","ne","sw","se"],e)}function F(t,e,r,n,a,s){var u=t.state.is3d?4:3,l=t7(t.state.rootMatrix,a,u),c=(0,o.tY)([s.left,s.top],l);e.startAbsoluteOrigin=c,e.prevDeg=(0,i.Mu)(c,[r,n])/Math.PI*180,e.defaultDeg=e.prevDeg,e.prevSnapDeg=0,e.loop=0}function Y(t,e,r){var n=r.defaultDeg,a=r.prevDeg,i=a%360,o=Math.floor(a/360);i<0&&(i+=360),i>t&&i>270&&t<90?++o:i<t&&i<90&&t>270&&--o;var s=e*(360*o+t-n);return r.prevDeg=n+s,s}function N(t,e,r,n){return Y((0,i.Mu)(n.startAbsoluteOrigin,[t,e])/Math.PI*180,r,n)}function q(t,e,r,n,a,s){var u=t.props.throttleRotate,l=n,c=r.prevSnapDeg;s&&(l=function(t,e,r,n){if(!L(t,"rotatable"))return n;var a=e.pos1,s=e.pos2,u=e.pos3,l=e.pos4,c=n*Math.PI/180,f=[a,s,u,l].map(function(t){return(0,o.Rd)(t,r)}),d=f.map(function(t){return(0,o.e$)(t,c)}),p=b(b([],function(t,e,r,n,a){if(!t.props.bounds)return[];var o=a*Math.PI/180,s=tp(t),u=s.left,l=s.top,c=s.right,f=s.bottom,d=u-n[0],p=c-n[0],g=l-n[1],h=f-n[1],v={left:d,top:g,right:p,bottom:h};if(!th(r,v,0))return[];var m=[];return[[d,0],[p,0],[g,1],[h,1]].forEach(function(t){var n=t[0],a=t[1];r.forEach(function(t){var r,s,u=(0,i.Mu)([0,0],t);m.push.apply(m,[s=Math.sqrt((r=er(t))*r-n*n)||0,-s].sort(function(e,r){return Math.abs(e-t[+!a])-Math.abs(r-t[+!a])}).map(function(t){return(0,i.Mu)([0,0],a?[t,n]:[n,t])}).map(function(t){return o+t-u}).filter(function(t){return!th(e,v,t)}).map(function(t){return(0,i.nF)(180*t/Math.PI,1e-7)}))})}),m}(t,f,d,r,n),!0),function(t,e,r,n,a){var o=t.props.innerBounds,s=a*Math.PI/180;if(!o)return[];var u=o.left,l=o.top,c=o.width,f=o.height,d=u-n[0],p=u+c-n[0],g=l-n[1],h=l+f-n[1],v=[[d,g],[p,g],[d,h],[p,h]],m=tq(r,[0,0]);if(!tf(r,v,m,0))return[];var b=[],x=v.map(function(t){return[er(t),(0,i.Mu)([0,0],t)]});return[[r[0],r[1]],[r[1],r[3]],[r[3],r[2]],[r[2],r[0]]].forEach(function(t){var r=(0,i.Mu)([0,0],function(t){var e=t[0],r=t[1],n=r[0]-e[0],a=r[1]-e[1];if(!n)return[e[0],0];if(!a)return[0,e[1]];var i=a/n,o=-i*e[0]+e[1];return[-o/(i+1/i),o/(i*i+1)]}(t)),n=function(t){var e=t[0],r=t[1],n=r[0]-e[0],a=r[1]-e[1];if(!n)return Math.abs(e[0]);if(!a)return Math.abs(e[1]);var i=a/n;return Math.abs((-i*e[0]+e[1])/Math.sqrt(Math.pow(i,2)+1))}(t);b.push.apply(b,x.filter(function(t){var e=t[0];return e&&n<=e}).map(function(t){var e=t[0],a=t[1],i=Math.acos(e?n/e:0);return[s+(a+i)-r,s+(a-i)-r]}).reduce(function(t,e){return t.push.apply(t,e),t},[]).filter(function(t){return!tf(e,v,m,t)}).map(function(t){return(0,i.nF)(180*t/Math.PI,1e-7)}))}),b}(t,f,d,r,n),!0);return(p.sort(function(t,e){return Math.abs(t-n)-Math.abs(e-n)}),p.length)?p[0]:n}(t,e,r.origin,l));var f=(0,i.nF)(a+l,void 0===u?0:u),d=f-a;return r.prevSnapDeg=d,[d-c,l,f]}function W(t,e){if(e.isRequest)return"rotatable"===e.requestAble;var r=e.inputEvent.target;if((0,i.nB)(r,tQ("rotation-control")))return!0;var n=t.props.rotationTarget;return!!n&&ew(n,!0).some(function(t){return!!t&&(r===t||r.contains(t))})}var _=["left","right","center"],H=["top","bottom","middle"],j={start:"left",end:"right",center:"center"},X={start:"top",end:"bottom",center:"middle"};function L(t,e){var r=t.props,n=r.snappable,a=r.bounds,i=r.innerBounds,o=r.verticalGuidelines,s=r.horizontalGuidelines,u=r.snapGridWidth,l=r.snapGridHeight,c=t.state,f=c.guidelines,d=c.enableSnap;return!(!n||!d||e&&!0!==n&&0>n.indexOf(e))&&(!!u||!!l||!!a||!!i||!!f&&!!f.length||!!o&&!!o.length||!!s&&!!s.length||!1)}function V(t){return!1===t?{}:!0!==t&&t?t:{left:!0,right:!0,top:!0,bottom:!0}}function U(t,e){var r=function(t,e){var r=V(t),n={};for(var a in r)a in e&&r[a]&&(n[a]=e[a]);return n}(t,e),n=H.filter(function(t){return t in r}),a=_.filter(function(t){return t in r});return{horizontal:n.map(function(t){return r[t]}),vertical:a.map(function(t){return r[t]})}}function K(t,e,r,n,a,o,s){void 0===a&&(a=0),void 0===o&&(o=0),void 0===s&&(s={left:0,top:0,right:0,bottom:0});var u=[],l=s.left,c=s.top,f=s.bottom,d=r+s.right-l,p=n+f-c;return t&&t.forEach(function(t){u.push({type:"horizontal",pos:[l,(0,i.nF)(t-o+c,.1)],size:d})}),e&&e.forEach(function(t){u.push({type:"vertical",pos:[(0,i.nF)(t-a+l,.1),c],size:p})}),u}function $(t){var e=t.state,r=e.snapOffset,n=e.containerClientRect,a=n.overflow,s=n.scrollHeight,c=n.scrollWidth,f=n.clientHeight,d=n.clientWidth,p=n.clientLeft,g=n.clientTop,h=t.props,v=h.snapGap,x=h.verticalGuidelines,E=h.horizontalGuidelines,S=h.snapThreshold,R=h.snapGridWidth,M=h.snapGridHeight,D=b([],function(t){var e=t.state,r=t.props.elementGuidelines,n=void 0===r?[]:r;if(!n.length)return e.elementRects=[],[];var a=(e.elementRects||[]).filter(function(t){return!t.refresh}),s=n.map(function(t){return(0,i.Gv)(t)&&"element"in t?t:{element:ez(t,!0)}}).filter(function(t){return t.element}),c=(0,u.U)(a.map(function(t){return t.element}),s.map(function(t){return t.element})),f=c.maintained,d=c.added,p=[];f.forEach(function(t){var e=t[0];p[t[1]]=a[e]}),(function(t,e){if(!e.length)return[];var r,n=t.state,a=n.containerClientRect,i=n.targetClientRect,s=i.top,u=i.left,c=n.rootMatrix,f=n.is3d?4:3,d=(r=t7(c,[a.clientLeft,a.clientTop],f),[a.left+r[0],a.top+r[1]]),p=d[0],g=d[1],h=ep(n),v=(0,l.Qk)(h),b=v.minX,x=v.minY,E=(0,o.Rd)([b,x],eM(c,[u-p,s-g],f)).map(function(t){var e;return Math.round((e=t)%1==-.5?e-1:e)}),S=E[0],R=E[1];return e.map(function(t){var e=t.element.getBoundingClientRect(),r=e.left-p,n=e.top-g,a=n+e.height,i=r+e.width,o=eM(c,[r,n],f),s=o[0],u=o[1],l=eM(c,[i,a],f),d=l[0],h=l[1];return m(m({},t),{rect:{left:s+S,right:d+S,top:u+R,bottom:h+R,center:(s+d)/2+S,middle:(u+h)/2+R}})})})(t,d.map(function(t){return s[t]})).map(function(t,e){p[d[e]]=t}),e.elementRects=p;var g=V(t.props.elementSnapDirections),h=[];return p.forEach(function(t){var e=t.element,r=t.top,n=void 0===r?g.top:r,a=t.left,o=void 0===a?g.left:a,s=t.right,u=void 0===s?g.right:s,l=t.bottom,c=void 0===l?g.bottom:l,f=t.center,d=void 0===f?g.center:f,p=t.middle,v=void 0===p?g.middle:p,m=t.className,b=t.rect,x=U({top:n,right:u,left:o,bottom:c,center:d,middle:v},b),E=x.horizontal,S=x.vertical,R=b.top,M=b.left,D=b.right-M,y=b.bottom-R,C=[D,y];S.forEach(function(r){h.push({type:"vertical",element:e,pos:[(0,i.nF)(r,.1),R],size:y,sizes:C,className:m,elementRect:t})}),E.forEach(function(r){h.push({type:"horizontal",element:e,pos:[M,(0,i.nF)(r,.1)],size:D,sizes:C,className:m,elementRect:t})})}),h}(t),!0);if(void 0===v||v){var y,C,B,G,z=et(ep(t.state)),w=z.top,O=z.left,P=z.bottom,T=z.right;D.push.apply(D,(y={top:w,left:O,bottom:P,right:T,center:(O+T)/2,middle:(w+P)/2},C=void 0===S?5:S,B=t.state.elementRects,G=[],[["vertical",j,X],["horizontal",X,j]].forEach(function(t){var e=t[0],r=t[1],n=t[2],a=y[r.start],i=y[r.end],o=y[r.center],s=y[n.start],u=y[n.end];function l(t){var e=t.rect;return e[r.end]<a+C?a-e[r.end]:i-C<e[r.start]?e[r.start]-i:-1}var c=B.filter(function(t){var e=t.rect;return!(e[n.start]>u)&&!(e[n.end]<s)&&l(t)>0}).sort(function(t,e){return l(t)-l(e)}),f=[];c.forEach(function(t){c.forEach(function(e){if(t!==e){var r=t.rect,a=e.rect,i=r[n.start],o=r[n.end],s=a[n.start];i>a[n.end]||s>o||f.push([t,e])}})}),f.forEach(function(t){var n=t[0],s=t[1],u=n.rect,l=s.rect,c=u[r.start],f=u[r.end],d=l[r.start],p=l[r.end],g=0,h=0,v=!1,m=!1,b=!1;if(f<=a&&i<=d){if(m=!0,g=(d-f-(i-a))/2,Math.abs((h=f+g+(i-a)/2)-o)>C)return}else if(f<d&&p<a+C){if(v=!0,Math.abs((h=p+(g=d-f))-a)>C)return}else if(!(f<d)||!(i-C<c))return;else if(b=!0,Math.abs((h=c-(g=d-f))-i)>C)return;g&&G.push({type:e,pos:"vertical"===e?[h,0]:[0,h],element:s.element,size:0,className:s.className,isStart:v,isCenter:m,isEnd:b,gap:g,hide:!0,gapRects:[n,s]})})}),G))}return D.push.apply(D,function(t,e,r,n,a,o){void 0===a&&(a=0),void 0===o&&(o=0);var s=[];if(e)for(var u=0;u<=n;u+=e)s.push({type:"horizontal",pos:[0,(0,i.nF)(u-o,.1)],size:r,hide:!0});if(t)for(var u=0;u<=r;u+=t)s.push({type:"vertical",pos:[(0,i.nF)(u-a,.1),0],size:n,hide:!0});return s}(void 0===R?0:R,void 0===M?0:M,a?c:d,a?s:f,p,g)),D.push.apply(D,K(E||!1,x||!1,a?c:d,a?s:f,p,g,r)),D}function Z(t,e,r,n){var a=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=t.length-1,n=0;n<r;++n){var a=t[n];if(!(0,i.b0)(a))return a}return t[r]}(n,t.props.snapThreshold,5);return Q(t.state.guidelines,e,r,a)}function Q(t,e,r,n){return{vertical:te(t,"vertical",e,n),horizontal:te(t,"horizontal",r,n)}}function J(t,e,r){var n=U(t.props.snapDirections,e);return Z(t,n.vertical,n.horizontal,r)}function tt(t){var e=t.isSnap;if(!e)return{isSnap:!1,offset:0,dist:-1,pos:0,guideline:null};var r=t.posInfos[0],n=r.guidelineInfos[0],a=n.offset,i=n.dist,o=n.guideline;return{isSnap:e,offset:a,dist:i,pos:r.pos,guideline:o}}function te(t,e,r,n){if(!t||!t.length)return{isSnap:!1,index:-1,posInfos:[]};var a=+("vertical"!==e),i=r.map(function(r,i){var o=t.map(function(t){var e=r-t.pos[a];return{offset:e,dist:Math.abs(e),guideline:t}}).filter(function(t){var r=t.guideline,a=t.dist;return r.type===e&&!(a>n)}).sort(function(t,e){return t.dist-e.dist});return{pos:r,index:i,guidelineInfos:o}}).filter(function(t){return t.guidelineInfos.length>0}).sort(function(t,e){return t.guidelineInfos[0].dist-e.guidelineInfos[0].dist}),o=i.length>0;return{isSnap:o,index:o?i[0].index:-1,posInfos:i}}function tr(t,e){var r=Math.abs(t.offset),n=Math.abs(e.offset);if(t.isBound&&e.isBound)return n-r;if(t.isBound)return -1;if(e.isBound)return 1;if(t.isSnap&&e.isSnap)return n-r;if(t.isSnap)return -1;else if(e.isSnap)return 1;else if(r<1e-7)return 1;else if(n<1e-7)return -1;return r-n}function tn(t,e){return t.slice().sort(function(t,r){var n=t.sign[e],a=r.sign[e],i=t.offset[e],o=r.offset[e];return n?a?tr({isBound:t.isBound,isSnap:t.isSnap,offset:i},{isBound:r.isBound,isSnap:r.isSnap,offset:o}):-1:1})[0]}function ta(t,e){var r=(0,i.G8)([e[0][0],e[1][0]]),n=(0,i.G8)([e[0][1],e[1][1]]);return{vertical:r<=t[0],horizontal:n<=t[1]}}function ti(t,e){var r,n,a=e[0],i=e[1],o=i[0]-a[0],s=i[1]-a[1];return 1e-7>Math.abs(o)&&(o=0),1e-7>Math.abs(s)&&(s=0),o?(r=s?s/o*(t[0]-a[0])+a[1]:a[1],n=t[1]):(r=a[0],n=t[0]),r-n}function to(t,e,r){void 0===r&&(r=1e-7);var n=0>=ti(t[0],e);return t.slice(1).every(function(t){var a=ti(t,e);return a<=0===n||Math.abs(a)<=r})}function ts(t,e,r,n,a){return(void 0===a&&(a=0),n&&e-a<=t||!n&&t<=r+a)?{isBound:!0,offset:n?e-t:r-t}:{isBound:!1,offset:0}}function tu(t,e,r,n,a){var i=t[0],o=t[1],s=e[0],u=e[1],l=eB(o[1]-i[1]),c=eB(o[0]-i[0]),f=eB(u[1]-s[1]);if(eB(u[0]-s[0])){if(!f)if(a&&!c);else{if(l)return ts((s[1]-i[1])/(l/c)+i[0],s[0],u[0],r,n);var d=s[1]-i[1],p=Math.abs(d)<=(n||0);return{isBound:p,offset:p?d:0}}}else if(a&&!l);else{if(c)return ts(l/c*(s[0]-i[0])+i[1],s[1],u[1],r,n);var d=s[0]-i[0],p=Math.abs(d)<=(n||0);return{isBound:p,offset:p?d:0}}return{isBound:!1,offset:0}}function tl(t,e,r,n){return e.map(function(e){var a=e[0],i=function(t,e,r){var n=t.props.innerBounds;if(!n)return{isAllBound:!1,isBound:!1,isVerticalBound:!1,isHorizontalBound:!1,offset:[0,0]};var a=n.left,i=n.top,o=n.width,s=n.height,u=[[a,i],[a,i+s]],l=[[a,i],[a+o,i]],c=[[a+o,i],[a+o,i+s]],f=[[a,i+s],[a+o,i+s]],d=ta(r,e),p=d.horizontal,g=d.vertical;if(to([r,[a,i],[a+o,i],[a,i+s],[a+o,i+s]],e))return{isAllBound:!1,isBound:!1,isVerticalBound:!1,isHorizontalBound:!1,offset:[0,0]};var h=tu(e,l,g),v=tu(e,f,g),m=tu(e,u,p),b=tu(e,c,p),x=h.isBound&&v.isBound,E=h.isBound||v.isBound,S=m.isBound&&b.isBound,R=m.isBound||b.isBound,M=eR(h.offset,v.offset),D=eR(m.offset,b.offset),y=[0,0],C=!1,B=!1;return Math.abs(D)<Math.abs(M)?(y=[M,0],C=E,B=x):(y=[0,D],C=R,B=S),{isAllBound:B,isVerticalBound:E,isHorizontalBound:R,isBound:C,offset:y}}(t,[e[1],e[2]],r),o=i.isBound,s=i.offset,u=i.isVerticalBound,l=i.isHorizontalBound,c=tF({datas:n,distX:s[0],distY:s[1]}).map(function(t,e){return t*(a[e]?2/a[e]:0)});return{sign:a,isBound:o,isVerticalBound:u,isHorizontalBound:l,isSnap:!1,offset:c}})}function tc(t,e,r){var n,a,i;return(n=[],a=e[0],i=e[1],a&&i?n.push([[0,2*i],e,[-a,i]],[[2*a,0],e,[a,-i]]):a?(n.push([[2*a,0],[a,1],[a,-1]]),r&&n.push([[0,-1],[a,-1],[-a,-1]],[[0,1],[a,1],[-a,1]])):i?(n.push([[0,2*i],[1,i],[-1,i]]),r&&n.push([[-1,0],[-1,i],[-1,-i]],[[1,0],[1,i],[1,-i]])):n.push([[-1,0],[-1,-1],[-1,1]],[[1,0],[1,-1],[1,1]],[[0,-1],[-1,-1],[1,-1]],[[0,1],[-1,1],[1,1]]),n).map(function(e){var r=e[0],n=e[1],a=e[2];return[r,tq(t,n),tq(t,a)]})}function tf(t,e,r,n){var a=n?t.map(function(t){return(0,o.e$)(t,n)}):t,i=b([r],e,!0);return[[a[0],a[1]],[a[1],a[3]],[a[3],a[2]],[a[2],a[0]]].some(function(t){return!to(i,t)})}function td(t,e,r){var n=t||{},a=n.position,i=n.left,o=n.top,s=n.right,u=n.bottom,l={position:void 0===a?"client":a,left:void 0===i?-1/0:i,top:void 0===o?-1/0:o,right:void 0===s?1/0:s,bottom:void 0===u?1/0:u};return{vertical:tg(l,e,!0),horizontal:tg(l,r,!1)}}function tp(t,e){var r=t.state,n=r.containerClientRect,a=n.clientHeight,i=n.clientWidth,o=n.clientLeft,s=n.clientTop,u=r.snapOffset,l=u.left,c=u.top,f=u.right,d=u.bottom,p=e||t.props.bounds||{},g="css"===(p.position||"client"),h=p.left,v=p.top,m=p.right,b=void 0===m?g?-1/0:1/0:m,x=p.bottom,E=void 0===x?g?-1/0:1/0:x;return g&&(b=i+f-l-b,E=a+d-c-E),{left:(void 0===h?-1/0:h)+l-o,right:b+l-o,top:(void 0===v?-1/0:v)+c-s,bottom:E+c-s}}function tg(t,e,r){var n=t[r?"left":"top"],a=t[r?"right":"bottom"],i=Math.min.apply(Math,e),o=Math.max.apply(Math,e),s=[];return n+1>i&&s.push({isBound:!0,offset:i-n,pos:n}),a-1<o&&s.push({isBound:!0,offset:o-a,pos:a}),s.length||s.push({isBound:!1,offset:0,pos:0}),s.sort(function(t,e){return Math.abs(e.offset)-Math.abs(t.offset)})}function th(t,e,r){return(r?t.map(function(t){return(0,o.e$)(t,r)}):t).some(function(t){return t[0]<e.left&&Math.abs(t[0]-e.left)>.1||t[0]>e.right&&Math.abs(t[0]-e.right)>.1||t[1]<e.top&&Math.abs(t[1]-e.top)>.1||t[1]>e.bottom&&Math.abs(t[1]-e.bottom)>.1})}function tv(t,e){var r,n,a,o,s,u,l,c,f,d;return r=m(m({},t),{classNames:b([tQ("line","guideline",t.direction)],t.classNames,!0).filter(function(t){return t}),size:t.size||"".concat(t.sizeValue,"px"),pos:t.pos||t.posValue.map(function(t){return"".concat((0,i.nF)(t,.1),"px")})}),a=r.direction,o=r.classNames,s=r.size,u=r.pos,l=r.zoom,c=r.key,d=(f="horizontal"===a)?"Y":"X",e.createElement("div",{key:c,className:o.join(" "),style:((n={})[f?"width":"height"]="".concat(s),n.transform="translate(".concat(u[0],", ").concat(u[1],") translate").concat(d,"(-50%) scale").concat(d,"(").concat(l,")"),n)})}function tm(t,e,r,n,a,i,o,s){var u=t.props.zoom;return r.map(function(t,r){var l=t.type,c=t.pos,f=[0,0];return f[o]=n,f[+!o]=-a+c,tv({key:"".concat(e,"TargetGuideline").concat(r),classNames:[tQ("target","bold",l)],posValue:f,sizeValue:i,zoom:u,direction:e},s)})}function tb(t,e,r,n,a,i){var o=t.props,s=o.zoom,u=o.isDisplayInnerSnapDigit,l="horizontal"===e?j:X,c=a[l.start],f=a[l.end];return r.filter(function(t){var e=t.hide,r=t.elementRect;if(e)return!1;if(u&&r){var n=r.rect;if(n[l.start]<=c&&f<=n[l.end])return!1}return!0}).map(function(t,r){var a=t.pos,o=t.size,u=t.element,l=[-n[0]+a[0],-n[1]+a[1]];return tv({key:"".concat(e,"-default-guideline-").concat(r),classNames:u?[tQ("bold")]:[],direction:e,posValue:l,sizeValue:o,zoom:s},i)})}function tx(t,e,r,n,a,i,o,s){var u,l=t.props,c=l.snapDigit,f=l.isDisplaySnapDigit,d=l.snapDistFormat,p=l.zoom,g="horizontal"===e?"X":"Y",h="vertical"===e?"height":"width",v=Math.abs(a),m=void 0===f||f?parseFloat(v.toFixed(void 0===c?0:c)):0;return s.createElement("div",{key:"".concat(e,"-").concat(r,"-guideline-").concat(n),className:tQ("guideline-group",e),style:((u={left:"".concat(i[0],"px"),top:"".concat(i[1],"px")})[h]="".concat(v,"px"),u)},tv({direction:e,classNames:[tQ(r),o],size:"100%",posValue:[0,0],sizeValue:v,zoom:p},s),s.createElement("div",{className:tQ("size-value","gap"),style:{transform:"translate".concat(g,"(-50%) scale(").concat(p,")")}},m>0?(void 0===d?function(t){return t}:d)(m):""))}function tE(t,e,r,n,a){var o=function(t,e,r,n){var a=e[0]-t[0],o=e[1]-t[1];if(Math.abs(a)<i._4&&(a=0),Math.abs(o)<i._4&&(o=0),!a)return n?[0,0]:[0,r];if(!o)return n?[r,0]:[0,0];var s=o/a,u=t[1]-s*t[0];if(!n)return[(e[1]+r-u)/s-e[0],r];var l=s*(e[0]+r)+u;return[r,l-e[1]]}(t,e,r,n);if(!o)return{isOutside:!1,offset:[0,0]};var s=(0,i.l$)(t,e),u=(0,i.l$)(o,t),l=(0,i.l$)(o,e),c=tF({datas:a,distX:o[0],distY:o[1]});return{offset:[c[0],c[1]],isOutside:u>s||l>s}}function tS(t,e){return t.isBound?t.offset:e.isSnap?tt(e).offset:0}function tR(t,e,r,n){void 0===n&&(n=r);var a=td(tp(t),n.vertical,n.horizontal),i=a.horizontal,o=a.vertical,s=e?{horizontal:{isSnap:!1,index:-1},vertical:{isSnap:!1,index:-1}}:Z(t,r.vertical,r.horizontal),u=s.horizontal,l=s.vertical,c=tS(i[0],u),f=tS(o[0],l),d=Math.abs(c),p=Math.abs(f);return{horizontal:{isBound:i[0].isBound,isSnap:u.isSnap,snapIndex:u.index,offset:c,dist:d,bounds:i,snap:u},vertical:{isBound:o[0].isBound,isSnap:l.isSnap,snapIndex:l.index,offset:f,dist:p,bounds:o,snap:l}}}function tM(t,e,r,n,a){var i=td(e,r,n),o=i.horizontal,s=i.vertical,u=Q(t,r,n,a),l=u.horizontal,c=u.vertical,f=tS(o[0],l),d=tS(s[0],c),p=Math.abs(f),g=Math.abs(d);return{horizontal:{isBound:o[0].isBound,isSnap:l.isSnap,snapIndex:l.index,offset:f,dist:p,bounds:o,snap:l},vertical:{isBound:s[0].isBound,isSnap:c.isSnap,snapIndex:c.index,offset:d,dist:g,bounds:s,snap:c}}}function tD(t,e){return t.isBound?t.offset:e.isSnap?e.offset:0}function ty(t){var e=t.state;if(!e.guidelines||!e.guidelines.length){var r=t.state.container,n=t.props.snapContainer||r,a=e.containerClientRect,o={left:0,top:0,bottom:0,right:0};if(r!==n){var s=ez(n,!0);if(s){var u=ec(s),l=eO(e,[u.left-a.left,u.top-a.top]),c=eO(e,[u.right-a.right,u.bottom-a.bottom]);o.left=(0,i.nF)(l[0],.1),o.top=(0,i.nF)(l[1],.1),o.right=(0,i.nF)(c[0],.1),o.bottom=(0,i.nF)(c[1],.1)}}e.snapOffset=o,e.guidelines=$(t),e.enableSnap=!0}}function tC(t,e,r,n,a,i){var s=t9(t,e,r,i?4:3),u=tq(s,n);return ed(s,(0,o.Rd)(a,u))}function tB(t,e,r,n,a,s,u,l){for(var c=ep(t.state),f=t.props.keepRatio,d=0,p=0,g=0;g<2;++g){var h=function(t,e,r,n,a,s){var u=function(t,e,r){var n=[];if(r)1!==Math.abs(e[0])||1!==Math.abs(e[1])?n.push([e,[-1,-1]],[e,[-1,1]],[e,[1,-1]],[e,[1,1]]):n.push([e,[t[0],-t[1]]],[e,[-t[0],t[1]]],[e,t]),n.push([e,t]);else if(t[0]&&t[1])n.push([e,[t[0],-t[1]]],[e,[-t[0],t[1]]]);else if(t[0]){var a=1===Math.abs(e[0])?[1]:[1,-1];a.forEach(function(r){n.push([[e[0],-1],[r*t[0],-1]],[[e[0],0],[r*t[0],0]],[[e[0],1],[r*t[0],1]])})}else if(t[1]){var a=1===Math.abs(e[1])?[1]:[1,-1];a.forEach(function(r){n.push([[-1,e[1]],[-1,r*t[1]]],[[0,e[1]],[0,r*t[1]]],[[1,e[1]],[1,r*t[1]]])})}else n.push([e,[1,0]],[e,[-1,0]],[e,[0,-1]],[e,[0,1]],[[1,0],[1,-1]],[[1,0],[1,1]],[[0,1],[1,1]],[[0,1],[-1,1]],[[-1,0],[-1,-1]],[[-1,0],[-1,1]],[[0,-1],[1,-1]],[[0,-1],[-1,-1]]);return n}(r,s.fixedDirection,n),l=tc(e,r,n),c=b(b([],u.map(function(r){var u,l,c,f,d,p,g,h,v,m,b,x,E=r[0],S=r[1],R=tq(e,E),M=tq(e,S),D=n?function(t,e,r,n){var a=function(t,e,r){var n=tp(t),a=n.left,i=n.top,s=n.right,u=n.bottom,l=r[0],c=r[1],f=(0,o.Rd)(r,e),d=f[0],p=f[1];1e-7>Math.abs(d)&&(d=0),1e-7>Math.abs(p)&&(p=0);var g=p>0,h=d>0,v={isBound:!1,offset:0,pos:0},m={isBound:!1,offset:0,pos:0};if(0===d&&0===p);else if(0===d)g?u<c&&(m.pos=u,m.offset=c-u):i>c&&(m.pos=i,m.offset=c-i);else if(0===p)h?s<l&&(v.pos=s,v.offset=l-s):a>l&&(v.pos=a,v.offset=l-a);else{var b=p/d,x=r[1]-b*l,E=0,S=0,R=!1;h&&s<=l?(E=b*s+x,S=s,R=!0):!h&&l<=a&&(E=b*a+x,S=a,R=!0),R&&(E<i||E>u)&&(R=!1),R||(g&&u<=c?(S=((E=u)-x)/b,R=!0):!g&&c<=i&&(S=((E=i)-x)/b,R=!0)),R&&(v.isBound=!0,v.pos=S,v.offset=l-S,m.isBound=!0,m.pos=E,m.offset=c-E)}return{vertical:v,horizontal:m}}(t,e,r),i=a.horizontal,s=a.vertical,u=n?{horizontal:{isSnap:!1},vertical:{isSnap:!1}}:function(t,e,r){var n=r[0],a=r[1],i=e[0],s=e[1],u=(0,o.Rd)(r,e),l=u[0],c=u[1],f=c>0,d=l>0;l=eB(l),c=eB(c);var p={isSnap:!1,offset:0,pos:0},g={isSnap:!1,offset:0,pos:0};if(0===l&&0===c)return{vertical:p,horizontal:g};var h=Z(t,l?[n]:[],c?[a]:[]),v=h.vertical,m=h.horizontal;v.posInfos.filter(function(t){var e=t.pos;return d?e>=i:e<=i}),m.posInfos.filter(function(t){var e=t.pos;return f?e>=s:e<=s}),v.isSnap=v.posInfos.length>0,m.isSnap=m.posInfos.length>0;var b=tt(v),x=b.isSnap,E=b.guideline,S=tt(m),R=S.isSnap,M=S.guideline,D=R?M.pos[1]:0,y=x?E.pos[0]:0;if(0===l)R&&(g.isSnap=!0,g.pos=M.pos[1],g.offset=a-g.pos);else if(0===c)x&&(p.isSnap=!0,p.pos=y,p.offset=n-y);else{var C=c/l,B=r[1]-C*n,G=0,z=0,w=!1;x?(G=C*(z=y)+B,w=!0):R&&(z=((G=D)-B)/C,w=!0),w&&(p.isSnap=!0,p.pos=z,p.offset=n-z,g.isSnap=!0,g.pos=G,g.offset=a-G)}return{vertical:p,horizontal:g}}(t,e,r),l=u.horizontal,c=u.vertical,f=tD(i,l),d=tD(s,c),p=Math.abs(f),g=Math.abs(d);return{horizontal:{isBound:i.isBound,isSnap:l.isSnap,offset:f,dist:p},vertical:{isBound:s.isBound,isSnap:c.isSnap,offset:d,dist:g}}}(t,R,M,a):tR(t,a,{vertical:[M[0]],horizontal:[M[1]]}),y=D.horizontal,C=y.offset,B=y.isBound,G=y.isSnap,z=D.vertical,w=z.offset,O=z.isBound,P=z.isSnap,T=(0,o.Rd)(S,E);if(!w&&!C)return{isBound:O||B,isSnap:P||G,sign:T,offset:[0,0]};var k=(u=(0,i.Mu)(R,M)/Math.PI*180,c=(l=D.vertical).isBound,f=l.isSnap,d=l.dist,g=(p=D.horizontal).isBound,h=p.isSnap,v=p.dist,b=(m=u%180)<3||m>177,x=m>87&&m<93,v<d&&(c||f&&!x&&(!n||!b))?"vertical":!g&&(!h||b||n&&x)?"":"horizontal");if(!k)return{sign:T,isBound:!1,isSnap:!1,offset:[0,0]};var I="vertical"===k,A=tE(R,M,-(I?w:C),I,s).offset.map(function(t,e){return t*(T[e]?2/T[e]:0)});return{sign:T,isBound:I?O:B,isSnap:I?P:G,offset:A}}),!0),tl(t,l,tq(e,[0,0]),s),!0),f=tn(c,0),d=tn(c,1);return{width:{isBound:f.isBound,offset:f.offset[0]},height:{isBound:d.isBound,offset:d.offset[1]}}}(t,e(d,p),a,f,u,l),v=h.width,m=h.height,x=v.isBound,E=m.isBound,S=v.offset,R=m.offset;if(1===g&&(x||(S=0),E||(R=0)),0===g&&u&&!x&&!E)return[0,0];if(f){var M=Math.abs(S)*(r?1/r:1),D=Math.abs(R)*(n?1/n:1);(x&&E?M<D:E||!x&&M<D)?S=r*R/n:R=n*S/r}d+=S,p+=R}if(a[0]&&a[1]){var y=function(t,e,r,n,a){var o=[-r[0],-r[1]],s=t.state,u=s.width,l=s.height,c=t.props.bounds,f=1/0,d=1/0;if(c){var p=[[r[0],-r[1]],[-r[0],r[1]]],g=c.left,h=void 0===g?-1/0:g,v=c.top,m=void 0===v?-1/0:v,b=c.right,x=void 0===b?1/0:b,E=c.bottom,S=void 0===E?1/0:E;p.forEach(function(t){var r=t[0]!==o[0],s=t[1]!==o[1],c=tq(e,t),p=360*(0,i.Mu)(n,c)/Math.PI;if(s){var g=c.slice();(2>Math.abs(p-360)||2>Math.abs(p-180))&&(g[1]=n[1]);var v=tE(n,g,(n[1]<c[1]?S:m)-c[1],!1,a),b=v.offset[1],E=v.isOutside;isNaN(b)||(d=l+(E?1:-1)*Math.abs(b))}if(r){var g=c.slice();(2>Math.abs(p-90)||2>Math.abs(p-270))&&(g[0]=n[0]);var R=tE(n,g,(n[0]<c[0]?x:h)-c[0],!0,a),M=R.offset[0],D=R.isOutside;isNaN(M)||(f=u+(D?1:-1)*Math.abs(M))}})}return{maxWidth:f,maxHeight:d}}(t,c,a,s,l),C=y.maxWidth,B=y.maxHeight,G=function(t,e,r,n,a,i,o,s,u){var l=tq(e,o),c=tR(t,s,{vertical:[l[0]],horizontal:[l[1]]}),f=c.horizontal.offset,d=c.vertical.offset;if(d||f){var p=tF({datas:u,distX:-d,distY:-f}),g=p[0],h=p[1];return[Math.min(a||1/0,r+o[0]*g)-r,Math.min(i||1/0,n+o[1]*h)-n]}return[0,0]}(t,e(d,p).map(function(t){return t.map(function(t){return(0,i.nF)(t,1e-4)})}),r+d,n+p,C,B,a,u,l),S=G[0],R=G[1];d+=S,p+=R}return[d,p]}function tG(t){var e=[];return t.forEach(function(t){t.guidelineInfos.forEach(function(t){var r=t.guideline;e.indexOf(r)>-1||e.push(r)})}),e}function tz(t,e,r,n,a,o){var s=td(tp(t,o),e,r),u=s.vertical,l=s.horizontal;u.forEach(function(t){t.isBound&&n.push({type:"bounds",pos:t.pos})}),l.forEach(function(t){t.isBound&&a.push({type:"bounds",pos:t.pos})});var c=function(t){var e=t.props.innerBounds;if(!e)return{vertical:[],horizontal:[]};var r=t.getRect(),n=r.pos1,a=r.pos2,i=r.pos3,o=r.pos4,s=tq([n,a,i,o],[0,0]),u=e.left,l=e.top,c=e.width,f=e.height,d=[[u,l],[u,l+f]],p=[[u,l],[u+c,l]],g=[[u+c,l],[u+c,l+f]],h=[[u,l+f],[u+c,l+f]],v=[],m=[],b={top:!1,bottom:!1,left:!1,right:!1};return[[n,a],[a,o],[o,i],[i,n]].forEach(function(t){var e=ta(s,t),r=e.horizontal,n=e.vertical,a=tu(t,p,n,1,!0),i=tu(t,h,n,1,!0),o=tu(t,d,r,1,!0),x=tu(t,g,r,1,!0);a.isBound&&!b.top&&(v.push(l),b.top=!0),i.isBound&&!b.bottom&&(v.push(l+f),b.bottom=!0),o.isBound&&!b.left&&(m.push(u),b.left=!0),x.isBound&&!b.right&&(m.push(u+c),b.right=!0)}),{horizontal:v,vertical:m}}(t),f=c.vertical,d=c.horizontal;f.forEach(function(t){(0,i.SL)(n,function(e){var r=e.type,n=e.pos;return"bounds"===r&&n===t})>=0||n.push({type:"bounds",pos:t})}),d.forEach(function(t){(0,i.SL)(a,function(e){var r=e.type,n=e.pos;return"bounds"===r&&n===t})>=0||a.push({type:"bounds",pos:t})})}var tw={name:"draggable",props:{draggable:Boolean,throttleDrag:Number,throttleDragRotate:Number,startDragRotate:Number,edgeDraggable:Boolean},events:{onDragStart:"dragStart",onDrag:"drag",onDragEnd:"dragEnd",onDragGroupStart:"dragGroupStart",onDragGroup:"dragGroup",onDragGroupEnd:"dragGroupEnd"},render:function(t,e){var r=t.props,n=r.throttleDragRotate,a=r.zoom,o=t.state,s=o.dragInfo,u=o.beforeOrigin;if(!n||!s)return[];var l=s.dist;if(!l[0]&&!l[1])return[];var c=er(l),f=(0,i.Mu)(l,[0,0]);return[e.createElement("div",{className:tQ("line","horizontal","dragline","dashed"),key:"dragRotateGuideline",style:{width:"".concat(c,"px"),transform:"translate(".concat(u[0],"px, ").concat(u[1],"px) rotate(").concat(f,"rad) scaleY(").concat(a,")")}})]},dragStart:function(t,e){var r=e.datas,n=e.parentEvent,a=e.parentGesto,i=t.state,o=i.target;if(i.gesto)return!1;i.gesto=a||t.targetGesto;var s=ex(o);r.datas={},r.left=parseFloat(s.left||"")||0,r.top=parseFloat(s.top||"")||0,r.bottom=parseFloat(s.bottom||"")||0,r.right=parseFloat(s.right||"")||0,r.startValue=[0,0],tP(t,e),tH(e,"translate"),r.absolutePoses=ep(t.state),r.prevDist=[0,0],r.prevBeforeDist=[0,0],r.isDrag=!1,r.deltaOffset=[0,0];var u=eh(t,e,m({set:function(t){r.startValue=t}},t_(e)));return!1!==(n||eb(t,"onDragStart",u))?(r.isDrag=!0,t.state.dragInfo={startRect:t.getRect(),dist:[0,0]}):(i.gesto=null,r.isPinch=!1),!!r.isDrag&&u},drag:function(t,e){if(e){tT(e,"translate");var r=e.datas,n=e.parentEvent,a=e.parentFlag,s=e.isPinch,u=e.isRequest,l=e.deltaOffset,c=e.distX,f=e.distY,d=r.isDrag,p=r.prevDist,g=r.prevBeforeDist,h=r.startValue;if(d){l&&(c+=l[0],f+=l[1]);var v,m,b,x,E,S,R=t.props,M=R.parentMoveable,D=n?0:R.throttleDrag||0,y=n?0:R.throttleDragRotate||0,C=!1,B=0;if(!n&&y>0&&(c||f)){var G=R.startDragRotate||0,z=(0,i.nF)(G+180*(0,i.Mu)([0,0],[c,f])/Math.PI,y)-G,w=f*Math.abs(Math.cos((z-90)/180*Math.PI)),O=er([c*Math.abs(Math.cos(z/180*Math.PI)),w]);c=O*Math.cos(B=z*Math.PI/180),f=O*Math.sin(B)}if(!s&&!n&&!a&&(!y||c||f)){var P=function(t,e,r,n,a,s){if(!L(t,"draggable"))return[{isSnap:!1,isBound:!1,offset:0},{isSnap:!1,isBound:!1,offset:0}];var u,l,c,f,d,p,g,h,v,m,b,x,E,S,R,M,D,y,C,B=ed(s.absolutePoses,[e,r]),G=et(B),z=G.left,w=G.right,O=G.top,P=G.bottom,T={horizontal:B.map(function(t){return t[1]}),vertical:B.map(function(t){return t[0]})},k=U(V(t.props.snapDirections),{left:z,right:w,top:O,bottom:P,center:(z+w)/2,middle:(O+P)/2}),I=tR(t,a,k,T),A=I.vertical,F=I.horizontal,Y=(S=tn(E=tl(t,tc(B,[0,0],!1).map(function(t){var e=t[0],r=t[1],n=t[2];return[e.map(function(t){return 2*Math.abs(t)}),r,n]}),tq(B,[0,0]),s),0),R=tn(E,1),M=0,D=0,y=S.isVerticalBound||R.isVerticalBound,C=S.isHorizontalBound||R.isHorizontalBound,(y||C)&&(M=(c=(u={datas:s,distX:-S.offset[0],distY:-R.offset[1]}).datas,f=u.distX,d=u.distY,p=c.beforeMatrix,g=c.matrix,h=c.is3d,v=c.startDragBeforeDist,m=c.startDragDist,b=c.absoluteOrigin,x=(0,o.Rd)((0,o.Di)(g,(0,o.tY)(l?v:m,[f,d]),h?4:3),b))[0],D=x[1]),{vertical:{isBound:y,offset:M},horizontal:{isBound:C,offset:D}}),N=Y.vertical,q=Y.horizontal,W=A.isSnap,_=F.isSnap,H=A.isBound||N.isBound,j=F.isBound||q.isBound,X=function(t,e,r,n,a){var s=e[0],u=e[1],l=r[0],c=r[1],f=n[0],d=n[1],p=a[0],g=a[1],h=-p,v=-g;if(t&&s&&u){h=0,v=0;var m=[];if(l&&c?m.push([0,g],[p,0]):l?m.push([p,0]):c?m.push([0,g]):f&&d?m.push([0,g],[p,0]):f?m.push([p,0]):d&&m.push([0,g]),m.length){m.sort(function(t,e){return er((0,o.Rd)([s,u],t))-er((0,o.Rd)([s,u],e))});var b=m[0];if(b[0]&&Math.abs(s)>i._4?v=u*Math.abs(s+(h=-b[0]))/Math.abs(s)-u:b[1]&&Math.abs(u)>i._4&&(h=s*Math.abs(u+(v=-b[1]))/Math.abs(u)-s),t&&c&&l)if(Math.abs(h)>i._4&&Math.abs(h)<Math.abs(p)){var x=Math.abs(p)/Math.abs(h);h*=x,v*=x}else if(Math.abs(v)>i._4&&Math.abs(v)<Math.abs(g)){var x=Math.abs(g)/Math.abs(v);h*=x,v*=x}else h=eR(-p,h),v=eR(-g,v)}}else h=s||l?-p:0,v=u||c?-g:0;return[h,v]}(n,[e,r],[H,j],[W,_],[eR(A.offset,N.offset),eR(F.offset,q.offset)]);return[{isBound:H,isSnap:W,offset:X[0]},{isBound:j,isSnap:_,offset:X[1]}]}(t,c,f,y,u||l,r),T=P[0],k=P[1],I=T.isSnap,A=T.isBound,F=T.offset,Y=k.isSnap,N=k.isBound,q=k.offset;C=I||Y||A||N,c+=F,f+=q}var W=(0,o.tY)(tA({datas:r,distX:c,distY:f}),h),_=(0,o.tY)((x=(b=tA({datas:m=(v={datas:r,distX:c,distY:f}).datas,distX:v.distX,distY:v.distY}))[0],E=b[1],S=tI(m,(0,o.kN)([x,E],4)),(0,o.Di)(S,(0,o.ez)([0,0,0],4),4)),h);y||C||((0,i.cC)(_,D),(0,i.cC)(W,D));var H=(0,o.Rd)(W,h),j=(0,o.Rd)(_,h),X=(0,o.Rd)(j,p),K=(0,o.Rd)(H,g);r.prevDist=j,r.prevBeforeDist=H,r.passDelta=X,r.passDist=j;var $=r.left+H[0],Z=r.top+H[1],Q=r.right-H[0],J=r.bottom-H[1],tt=tk(r,"translate(".concat(_[0],"px, ").concat(_[1],"px)"),"translate(".concat(j[0],"px, ").concat(j[1],"px)"));if(tX(e,tt),t.state.dragInfo.dist=n?[0,0]:j,!(!n&&!M&&X.every(function(t){return!t})&&K.some(function(t){return!t}))){var te=t.state,tr=eh(t,e,{transform:tt,dist:j,delta:X,translate:_,beforeDist:H,beforeDelta:K,beforeTranslate:W,left:$,top:Z,right:Q,bottom:J,width:te.width,height:te.height,isPinch:s});return n||eb(t,"onDrag",tr),tr}}}},dragAfter:function(t,e){var r=e.datas,n=r.deltaOffset;return(!!n[0]||!!n[1])&&(r.deltaOffset=[0,0],this.drag(t,m(m({},e),{deltaOffset:n})))},dragEnd:function(t,e){var r=e.parentEvent,n=e.datas;if(t.state.gesto=null,t.state.dragInfo=null,n.isDrag){n.isDrag=!1;var a=ev(t,e,{});return r||eb(t,"onDragEnd",a),a}},dragGroupStart:function(t,e){var r=e.datas,n=e.clientX,a=e.clientY,i=this.dragStart(t,e);if(!i)return!1;var o=P(t,this,"dragStart",[n||0,a||0],e,!1),s=m(m({},i),{targets:t.props.targets,events:o});return r.isDrag=!1!==eb(t,"onDragGroupStart",s),!!r.isDrag&&i},dragGroup:function(t,e){if(e.datas.isDrag){var r=this.drag(t,e),n=P(t,this,"drag",e.datas.passDelta,e,!1);if(r){var a=m({targets:t.props.targets,events:n},r);return eb(t,"onDragGroup",a),a}}},dragGroupEnd:function(t,e){var r=e.isDrag;if(e.datas.isDrag){this.dragEnd(t,e);var n=P(t,this,"dragEnd",[0,0],e,!1);return eb(t,"onDragGroupEnd",ev(t,e,{targets:t.props.targets,events:n})),r}},request:function(t){var e={},r=t.getRect(),n=0,a=0;return{isControl:!1,requestStart:function(){return{datas:e}},request:function(t){return"x"in t?n=t.x-r.left:"deltaX"in t&&(n+=t.deltaX),"y"in t?a=t.y-r.top:"deltaY"in t&&(a+=t.deltaY),{datas:e,distX:n,distY:a}},requestEnd:function(){return{datas:e,isDrag:!0}}}},unset:function(t){t.state.dragInfo=null}};function tO(t,e){var r=e.clientX,n=e.clientY,a=e.datas,i=t.state,s=i.moveableClientRect,u=i.rootMatrix,l=i.is3d,c=i.pos1,f=s.left,d=s.top,p=(0,o.Rd)(eM(u,[r-f,n-d],l?4:3),c),g=tF({datas:a,distX:p[0],distY:p[1]});return[g[0],g[1]]}function tP(t,e){var r=e.datas,n=t.state,a=n.allMatrix,i=n.beforeMatrix,s=n.is3d,u=n.left,l=n.top,c=n.origin,f=n.offsetMatrix,d=n.targetMatrix,p=n.transformOrigin,g=s?4:3;r.is3d=s,r.matrix=a,r.targetMatrix=d,r.beforeMatrix=i,r.offsetMatrix=f,r.transformOrigin=p,r.inverseMatrix=(0,o.B8)(a,g),r.inverseBeforeMatrix=(0,o.B8)(i,g),r.absoluteOrigin=(0,o.ez)((0,o.tY)([u,l],c),g),r.startDragBeforeDist=(0,o.Di)(r.inverseBeforeMatrix,r.absoluteOrigin,g),r.startDragDist=(0,o.Di)(r.inverseMatrix,r.absoluteOrigin,g)}function tT(t,e){var r,n,a,i,u,l,c,f,d,p,g,h,v,m,x,E,S,R=t.datas,M=t.originalDatas.beforeRenderable,D=R.transformIndex,y=M.nextTransforms,C=M.nextTransformAppendedIndexes,B=0;-1===D?R.transformIndex=B=y.length:B=D+C.filter(function(t){return t<D}).length;var G=(r=y,n=B,a=r.slice(0,n<0?void 0:n),i=r.slice(0,n<0?void 0:n+1),u=r[n]||"",l=n<0?[]:r.slice(n),c=n<0?[]:r.slice(n+1),f=(0,s.qg)(a),d=(0,s.qg)(i),p=(0,s.qg)([u]),g=(0,s.qg)(l),h=(0,s.qg)(c),v=(0,s.ap)(f),m=(0,s.ap)(d),x=(0,s.ap)(g),E=(0,s.ap)(h),S=(0,o.lw)(v,x,4),{transforms:r,beforeFunctionMatrix:v,beforeFunctionMatrix2:m,targetFunctionMatrix:(0,s.ap)(p),afterFunctionMatrix:x,afterFunctionMatrix2:E,allFunctionMatrix:S,beforeFunctions:f,beforeFunctions2:d,targetFunction:p[0],afterFunctions:g,afterFunctions2:h,beforeFunctionTexts:a,beforeFunctionTexts2:i,targetFunctionText:u,afterFunctionTexts:l,afterFunctionTexts2:c}),z=G.targetFunction;R.beforeFunctionTexts=G.beforeFunctionTexts,R.afterFunctionTexts=G.afterFunctionTexts,R.beforeTransform=G.beforeFunctionMatrix,R.beforeTransform2=G.beforeFunctionMatrix2,R.targetTansform=G.targetFunctionMatrix,R.afterTransform=G.afterFunctionMatrix,R.afterTransform2=G.afterFunctionMatrix2,R.targetAllTransform=G.allFunctionMatrix,z.functionName===("rotate"===e?"rotateZ":e)?(R.afterFunctionTexts.splice(0,1),R.isAppendTransform=!1):(R.isAppendTransform=!0,M.nextTransformAppendedIndexes=b(b([],C,!0),[B],!1))}function tk(t,e,r){return"".concat(t.beforeFunctionTexts.join(" ")," ").concat(t.isAppendTransform?r:e," ").concat(t.afterFunctionTexts.join(" "))}function tI(t,e,r){var n=t.beforeTransform,a=t.afterTransform,i=t.beforeTransform2,s=t.afterTransform2,u=t.targetAllTransform,l=r?(0,o.lw)(u,e,4):(0,o.lw)(e,u,4),c=(0,o.lw)((0,o.B8)(r?i:n,4),l,4);return(0,o.lw)(c,(0,o.B8)(r?s:a,4),4)}function tA(t){var e=t.datas,r=t.distX,n=t.distY,a=e.inverseBeforeMatrix,i=e.is3d,s=e.startDragBeforeDist,u=e.absoluteOrigin;return(0,o.Rd)((0,o.Di)(a,(0,o.tY)(u,[r,n]),i?4:3),s)}function tF(t,e){var r=t.datas,n=t.distX,a=t.distY,i=r.inverseBeforeMatrix,s=r.inverseMatrix,u=r.is3d,l=r.startDragBeforeDist,c=r.startDragDist,f=r.absoluteOrigin;return(0,o.Rd)((0,o.Di)(e?i:s,(0,o.tY)(f,[n,a]),u?4:3),e?l:c)}function tY(t){var e=[];return t[1]>=0&&(t[0]>=0&&e.push(3),t[0]<=0&&e.push(2)),t[1]<=0&&(t[0]>=0&&e.push(1),t[0]<=0&&e.push(0)),e}function tN(t,e){return tY(e).map(function(e){return t[e]})}function tq(t,e){var r=tN(t,e);return[(0,i.G8)(r.map(function(t){return t[0]})),(0,i.G8)(r.map(function(t){return t[1]}))]}function tW(t,e,r,n){return(0,o.lw)(t,tJ(e,n,r),n)}function t_(t){var e=t.originalDatas.beforeRenderable;return{setTransform:function(r,n){void 0===n&&(n=-1),e.startTransforms=(0,i.cy)(r)?r:(0,i.Z3)(r),tj(t,n)},setTransformIndex:function(e){tj(t,e)}}}function tH(t,e){var r=t.originalDatas.beforeRenderable.startTransforms;tj(t,(0,i.SL)(r,function(t){return 0===t.indexOf("".concat(e,"("))}))}function tj(t,e){var r=t.originalDatas.beforeRenderable,n=t.datas;if(n.transformIndex=e,-1!==e){var a=r.startTransforms[e];a&&(n.startValue=(0,s.qg)([a])[0].functionValue)}}function tX(t,e){t.originalDatas.beforeRenderable.nextTransforms=(0,i.Z3)(e)}function tL(t){return t.originalDatas.beforeRenderable.nextTransforms.join(" ")}function tV(t,e,r,n,a){return tX(a,e),{transform:e,drag:tw.drag(t,z(a,t.state,r,n,!1))}}function tU(t,e,r,n,a){var i,u,l,c,f,d,p,g,h=t.state,v=h.left,m=h.top,b=t.props.groupable,x=(i=t.state,u=i.transformOrigin,l=i.offsetMatrix,c=i.is3d,f=a.beforeTransform,d=a.afterTransform,p=c?4:3,g=(0,s.yV)([e]),tW(l,(0,o._C)((0,o.lw)((0,o.lw)(f,g,4),d,4),4,p),u,p)),E=t$(t,r,x),S=(0,o.Rd)(n,E);return(0,o.Rd)(S,[b?v:0,b?m:0])}function tK(t){var e=t.state,r=e.width,n=e.height,a=e.transformOrigin;return[-1+a[0]/(r/2),-1+a[1]/(n/2)]}function t$(t,e,r){void 0===r&&(r=t.state.allMatrix);var n=t.state,a=n.width,i=n.height,o=n.is3d;return t7(r,[a/2*(1+e[0]),i/2*(1+e[1])],o?4:3)}function tZ(t,e){return tq(ep(t.state),e)}function tQ(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return a.T6.apply(void 0,b([R],t,!1))}function tJ(t,e,r){return(0,o.B4)(e,(0,o.GD)(r,e),t,(0,o.GD)(r.map(function(t){return-t}),e))}function t0(t){var e=t.transformOrigin;return e?e.split(" "):["0","0"]}function t1(t,e){void 0===e&&(e=ex(t));var r=e.transform;if(r&&"none"!==r)return e.transform;if("transform"in t){var n=t.transform.baseVal;if(!n)return"";var a=n.length;if(!a)return"";for(var i=[],o=0;o<a;++o)!function(t){var e=n[0].matrix;i.push("matrix(".concat(["a","b","c","d","e","f"].map(function(t){return e[t]}).join(", "),")"))}(0);return i.join(" ")}return""}function t2(t,e,r){for(var n=document.body,a=!t||r?t:t.parentElement,i=t===e||a===e,o="relative";a&&a!==n;){e===a&&(i=!0);var s=ex(a),u=a.tagName.toLowerCase(),l=t1(a,s);if(o=s.position,"svg"===u||"static"!==o||l&&"none"!==l)break;a=a.parentElement,o="relative"}return{isStatic:"static"===o,isEnd:i||!a||a===n,offsetParent:a||n}}function t3(t,e,r){void 0===r&&(r=ex(t));var n=ex(document.body),a=n.position;if(!e&&(!a||"static"===a))return[0,0];var i=parseInt(n.marginLeft,10),o=parseInt(n.marginTop,10);return"absolute"===r.position&&(("auto"!==r.top||"auto"!==r.bottom)&&(o=0),("auto"!==r.left||"auto"!==r.right)&&(i=0)),[i,o]}function t4(t){t.forEach(function(t){var e=t.matrix;e&&(t.matrix=(0,o._C)(e,3,4))})}function t5(t,e,r){for(var n,a,u,l=t,c=[],f=!r&&t===e,d=f,p=!1,g=3,h=t2(e,e,!0).offsetParent;l&&!d;){d=f;var v=ex(l),m=v.position,b="fixed"===m,x=t1(l,v),R=(0,o.zs)(x&&"none"!==x?(0,i.Gv)(x)?x:(0,s.yV)(x):[1,0,0,1,0,0]),M=R.length;!p&&16===M&&(p=!0,g=4,t4(c),u&&(u=(0,o._C)(u,3,4))),p&&9===M&&(R=(0,o._C)(R,3,4));var D=function(t,e,r,n){var a,o,s,u=t.tagName.toLowerCase(),l=t.offsetLeft,c=t.offsetTop;if(n){var f=(e||document.documentElement).getBoundingClientRect();l-=f.left,c-=f.top}var d=(0,i.b0)(l),p=!d;return p||"svg"===u?s=(o=t0(r).map(function(t){return parseFloat(t)})).slice():(s=(o=S?t0(ex(t,":before")).map(function(e,r){var n,a=(0,i.hg)(e),o=a.value,s=a.unit;return o*(n=0===r,"%"===s?t6(t.ownerSVGElement)[n?"width":"height"]/100:1)}):t0(r).map(function(t){return parseFloat(t)})).slice(),p=!0,l=(a=function(t,e){if(!t.getBBox)return[0,0];var r=t.getBBox(),n=t6(t.ownerSVGElement),a=r.x-n.x,i=r.y-n.y;return[a,i,e[0]-a,e[1]-i]}(t,o))[0],c=a[1],o[0]=a[2],o[1]=a[3]),{tagName:u,isSVG:d,hasOffset:p,offset:[l||0,c||0],origin:o,targetOrigin:s}}(l,e,v,b),y=D.tagName,C=D.hasOffset,B=D.isSVG,G=D.origin,z=D.targetOrigin,w=D.offset,O=w[0],P=w[1];"svg"===y&&u?(c.push({type:"target",target:l,matrix:function(t,e){var r=t6(t),n=r.width,a=r.height,i=r.clientWidth,s=r.clientHeight,u=i/n,l=s/a,c=t.preserveAspectRatio.baseVal,f=c.align,d=c.meetOrSlice,p=[0,0],g=[u,l],h=[0,0];if(1!==f){var v=(f-2)%3,m=Math.floor((f-2)/3);p[0]=n*v/2,p[1]=a*m/2;var b=2===d?Math.max(l,u):Math.min(u,l);g[0]=b,g[1]=b,h[0]=(i-n)/2*v,h[1]=(s-a)/2*m}var x=(0,o.kb)(g,e);return x[e*(e-1)]=h[0],x[e*(e-1)+1]=h[1],tJ(x,e,p)}(l,g)}),c.push({type:"offset",target:l,matrix:(0,o.k8)(g)})):"g"===y&&t!==l&&(O=0,P=0);var T=t2(l,e),k=T.offsetParent,I=T.isEnd,A=T.isStatic;E&&C&&!B&&A&&("relative"===m||"static"===m)&&(O-=k.offsetLeft,P-=k.offsetTop,f=f||I);var F=0,Y=0;if(C&&h!==k&&(F=k.clientLeft,Y=k.clientTop),C&&k===document.body){var N=t3(l,!1,v);O+=N[0],P+=N[1]}if(c.push({type:"target",target:l,matrix:tJ(R,g,G)}),C?c.push({type:"offset",target:l,matrix:(0,o.GD)([O-l.scrollLeft+F,P-l.scrollTop+Y],g)}):c.push({type:"offset",target:l,origin:G}),u||(u=R),n||(n=G),a||(a=z),d||b)break;l=k,f=I,r&&l!==document.body||(d=f)}return u||(u=(0,o.k8)(g)),n||(n=[0,0]),a||(a=[0,0]),{offsetContainer:h,matrixes:c,targetMatrix:u,transformOrigin:n,targetOrigin:a,is3d:p}}function t8(t,e){return void 0===e&&(e=t.length>9),"".concat(e?"matrix3d":"matrix","(").concat((0,o.ZB)(t,!e).join(","),")")}function t6(t){var e=t.clientWidth,r=t.clientHeight;if(!t)return{x:0,y:0,width:0,height:0,clientWidth:e,clientHeight:r};var n=t.viewBox,a=n&&n.baseVal||{x:0,y:0,width:0,height:0};return{x:a.x,y:a.y,width:a.width||e,height:a.height||r,clientWidth:e,clientHeight:r}}function t7(t,e,r){return(0,o.Di)(t,(0,o.ez)(e,r),r)}function t9(t,e,r,n){return[[0,0],[e,0],[0,r],[e,r]].map(function(e){return t7(t,e,n)})}function et(t){var e=t.map(function(t){return t[0]}),r=t.map(function(t){return t[1]}),n=Math.min.apply(Math,e),a=Math.min.apply(Math,r),i=Math.max.apply(Math,e),o=Math.max.apply(Math,r);return{left:n,top:a,right:i,bottom:o,width:i-n,height:o-a}}function ee(t,e,r,n){var a=16===t.length?4:3,o=t9(t,r,n,a),s=o[0],u=s[0],l=s[1],c=o[1],f=c[0],d=c[1],p=o[2],g=p[0],h=p[1],v=o[3],m=v[0],b=v[1],x=t7(t,e,a),E=x[0],S=x[1],R=Math.min(u,f,g,m),M=Math.min(l,d,h,b),D=Math.max(u,f,g,m),y=Math.max(l,d,h,b);return u=u-R||0,f=f-R||0,g=g-R||0,m=m-R||0,l=l-M||0,d=d-M||0,h=h-M||0,b=b-M||0,E=E-R||0,S=S-M||0,{left:R,top:M,right:D,bottom:y,origin:[E,S],pos1:[u,l],pos2:[f,d],pos3:[g,h],pos4:[m,b],direction:(0,i.rB)(o)}}function er(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function en(t,e){return er([e[0]-t[0],e[1]-t[1]])}function ea(t,e,r,n){void 0===r&&(r=1),void 0===n&&(n=(0,i.Mu)(t,e));var a=en(t,e);return{transform:"translateY(-50%) translate(".concat(t[0],"px, ").concat(t[1],"px) rotate(").concat(n,"rad) scaleY(").concat(r,")"),width:"".concat(a,"px")}}function ei(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var a=r.length,i=r.reduce(function(t,e){return t+e[0]},0)/a,o=r.reduce(function(t,e){return t+e[1]},0)/a;return{transform:"translateZ(0px) translate(".concat(i,"px, ").concat(o,"px) rotate(").concat(t,"rad) scale(").concat(e,")")}}function eo(t,e){if(void 0===e&&(e=ex(t)),!(0,i.b0)(t.offsetWidth)||"svg"===t.tagName.toLowerCase()){var r="border-box"===e.boxSizing,n=parseFloat(e.borderLeftWidth)||0,a=parseFloat(e.borderRightWidth)||0,o=parseFloat(e.borderTopWidth)||0,s=parseFloat(e.borderBottomWidth)||0,u=parseFloat(e.paddingLeft)||0,l=parseFloat(e.paddingRight)||0,c=parseFloat(e.paddingTop)||0,f=parseFloat(e.paddingBottom)||0,d=parseFloat(e.width),p=parseFloat(e.height),g=d,h=p,v=d,m=p,b=u+l,x=c+f,E=b+(n+a),S=x+(o+s);return r?(d=g-E,p=h-S):(g=d+E,h=p+S),{svg:!1,offsetWidth:g,offsetHeight:h,clientWidth:d+b,clientHeight:p+x,cssWidth:d,cssHeight:p}}var R=t.getBBox(),g=R.width,h=R.height;return{svg:!0,offsetWidth:g,offsetHeight:h,clientWidth:g,clientHeight:h,cssWidth:g,cssHeight:h}}function es(t,e){return(0,i.Mu)(e>0?t[0]:t[1],e>0?t[1]:t[0])}function eu(t,e,r,n,a){var i=1,s=[0,0],u=el(),l=el(),c=el(),f=function(t,e,r,n){void 0===r&&(r=e);var a=0,i=0,s=0,u={};if(t){var l=eo(t),c=l.offsetWidth,f=l.offsetHeight;a=c,i=f}if(t){var d,p,g,h,v,b,x,E,S,R,M,D,y,C,B,G,z,w,O,P,T,k,I,A,F,Y,N,q,W=(d=t,p=e,g=r,h=n,void 0===g&&(g=p),x=(b=t5(d,p)).matrixes,E=b.is3d,S=b.targetMatrix,R=b.transformOrigin,M=b.targetOrigin,y=(D=t5(b.offsetContainer,g,!0)).matrixes,C=D.is3d,G=(B=h||C||E)?4:3,z="svg"!==d.tagName.toLowerCase()&&"ownerSVGElement"in d,w=S,O=(0,o.k8)(G),P=(0,o.k8)(G),T=(0,o.k8)(G),k=(0,o.k8)(G),I=x.length,y.reverse(),x.reverse(),!E&&B&&(w=(0,o._C)(w,3,4),t4(x)),!C&&B&&t4(y),y.forEach(function(t){P=(0,o.lw)(P,t.matrix,G)}),A=g||document.body,F=(null==(v=y[0])?void 0:v.target)||t2(A,A,!0).offsetParent,Y=y.slice(1).reduce(function(t,e){return(0,o.lw)(t,e.matrix,G)},(0,o.k8)(G)),x.forEach(function(t,e){if(I-2===e&&(T=O.slice()),I-1===e&&(k=O.slice()),!t.matrix){var r=function(t,e,r,n,a){var i,s=t.target,u=t.origin,l=e.matrix,c=eo(s),f=c.offsetWidth,d=c.offsetHeight,p=r.getBoundingClientRect(),g=[0,0];r===document.body&&(g=t3(s,!0));for(var h=s.getBoundingClientRect(),v=h.left-p.left+r.scrollLeft-(r.clientLeft||0)+g[0],m=h.top-p.top+r.scrollTop-(r.clientTop||0)+g[1],b=h.width,x=h.height,E=(0,o.B4)(n,a,l),S=et(t9(E,f,d,n)),R=S.left,M=S.top,D=S.width,y=S.height,C=t7(E,u,n),B=(0,o.Rd)(C,[R,M]),G=[v+B[0]*b/D,m+B[1]*x/y],z=[0,0],w=0;++w<10;){var O=(0,o.B8)(a,n);i=(0,o.Rd)(t7(O,G,n),t7(O,C,n)),z[0]=i[0],z[1]=i[1];var P=et(t9((0,o.B4)(n,a,(0,o.GD)(z,n),l),f,d,n)),T=P.left,k=P.top,I=T-v,A=k-m;if(2>Math.abs(I)&&2>Math.abs(A))break;G[0]-=I,G[1]-=A}return z.map(function(t){return Math.round(t)})}(t,x[e+1],F,G,(0,o.lw)(Y,O,G));t.matrix=(0,o.GD)(r,G)}O=(0,o.lw)(O,t.matrix,G)}),N=!z&&E,w||(w=(0,o.k8)(N?4:3)),q=t8(z&&16===w.length?(0,o._C)(w,4,3):w,N),{rootMatrix:P=(0,o.bn)(P,G,G),beforeMatrix:T,offsetMatrix:k,allMatrix:O,targetMatrix:w,targetTransform:q,transformOrigin:R,targetOrigin:M,is3d:B}),_=ee(W.allMatrix,W.transformOrigin,a,i);u=m(m({},W),_);var H=ee(W.allMatrix,[50,50],100,100);s=es([H.pos1,H.pos2],H.direction)}var j=n?4:3;return m({width:a,height:i,rotation:s,rootMatrix:(0,o.k8)(j),beforeMatrix:(0,o.k8)(j),offsetMatrix:(0,o.k8)(j),allMatrix:(0,o.k8)(j),targetMatrix:(0,o.k8)(j),targetTransform:"",transformOrigin:[0,0],targetOrigin:[0,0],is3d:!!n,left:0,top:0,right:0,bottom:0,origin:[0,0],pos1:[0,0],pos2:[0,0],pos3:[0,0],pos4:[0,0],direction:1},u)}(e,r,a,!1);if(e){var d=f.is3d?4:3,p=ee(f.offsetMatrix,(0,o.tY)(f.transformOrigin,(0,o.$z)(f.targetMatrix,d)),f.width,f.height);i=p.direction,s=(0,o.tY)(p.origin,[p.left-f.left,p.top-f.top]),u=ec(e),l=ec(t2(n,n,!0).offsetParent||document.body,!0),t&&(c=ec(t))}return m({targetClientRect:u,containerClientRect:l,moveableClientRect:c,beforeDirection:i,beforeOrigin:s,originalBeforeOrigin:s,target:e},f)}function el(){return{left:0,right:0,top:0,bottom:0,width:0,height:0,clientLeft:0,clientTop:0,clientWidth:0,clientHeight:0,scrollWidth:0,scrollHeight:0}}function ec(t,e){var r=0,n=0,a=0,i=0;if(t===document.body||t===document.documentElement)a=window.innerWidth,i=window.innerHeight,r=-(document.documentElement.scrollLeft||document.body.scrollLeft),n=-(document.documentElement.scrollTop||document.body.scrollTop);else{var o=t.getBoundingClientRect();r=o.left,n=o.top,a=o.width,i=o.height}var s={left:r,right:r+a,top:n,bottom:n+i,width:a,height:i};return e&&(s.clientLeft=t.clientLeft,s.clientTop=t.clientTop,s.clientWidth=t.clientWidth,s.clientHeight=t.clientHeight,s.scrollWidth=t.scrollWidth,s.scrollHeight=t.scrollHeight,s.overflow="visible"!==ex(t).overflow),s}function ef(t){if(t){var e=t.getAttribute("data-direction");if(e){var r=[0,0];return e.indexOf("w")>-1&&(r[0]=-1),e.indexOf("e")>-1&&(r[0]=1),e.indexOf("n")>-1&&(r[1]=-1),e.indexOf("s")>-1&&(r[1]=1),r}}}function ed(t,e){return[(0,o.tY)(e,t[0]),(0,o.tY)(e,t[1]),(0,o.tY)(e,t[2]),(0,o.tY)(e,t[3])]}function ep(t){var e=t.left,r=t.top;return ed([t.pos1,t.pos2,t.pos3,t.pos4],[e,r])}function eg(t,e){var r;null==(r=t[e])||r.unset(),t[e]=null}function eh(t,e,r){var n=e.datas;n.datas||(n.datas={});var a=m(m({},r),{target:t.state.target,clientX:e.clientX,clientY:e.clientY,inputEvent:e.inputEvent,currentTarget:t,moveable:t,datas:n.datas});return n.isStartEvent?n.lastEvent=a:n.isStartEvent=!0,a}function ev(t,e,r){var n=e.datas,a="isDrag"in r?r.isDrag:e.isDrag;return n.datas||(n.datas={}),m(m({isDrag:a},r),{moveable:t,target:t.state.target,clientX:e.clientX,clientY:e.clientY,inputEvent:e.inputEvent,currentTarget:t,lastEvent:n.lastEvent,isDouble:e.isDouble,datas:n.datas})}function em(t,e,r){t._emitter.on(e,r)}function eb(t,e,r,n){return t.triggerEvent(e,r,n)}function ex(t,e){return window.getComputedStyle(t,e)}function eE(t,e,r){var n={},a={};return t.filter(function(t){var i=t.name;if(n[i]||!e.some(function(e){return t[e]}))return!1;if(!r&&t.ableGroup){if(a[t.ableGroup])return!1;a[t.ableGroup]=!0}return n[i]=!0,!0})}function eS(t,e){return t===e||null==t&&null==e}function eR(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t.sort(function(t,e){return Math.abs(e)-Math.abs(t)}),t[0]}function eM(t,e,r){return(0,o.Di)((0,o.B8)(t,r),(0,o.ez)(e,r),r)}function eD(t,e){var r,n=t.is3d;return r=eM(t.rootMatrix,[e.distX,e.distY],n?4:3),e.distX=r[0],e.distY=r[1],e}function ey(t,e,r,n,a){return(0,o.Rd)(t7(t,(0,o.tY)(r,e),a),n)}function eC(t,e,r){return r?"".concat(t/e*100,"%"):"".concat(t,"px")}function eB(t){return 1e-7>=Math.abs(t)?0:t}function eG(t,e){if(e.isRequest)if("resizable"===e.requestAble||"scalable"===e.requestAble)return e.parentDirection;else return!1;return(0,i.nB)(e.inputEvent.target,tQ("direction"))}function ez(t,e){return t?(0,i.Kg)(t)?e?document.querySelector(t):t:(0,i.Tn)(t)?t():"current"in t?t.current:t:null}function ew(t,e){return t?(!(!t||!(0,i.Gv)(t)||t instanceof Element)&&((0,i.cy)(t)||"length"in t)?[].slice.call(t):[t]).reduce(function(t,r){return(0,i.Kg)(r)&&e?b(b([],t,!0),[].slice.call(document.querySelectorAll(r)),!0):(t.push(ez(r,e)),t)},[]):[]}function eO(t,e){var r=t.rootMatrix,n=t.is3d,a=(0,o.B8)(r,n?4:3);return n||(a=(0,o._C)(a,3,4)),a[12]=0,a[13]=0,a[14]=0,(0,s.c4)(a,e)}function eP(t,e,r,n){var a=r.ratio,o=r.startOffsetWidth,s=r.startOffsetHeight,u=0,l=0,c=n.distX,f=n.distY,d=n.parentDistance,p=n.parentDist,g=n.parentScale,h=n.isPinch,v=r.fixedDirection;if(p)u=p[0],l=p[1],e&&(u?l||(l=u/a):u=l*a);else if(g)u=(g[0]-1)*o,l=(g[1]-1)*s;else if(h)d&&(u=d,l=d*s/o);else{var m=tF({datas:r,distX:c,distY:f});if(m=[0,1].map(function(e){var r=Math.abs(t[e]-v[e]);return 0!==r&&(r=2/r),m[e]*r}),e&&o&&s){var b=Math.cos((0,i.Mu)([0,0],m)-(0,i.Mu)([0,0],t))*er(m);if(t[0])if(t[1]){var x=2*t[0]*o,E=2*t[1]*s,S=er([x+m[0],E+m[1]])-er([x,E]),R=(0,i.Mu)([0,0],[a,1]);u=Math.cos(R)*S,l=Math.sin(R)*S}else l=(u=b)/a;else u=(l=b)*a}else u=t[0]*m[0],l=t[1]*m[1]}return{distWidth:u,distHeight:l}}var eT=x("pinchable",{events:{onPinchStart:"pinchStart",onPinch:"pinch",onPinchEnd:"pinchEnd",onPinchGroupStart:"pinchGroupStart",onPinchGroup:"pinchGroup",onPinchGroupEnd:"pinchGroupEnd"},dragStart:function(){return!0},pinchStart:function(t,e){var r=e.datas,n=e.targets,a=e.angle,i=e.originalDatas,o=t.props,s=o.pinchable,u=o.ables;if(!s)return!1;var l="drag".concat(n?"Group":"","ControlStart"),c=(!0===s?t.controlAbles:u.filter(function(t){return s.indexOf(t.name)>-1})).filter(function(t){return t.canPinch&&t[l]}),f=eh(t,e,{});n&&(f.targets=n),r.isPinch=!1!==eb(t,"onPinch".concat(n?"Group":"","Start"),f),r.ables=c;var d=r.isPinch;return!!d&&(c.forEach(function(r){if(i[r.name]=i[r.name]||{},r[l]){var n=m(m({},e),{datas:i[r.name],parentRotate:a,isPinch:!0});r[l](t,n)}}),t.state.snapRenderInfo={request:e.isRequest,direction:[0,0]},d)},pinch:function(t,e){var r=e.datas,n=e.scale,a=e.distance,i=e.originalDatas,o=e.inputEvent,s=e.targets,u=e.angle;if(r.isPinch){var l=a*(1-1/n),c=eh(t,e,{});s&&(c.targets=s),eb(t,"onPinch".concat(s?"Group":""),c);var f=r.ables,d="drag".concat(s?"Group":"","Control");return f.forEach(function(r){r[d]&&r[d](t,m(m({},e),{datas:i[r.name],inputEvent:o,parentDistance:l,parentRotate:u,isPinch:!0}))}),c}},pinchEnd:function(t,e){var r=e.datas,n=e.isPinch,a=e.inputEvent,i=e.targets,o=e.originalDatas;if(r.isPinch){var s=ev(t,e,{isDrag:n});i&&(s.targets=i),eb(t,"onPinch".concat(i?"Group":"","End"),s);var u=r.ables,l="drag".concat(i?"Group":"","ControlEnd");return u.forEach(function(r){r[l]&&r[l](t,m(m({},e),{isDrag:n,datas:o[r.name],inputEvent:a,isPinch:!0}))}),n}},pinchGroupStart:function(t,e){return this.pinchStart(t,m(m({},e),{targets:t.props.targets}))},pinchGroup:function(t,e){return this.pinch(t,m(m({},e),{targets:t.props.targets}))},pinchGroupEnd:function(t,e){return this.pinchEnd(t,m(m({},e),{targets:t.props.targets}))}}),ek={name:"resizable",ableGroup:"size",canPinch:!0,props:{resizable:Boolean,throttleResize:Number,renderDirections:Array,keepRatio:Boolean,resizeFormat:Function},events:{onResizeStart:"resizeStart",onBeforeResize:"beforeResize",onResize:"resize",onResizeEnd:"resizeEnd",onResizeGroupStart:"resizeGroupStart",onBeforeResizeGroup:"beforeResizeGroup",onResizeGroup:"resizeGroup",onResizeGroupEnd:"resizeGroupEnd"},render:function(t,e){var r=t.props,n=r.resizable,a=r.edge;if(n)return a?A(t,e):k(t,y,e)},dragControlCondition:eG,dragControlStart:function(t,e){var r,n,a=e.inputEvent,s=e.isPinch,u=e.parentDirection,l=e.datas,c=e.parentFlag,f=u||(s?[0,0]:ef(a.target)),d=t.state,p=d.target,g=d.width,h=d.height;if(!f||!p)return!1;s||tP(t,e),l.datas={},l.direction=f,l.startOffsetWidth=g,l.startOffsetHeight=h,l.prevWidth=0,l.prevHeight=0,l.startWidth=(n=[parseFloat((r=ex(p)).width),parseFloat(r.height)])[0],l.startHeight=n[1];var v=[Math.max(0,g-l.startWidth),Math.max(0,h-l.startHeight)];if(l.minSize=v,l.maxSize=[1/0,1/0],!c){var m=ex(p),b=m.position,x=m.minWidth,E=m.minHeight,S=m.maxWidth,R=m.maxHeight,M="static"===b||"relative"===b,D=M?p.parentElement:p.offsetParent,y=g,C=h;if(D&&(y=D.clientWidth,C=D.clientHeight,M)){var B=ex(D);y-=parseFloat(B.paddingLeft)||0,C-=parseFloat(B.paddingTop)||0}l.minSize=(0,o.tY)([(0,i.jc)(x,y)||0,(0,i.jc)(E,C)||0],v),l.maxSize=(0,o.tY)([(0,i.jc)(S,y)||1/0,(0,i.jc)(R,C)||1/0],v)}var G=t.props.transformOrigin||"% %";function z(t){l.ratio=t&&isFinite(t)?t:0}function O(t){l.fixedDirection=t,l.fixedPosition=tq(l.startPositions,t)}l.transformOrigin=G&&(0,i.Kg)(G)?G.split(" "):G,l.isWidth=!f[0]&&!f[1]||f[0]||!f[1],l.startPositions=ep(t.state),z(g/h),O([-f[0],-f[1]]),l.setFixedDirection=O;var P=eh(t,e,{direction:f,set:function(t){var e=t[0],r=t[1];l.startWidth=e,l.startHeight=r},setMin:function(t){l.minSize=t},setMax:function(t){l.maxSize=[t[0]||1/0,t[1]||1/0]},setRatio:z,setFixedDirection:O,setOrigin:function(t){l.transformOrigin=t},dragStart:tw.dragStart(t,new w().dragStart([0,0],e))});return!1!==eb(t,"onResizeStart",P)&&(l.isResize=!0,t.state.snapRenderInfo={request:e.isRequest,direction:f}),!!l.isResize&&P},dragControl:function(t,e){var r,n,a,s,u,l,c,f,d,p,g,h,v,m,b,x,E,S,R,M,D,y,C=e.datas,B=e.parentFlag,G=e.isPinch,w=e.parentKeepRatio,O=e.dragClient,P=e.parentDist,T=e.isRequest,k=C.isResize,I=C.transformOrigin,A=C.startWidth,F=C.startHeight,Y=C.prevWidth,N=C.prevHeight,q=C.minSize,W=C.maxSize,_=C.ratio,H=C.isWidth,j=C.startOffsetWidth,X=C.startOffsetHeight;if(k){var V=t.props,U=V.resizeFormat,K=V.throttleResize,$=void 0===K?1:K,Z=V.parentMoveable,Q=V.snapThreshold,J=void 0===Q?5:Q,tt=C.direction,te=tt,tr=0,tn=0;tt[0]||tt[1]||(te=[1,1]);var ta=_&&(null!=w?w:V.keepRatio)||!1,ti=tg(),to=ti[0],ts=ti[1];C.setFixedDirection(C.fixedDirection),eb(t,"onBeforeResize",eh(t,e,{setFixedDirection:function(t){var e;return C.setFixedDirection(t),to=(e=tg())[0],ts=e[1],[to,ts]},boundingWidth:to,boundingHeight:ts,setSize:function(t){to=t[0],ts=t[1]}}));var tu=O;O||(tu=!B&&G?tZ(t,[0,0]):C.fixedPosition);var tl=[0,0];if(G||(tl=function(t,e,r,n,a,i,o){if(!L(t,"resizable"))return[0,0];var s=o.fixedDirection,u=t.state,l=u.allMatrix,c=u.is3d;return tB(t,function(t,n){return tC(l,e+t,r+n,s,a,c)},e,r,n,a,i,o)}(t,to,ts,tt,tu,T,C)),P&&(P[0]||(tl[0]=0),P[1]||(tl[1]=0)),ta){te[0]&&te[1]&&tl[0]&&tl[1]&&(Math.abs(tl[0])>Math.abs(tl[1])?tl[1]=0:tl[0]=0);var tc=!tl[0]&&!tl[1];tc&&th(),te[0]&&!te[1]||tl[0]&&!tl[1]||tc&&H?(to+=tl[0],ts=to/_):(!te[0]&&te[1]||!tl[0]&&tl[1]||tc&&!H)&&(ts+=tl[1],to=ts*_)}else j+tr<-J&&(tl[0]=0),j+tn<-J&&(tl[1]=0),to+=tl[0],ts+=tl[1];to=(y=(0,i.u0)([to,ts],q,W,ta))[0],ts=y[1],th();var tf=[(tr=to-j)-Y,(tn=ts-X)-N];C.prevWidth=tr,C.prevHeight=tn;var td=(r=to,n=ts,a=C.fixedDirection,s=tu,u=t.props.groupable,c=(l=t.state).transformOrigin,f=l.targetMatrix,d=l.offsetMatrix,p=l.is3d,g=l.width,h=l.height,v=l.left,m=l.top,b=p?4:3,R=tW(d,f,(x=g,E=h,S=c,(void 0===x&&(x=r),void 0===E&&(E=n),void 0===S&&(S=[0,0]),I)?I.map(function(t,e){var a=(0,i.hg)(t),o=a.value,s=a.unit,u=e?E:x,l=e?n:r;return"%"===t||isNaN(o)?l*(u?S[e]/u:0):"%"!==s?o:l*o/100}):S),b),M=tq(t9(R,r,n,b),a),D=[s[0]-M[0],s[1]-M[1]],(0,o.Rd)(D,[u?v:0,u?m:0]));if(!(!Z&&tf.every(function(t){return!t})&&td.every(function(t){return!t}))){var tp=eh(t,e,{width:A+tr,height:F+tn,offsetWidth:Math.round(to),offsetHeight:Math.round(ts),boundingWidth:to,boundingHeight:ts,direction:tt,dist:[tr,tn],delta:tf,isPinch:!!G,drag:tw.drag(t,z(e,t.state,td,!!G,!1))});return eb(t,"onResize",tp),tp}}function tg(){var t=eP(te,ta,C,e);tr=t.distWidth,tn=t.distHeight;var r=te[0]||ta?Math.max(j+tr,1e-7):j,n=te[1]||ta?Math.max(X+tn,1e-7):X;return ta&&j&&X&&(H?n=r/_:r=n*_),[r,n]}function th(){var t;U&&(to=(t=U([to,ts]))[0],ts=t[1]),to=(0,i.nF)(to,$),ts=(0,i.nF)(ts,$)}},dragControlAfter:function(t,e){var r=e.datas,n=r.isResize,a=r.startOffsetWidth,i=r.startOffsetHeight,o=r.prevWidth,s=r.prevHeight;if(n){var u=t.state,l=u.width,c=u.height,f=l-(a+o),d=c-(i+s),p=Math.abs(f)>3,g=Math.abs(d)>3;if(p&&(r.startWidth+=f,r.startOffsetWidth+=f,r.prevWidth+=f),g&&(r.startHeight+=d,r.startOffsetHeight+=d,r.prevHeight+=d),p||g)return this.dragControl(t,e)}},dragControlEnd:function(t,e){var r=e.datas;if(r.isResize){r.isResize=!1;var n=ev(t,e,{});return eb(t,"onResizeEnd",n),n}},dragGroupControlCondition:eG,dragGroupControlStart:function(t,e){var r=e.datas,n=this.dragControlStart(t,e);if(!n)return!1;var a=O(t,"resizable",e);function i(e,n){var a=r.fixedDirection,i=r.fixedPosition,s=tq(n.datas.startPositions||ep(e.state),a),u=(0,o.Di)((0,o.XI)(-t.rotation/180*Math.PI,3),[s[0]-i[0],s[1]-i[1],1],3),l=u[0],c=u[1];return n.datas.originalX=l,n.datas.originalY=c,n}var s=T(t,this,"dragControlStart",e,function(t,e){return i(t,e)}),u=function(t){n.setFixedDirection(t),s.forEach(function(e,r){e.setFixedDirection(t),i(e.moveable,a[r])})};r.setFixedDirection=u;var l=m(m({},n),{targets:t.props.targets,events:s,setFixedDirection:u});return r.isResize=!1!==eb(t,"onResizeGroupStart",l),!!r.isResize&&n},dragGroupControl:function(t,e){var r=e.datas;if(r.isResize){em(t,"onBeforeResize",function(r){eb(t,"onBeforeResizeGroup",eh(t,e,m(m({},r),{targets:t.props.targets})))});var n=this.dragControl(t,e);if(n){var a=n.boundingWidth,i=n.boundingHeight,s=n.dist,u=t.props.keepRatio,l=[a/(a-s[0]),i/(i-s[1])],c=r.fixedPosition,f=T(t,this,"dragControl",e,function(e,r){var n=(0,o.Di)((0,o.XI)(t.rotation/180*Math.PI,3),[r.datas.originalX*l[0],r.datas.originalY*l[1],1],3),a=n[0],i=n[1];return m(m({},r),{parentDist:null,parentScale:l,dragClient:(0,o.tY)(c,[a,i]),parentKeepRatio:u})}),d=m({targets:t.props.targets,events:f},n);return eb(t,"onResizeGroup",d),d}}},dragGroupControlEnd:function(t,e){var r=e.isDrag;if(e.datas.isResize){this.dragControlEnd(t,e);var n=T(t,this,"dragControlEnd",e),a=ev(t,e,{targets:t.props.targets,events:n});return eb(t,"onResizeGroupEnd",a),r}},request:function(t){var e={},r=0,n=0,a=t.getRect();return{isControl:!0,requestStart:function(t){return{datas:e,parentDirection:t.direction||[1,1]}},request:function(t){return"offsetWidth"in t?r=t.offsetWidth-a.offsetWidth:"deltaWidth"in t&&(r+=t.deltaWidth),"offsetHeight"in t?n=t.offsetHeight-a.offsetHeight:"deltaHeight"in t&&(n+=t.deltaHeight),{datas:e,parentDist:[r,n],parentKeepRatio:t.keepRatio}},requestEnd:function(){return{datas:e,isDrag:!0}}}}};function eI(t,e){return t.map(function(t,r){return(0,i.Om)(t,e[r],1,2)})}function eA(t,e,r){var n=(0,i.Mu)(t,e),a=(0,i.Mu)(t,r)-n;return a>=0?a:a+2*Math.PI}var eF=tQ("area-pieces"),eY=tQ("area-piece"),eN=tQ("avoid");function eq(t){var e=t.areaElement;if(e){var r=t.state,n=r.width,a=r.height;(0,i.vy)(e,eN),e.style.cssText+="left: 0px; top: 0px; width: ".concat(n,"px; height: ").concat(a,"px")}}function eW(t){return t.createElement("div",{key:"area_pieces",className:eF},t.createElement("div",{className:eY}),t.createElement("div",{className:eY}),t.createElement("div",{className:eY}),t.createElement("div",{className:eY}))}var e_={name:"dragArea",props:{dragArea:Boolean,passDragArea:Boolean},events:{onClick:"click",onClickGroup:"clickGroup"},render:function(t,e){var r=t.props,n=r.target,i=r.dragArea,s=r.groupable,u=r.passDragArea,l=t.state,c=l.width,f=l.height,d=l.renderPoses,p=u?tQ("area","pass"):tQ("area");if(s)return[e.createElement("div",{key:"area",ref:(0,a.KR)(t,"areaElement"),className:p}),eW(e)];if(!n||!i)return[];var g=(0,o.qL)([0,0],[c,0],[0,f],[c,f],d[0],d[1],d[2],d[3]),h=g.length?t8(g,!0):"none";return[e.createElement("div",{key:"area",ref:(0,a.KR)(t,"areaElement"),className:p,style:{top:"0px",left:"0px",width:"".concat(c,"px"),height:"".concat(f,"px"),transformOrigin:"0 0",transform:h}}),eW(e)]},dragStart:function(t,e){var r=e.datas,n=e.clientX,a=e.clientY;if(!e.inputEvent)return!1;r.isDragArea=!1;var o=t.areaElement,s=t.state,u=s.moveableClientRect,l=s.renderPoses,c=s.rootMatrix,f=s.is3d,d=u.left,p=u.top,g=et(l),h=g.left,v=g.top,m=g.width,b=g.height,x=eM(c,[n-d,a-p],f?4:3),E=x[0],S=x[1];E-=h,S-=v;var R=[{left:h,top:v,width:m,height:S-10},{left:h,top:v,width:E-10,height:b},{left:h,top:v+S+10,width:m,height:b-S-10},{left:h+E+10,top:v,width:m-E-10,height:b}],M=[].slice.call(o.nextElementSibling.children);R.forEach(function(t,e){M[e].style.cssText="left: ".concat(t.left,"px;top: ").concat(t.top,"px; width: ").concat(t.width,"px; height: ").concat(t.height,"px;")}),(0,i.iQ)(o,eN),s.disableNativeEvent=!0},drag:function(t,e){var r=e.datas,n=e.inputEvent;if(this.enableNativeEvent(t),!n)return!1;r.isDragArea||(r.isDragArea=!0,eq(t))},dragEnd:function(t,e){this.enableNativeEvent(t);var r=e.inputEvent,n=e.datas;if(!r)return!1;n.isDragArea||eq(t)},dragGroupStart:function(t,e){return this.dragStart(t,e)},dragGroup:function(t,e){return this.drag(t,e)},dragGroupEnd:function(t,e){return this.dragEnd(t,e)},unset:function(t){eq(t),t.state.disableNativeEvent=!1},enableNativeEvent:function(t){var e=t.state;e.disableNativeEvent&&(0,i.xi)(function(){e.disableNativeEvent=!1})}},eH=x("origin",{render:function(t,e){var r=t.props.zoom,n=t.state,a=n.beforeOrigin,i=n.rotation;return[e.createElement("div",{className:tQ("control","origin"),style:ei(i,r,a),key:"beforeOrigin"})]}});function ej(t){var e=t.scrollContainer;return[e.scrollLeft,e.scrollTop]}var eX={name:"",props:{target:Object,dragTarget:Object,container:Object,portalContainer:Object,rootContainer:Object,useResizeObserver:Boolean,zoom:Number,transformOrigin:Array,edge:Boolean,ables:Array,className:String,pinchThreshold:Number,pinchOutside:Boolean,triggerAblesSimultaneously:Boolean,checkInput:Boolean,cspNonce:String,translateZ:Number,hideDefaultLines:Boolean,props:Object},events:{}},eL=x("padding",{render:function(t,e){var r=t.props;if(r.dragArea)return[];var n=r.padding||{},a=n.left,i=n.top,s=n.right,u=n.bottom,l=t.state,c=l.renderPoses,f=[l.pos1,l.pos2,l.pos3,l.pos4],d=[];return(void 0===a?0:a)>0&&d.push([0,2]),(void 0===i?0:i)>0&&d.push([0,1]),(void 0===s?0:s)>0&&d.push([1,3]),(void 0===u?0:u)>0&&d.push([2,3]),d.map(function(t,r){var n=t[0],a=t[1],i=f[n],s=f[a],u=c[n],l=c[a],d=(0,o.qL)([0,0],[100,0],[0,100],[100,100],i,s,u,l);if(d.length)return e.createElement("div",{key:"padding".concat(r),className:tQ("padding"),style:{transform:t8(d,!0)}})})}}),eV=["nw","ne","se","sw"];function eU(t,e){var r=t[0]+t[1],n=r>e?e/r:1;return t[0]*=n,t[1]=e-t[1]*n,t}var eK=[1,2,5,6],e$=[0,3,4,7],eZ=[1,-1,-1,1],eQ=[1,1,-1,-1];function eJ(t,e,r,n,a,i,o,s,u){void 0===i&&(i=0),void 0===o&&(o=0),void 0===s&&(s=n),void 0===u&&(u=a);var l=[],c=!1,f=t.map(function(t,f){var d=e[f],p=d.horizontal,g=d.vertical;if(g&&!c&&(c=!0,l.push("/")),c){var h=Math.max(0,1===g?t[1]-o:u-t[1]);return l.push(eC(h,a,r)),h}var h=Math.max(0,1===p?t[0]-i:s-t[0]);return l.push(eC(h,n,r)),h});return{styles:l,raws:f}}function e0(t){for(var e=[0,0],r=[0,0],n=t.length,a=0;a<n;++a){var i=t[a];i.sub&&(i.horizontal&&(0===e[1]&&(e[0]=a),e[1]=a-e[0]+1,r[0]=a+1),i.vertical&&(0===r[1]&&(r[0]=a),r[1]=a-r[0]+1))}return{horizontalRange:e,verticalRange:r}}function e1(t,e,r,n,a,o){void 0===o&&(o=[0,0]);var s,u,l,c,f=t.indexOf("/"),d=(f>-1?t.slice(0,f):t).length,p=t.slice(0,d),g=t.slice(d+1),h=p[0],v=void 0===h?"0px":h,m=p[1],x=void 0===m?v:m,E=p[2],S=void 0===E?v:E,R=p[3],M=void 0===R?x:R,D=g[0],y=void 0===D?v:D,C=g[1],B=void 0===C?y:C,G=g[2],z=void 0===G?y:G,w=g[3],O=void 0===w?B:w,P=[v,x,S,M].map(function(t){return(0,i.jc)(t,e)}),T=[y,B,z,O].map(function(t){return(0,i.jc)(t,r)}),k=P.slice(),I=T.slice();s=eU([k[0],k[1]],e),k[0]=s[0],k[1]=s[1],u=eU([k[3],k[2]],e),k[3]=u[0],k[2]=u[1],l=eU([I[0],I[3]],r),I[0]=l[0],I[3]=l[1],c=eU([I[1],I[2]],r),I[1]=c[0],I[2]=c[1];var A=k.slice(0,Math.max(o[0],p.length)),F=I.slice(0,Math.max(o[1],g.length));return b(b([],A.map(function(t,e){var i=eV[e];return{horizontal:eZ[e],vertical:0,pos:[n+t,a+(-1===eQ[e]?r:0)],sub:!0,raw:P[e],direction:i}}),!0),F.map(function(t,r){var i=eV[r];return{horizontal:0,vertical:eQ[r],pos:[n+(-1===eZ[r]?e:0),a+t],sub:!0,raw:T[r],direction:i}}),!0)}function e2(t,e,r,n,a){void 0===a&&(a=e.length);var i=e0(t.slice(n)),o=i.horizontalRange,s=i.verticalRange,u=r-n,l=0;if(0===u)l=a;else if(u>0&&u<o[1])l=o[1]-u;else{if(!(u>=s[0]))return;l=s[0]+s[1]-u}t.splice(r,l),e.splice(r,l)}function e3(t,e,r,n,a,i,o,s,u,l,c){void 0===l&&(l=0),void 0===c&&(c=0);var f=e0(t.slice(r)),d=f.horizontalRange,p=f.verticalRange;if(n>-1)for(var g=1===eZ[n]?i-l:s-i,h=d[1];h<=n;++h){var v=1===eQ[h]?c:u,m=0;if(n===h?m=i:0===h?m=l+g:-1===eZ[h]&&(m=s-(e[r][0]-l)),t.splice(r+h,0,{horizontal:eZ[h],vertical:0,pos:[m,v]}),e.splice(r+h,0,[m,v]),0===h)break}else if(a>-1){var b=1===eQ[a]?o-c:u-o;if(0===d[1]&&0===p[1]){var x=[l+b,c];t.push({horizontal:eZ[0],vertical:0,pos:x}),e.push(x)}for(var E=p[0],h=p[1];h<=a;++h){var m=1===eZ[h]?l:s,v=0;if(a===h?v=o:0===h?v=c+b:1===eQ[h]?v=e[r+E][1]:-1===eQ[h]&&(v=u-(e[r+E][1]-c)),t.push({horizontal:0,vertical:eQ[h],pos:[m,v]}),e.push([m,v]),0===h)break}}}function e4(t,e){return void 0===e&&(e=t.map(function(t){return t.raw})),{horizontals:t.map(function(t,r){return t.horizontal?e[r]:null}).filter(function(t){return null!=t}),verticals:t.map(function(t,r){return t.vertical?e[r]:null}).filter(function(t){return null!=t})}}var e5=[[0,-1,"n"],[1,0,"e"]],e8=[[-1,-1,"nw"],[0,-1,"n"],[1,-1,"ne"],[1,0,"e"],[1,1,"se"],[0,1,"s"],[-1,1,"sw"],[-1,0,"w"]];function e6(t,e,r){var n=t.props.clipRelative,a=t.state,i=a.width,s=a.height,u=e.type,l=e.poses,c="rect"===u,f="circle"===u;if("polygon"===u)return r.map(function(t){return"".concat(eC(t[0],i,n)," ").concat(eC(t[1],s,n))});if(c||"inset"===u){var d=r[1][1],p=r[3][0],g=r[7][0],h=r[5][1];if(c)return[d,p,h,g].map(function(t){return"".concat(t,"px")});var v=[d,i-p,s-h,g].map(function(t,e){return eC(t,e%2?i:s,n)});if(r.length>8){var m=(0,o.Rd)(r[4],r[0]),x=m[0],E=m[1];v.push.apply(v,b(["round"],eJ(r.slice(8),l.slice(8),n,x,E,g,d,p,h).styles,!1))}return v}if(f||"ellipse"===u){var S=r[0],R=eC(Math.abs(r[1][1]-S[1]),f?Math.sqrt((i*i+s*s)/2):s,n),v=f?[R]:[eC(Math.abs(r[2][0]-S[0]),i,n),R];return v.push("at",eC(S[0],i,n),eC(S[1],s,n)),v}}function e7(t,e,r,n){var a=[n,(n+e)/2,e],i=[t,(t+r)/2,r];return e8.map(function(t){var e=t[0],r=t[1],n=t[2],o=a[e+1],s=i[r+1];return{vertical:Math.abs(r),horizontal:Math.abs(e),direction:n,pos:[o,s]}})}function e9(t,e,r,n,a){var o,s,u,l,c,f,d,p=a;if(!p){var g=ex(t),h=g.clipPath;p="none"!==h?h:g.clip}if(p&&"none"!==p&&"auto"!==p||(p=n)){var v=(0,i.ft)(p),m=v.prefix,x=void 0===m?p:m,E=v.value,S=void 0===E?"":E,R="circle"===x,M=" ";if("polygon"===x){var D=(0,i.cM)(S||"0% 0%, 100% 0%, 100% 100%, 0% 100%");M=",";var y=D.map(function(t){var n=t.split(" "),a=n[0],o=n[1];return{vertical:1,horizontal:1,pos:[(0,i.jc)(a,e),(0,i.jc)(o,r)]}});return{type:x,clipText:p,poses:y,splitter:M}}if(R||"ellipse"===x){var C="",B="",G=0,z=0,D=(0,i.Z3)(S);if(R){var w="";w=void 0===(o=D[0])?"50%":o,C=void 0===(s=D[2])?"50%":s,B=void 0===(u=D[3])?"50%":u,z=G=(0,i.jc)(w,Math.sqrt((e*e+r*r)/2))}else{var O="",P="";O=void 0===(l=D[0])?"50%":l,P=void 0===(c=D[1])?"50%":c,C=void 0===(f=D[3])?"50%":f,B=void 0===(d=D[4])?"50%":d,G=(0,i.jc)(O,e),z=(0,i.jc)(P,r)}var T=[(0,i.jc)(C,e),(0,i.jc)(B,r)],y=b([{vertical:1,horizontal:1,pos:T,direction:"nesw"}],e5.slice(0,R?1:2).map(function(t){return{vertical:Math.abs(t[1]),horizontal:t[0],direction:t[2],sub:!0,pos:[T[0]+t[0]*G,T[1]+t[1]*z]}}),!0);return{type:x,clipText:p,radiusX:G,radiusY:z,left:T[0]-G,top:T[1]-z,poses:y,splitter:M}}if("inset"===x){var D=(0,i.Z3)(S||"0 0 0 0"),k=D.indexOf("round"),I=(k>-1?D.slice(0,k):D).length,A=D.slice(I+1),F=D.slice(0,I),Y=F[0],N=F[1],q=void 0===N?Y:N,W=F[2],_=void 0===W?Y:W,H=F[3],j=void 0===H?q:H,X=[Y,_].map(function(t){return(0,i.jc)(t,r)}),L=X[0],V=X[1],U=[j,q].map(function(t){return(0,i.jc)(t,e)}),K=U[0],$=U[1],Z=e-$,Q=r-V,J=e1(A,Z-K,Q-L,K,L),y=b(b([],e7(L,Z,Q,K),!0),J,!0);return{type:"inset",clipText:p,poses:y,top:L,left:K,right:Z,bottom:Q,radius:A,splitter:M}}if("rect"===x){var D=(0,i.cM)(S||"0px, ".concat(e,"px, ").concat(r,"px, 0px"));M=",";var tt=D.map(function(t){return(0,i.hg)(t).value}),L=tt[0],$=tt[1],V=tt[2],K=tt[3],y=e7(L,$,V,K);return{type:"rect",clipText:p,poses:y,top:L,right:$,bottom:V,left:K,values:D,splitter:M}}}}var rt={name:"clippable",props:{clippable:Boolean,defaultClipPath:String,customClipPath:String,clipRelative:Boolean,clipArea:Boolean,dragWithClip:Boolean,clipTargetBounds:Boolean,clipVerticalGuidelines:Array,clipHorizontalGuidelines:Array,clipSnapThreshold:Boolean},events:{onClipStart:"clipStart",onClip:"clip",onClipEnd:"clipEnd"},css:[".control.clip-control {\n    background: #6d6;\n    cursor: pointer;\n}\n.control.clip-control.clip-radius {\n    background: #d66;\n}\n.line.clip-line {\n    background: #6e6;\n    cursor: move;\n    z-index: 1;\n}\n.clip-area {\n    position: absolute;\n    top: 0;\n    left: 0;\n}\n.clip-ellipse {\n    position: absolute;\n    cursor: move;\n    border: 1px solid #6d6;\n    border: var(--zoompx) solid #6d6;\n    border-radius: 50%;\n    transform-origin: 0px 0px;\n}",":host {\n    --bounds-color: #d66;\n}",".guideline {\n    pointer-events: none;\n    z-index: 2;\n}",".line.guideline.bounds {\n    background: #d66;\n    background: var(--bounds-color);\n}"],render:function(t,e){var r=t.props,n=r.customClipPath,a=r.defaultClipPath,s=r.clipArea,u=r.zoom,l=t.state,c=l.target,f=l.width,d=l.height,p=l.allMatrix,g=l.is3d,h=l.left,v=l.top,m=l.pos1,x=l.pos2,E=l.pos3,S=l.pos4,R=l.clipPathState,M=l.snapBoundInfos,D=l.rotation;if(!c)return[];var y=e9(c,f,d,a||"inset",R||n);if(!y)return[];var C=g?4:3,B=y.type,G=y.poses.map(function(t){var e=t7(p,t.pos,C);return[e[0]-h,e[1]-v]}),z=[],w=[],O="rect"===B,P="inset"===B,T="polygon"===B;if(O||P||T){var k=P?G.slice(0,8):G;w=k.map(function(t,r){var n=0===r?k[k.length-1]:k[r-1],a=(0,i.Mu)(n,t),o=en(n,t);return e.createElement("div",{key:"clipLine".concat(r),className:tQ("line","clip-line","snap-control"),"data-clip-index":r,style:{width:"".concat(o,"px"),transform:"translate(".concat(n[0],"px, ").concat(n[1],"px) rotate(").concat(a,"rad) scaleY(").concat(u,")")}})})}if(z=G.map(function(t,r){return e.createElement("div",{key:"clipControl".concat(r),className:tQ("control","clip-control","snap-control"),"data-clip-index":r,style:{transform:"translate(".concat(t[0],"px, ").concat(t[1],"px) rotate(").concat(D,"rad) scale(").concat(u,")")}})}),P&&z.push.apply(z,G.slice(8).map(function(t,r){return e.createElement("div",{key:"clipRadiusControl".concat(r),className:tQ("control","clip-control","clip-radius","snap-control"),"data-clip-index":8+r,style:{transform:"translate(".concat(t[0],"px, ").concat(t[1],"px) rotate(").concat(D,"rad) scale(").concat(u,")")}})})),"circle"===B||"ellipse"===B){var A=y.left,F=y.top,Y=y.radiusX,N=y.radiusY,q=(0,o.Rd)(t7(p,[A,F],C),t7(p,[0,0],C)),W=q[0],_=q[1],H="none";if(!s){for(var j=Math.max(10,Y/5,N/5),X=[],L=0;L<=j;++L){var V=2*Math.PI/j*L;X.push([Y+(Y-u)*Math.cos(V),N+(N-u)*Math.sin(V)])}X.push([Y,-2]),X.push([-2,-2]),X.push([-2,2*N+2]),X.push([2*Y+2,2*N+2]),X.push([2*Y+2,-2]),X.push([Y,-2]),H="polygon(".concat(X.map(function(t){return"".concat(t[0],"px ").concat(t[1],"px")}).join(", "),")")}z.push(e.createElement("div",{key:"clipEllipse",className:tQ("clip-ellipse","snap-control"),style:{width:"".concat(2*Y,"px"),height:"".concat(2*N,"px"),clipPath:H,transform:"translate(".concat(-h+W,"px, ").concat(-v+_,"px) ").concat(t8(p))}}))}if(s){var U=et(b([m,x,E,S],G,!0)),K=U.width,$=U.height,Z=U.left,Q=U.top;if(T||O||P){var X=P?G.slice(0,8):G;z.push(e.createElement("div",{key:"clipArea",className:tQ("clip-area","snap-control"),style:{width:"".concat(K,"px"),height:"".concat($,"px"),transform:"translate(".concat(Z,"px, ").concat(Q,"px)"),clipPath:"polygon(".concat(X.map(function(t){return"".concat(t[0]-Z,"px ").concat(t[1]-Q,"px")}).join(", "),")")}}))}}return M&&["vertical","horizontal"].forEach(function(t){var r=M[t],n="horizontal"===t;r.isSnap&&w.push.apply(w,r.snap.posInfos.map(function(r,a){var i=r.pos;return I(e,"",(0,o.Rd)(t7(p,n?[0,i]:[i,0],C),[h,v]),(0,o.Rd)(t7(p,n?[f,i]:[i,d],C),[h,v]),u,"clip".concat(t,"snap").concat(a),"guideline")})),r.isBound&&w.push.apply(w,r.bounds.map(function(r,a){var i=r.pos;return I(e,"",(0,o.Rd)(t7(p,n?[0,i]:[i,0],C),[h,v]),(0,o.Rd)(t7(p,n?[f,i]:[i,d],C),[h,v]),u,"clip".concat(t,"bounds").concat(a),"guideline","bounds","bold")}))}),b(b([],z,!0),w,!0)},dragControlCondition:function(t,e){return e.inputEvent&&(e.inputEvent.target.getAttribute("class")||"").indexOf("clip")>-1},dragStart:function(t,e){var r=t.props.dragWithClip;return void 0!==r&&!r&&this.dragControlStart(t,e)},drag:function(t,e){return this.dragControl(t,m(m({},e),{isDragTarget:!0}))},dragEnd:function(t,e){return this.dragControlEnd(t,e)},dragControlStart:function(t,e){var r=t.state,n=t.props,a=n.defaultClipPath,i=n.customClipPath,o=r.target,s=r.width,u=r.height,l=e.inputEvent?e.inputEvent.target:null,c=l&&l.getAttribute("class")||"",f=e.datas,d=e9(o,s,u,a||"inset",i);if(!d)return!1;var p=d.clipText;return!1===eb(t,"onClipStart",eh(t,e,{clipType:d.type,clipStyle:p,poses:d.poses.map(function(t){return t.pos})}))?(f.isClipStart=!1,!1):(f.isControl=c&&c.indexOf("clip-control")>-1,f.isLine=c.indexOf("clip-line")>-1,f.isArea=c.indexOf("clip-area")>-1||c.indexOf("clip-ellipse")>-1,f.index=l?parseInt(l.getAttribute("data-clip-index"),10):-1,f.clipPath=d,f.isClipStart=!0,r.clipPathState=p,tP(t,e),!0)},dragControl:function(t,e){var r,n=e.datas,a=e.originalDatas,s=e.isDragTarget;if(!n.isClipStart)return!1;var u=n.isControl,l=n.isLine,c=n.isArea,f=n.index,d=n.clipPath;if(!d)return!1;var p=0,g=0,h=a.draggable,v=tF(e);s&&h?(p=(r=h.prevBeforeDist)[0],g=r[1]):(p=v[0],g=v[1]);var m=[p,g],x=t.props,E=t.state,S=E.width,R=E.height,M=d.type,D=d.poses,y=d.splitter,C=D.map(function(t){return t.pos});c||u||l||(p=-p,g=-g);var B=!u||"nesw"===D[f].direction,G="inset"===M||"rect"===M,z=D.map(function(){return[0,0]});if(u&&!B){var w=D[f],O=w.horizontal,P=w.vertical;z=function(t,e,r,n){var a=t[e],i=a.direction,o=a.sub,s=t.map(function(){return[0,0]}),u=i?i.split(""):[];if(n&&e<8){var l=u.filter(function(t){return"w"===t||"e"===t})[0],c=u.filter(function(t){return"n"===t||"s"===t})[0];s[e]=r,t.forEach(function(t,e){var n=t.direction;n&&(n.indexOf(l)>-1&&(s[e][0]=r[0]),n.indexOf(c)>-1&&(s[e][1]=r[1]))}),l&&(s[1][0]=r[0]/2,s[5][0]=r[0]/2),c&&(s[3][1]=r[1]/2,s[7][1]=r[1]/2)}else i&&!o?u.forEach(function(e){var n="n"===e||"s"===e;t.forEach(function(t,a){var i=t.direction,o=t.horizontal,u=t.vertical;i&&-1!==i.indexOf(e)&&(s[a]=[n||!o?0:r[0],n&&u?r[1]:0])})}):s[e]=r;return s}(D,f,[p*Math.abs(O),g*Math.abs(P)],G)}else B&&(z=C.map(function(){return[p,g]}));var T=C.map(function(t,e){return(0,o.tY)(t,z[e])}),k=b([],T,!0);E.snapBoundInfos=null;var I="circle"===d.type,A="ellipse"===d.type;if(I||A){var F=et(T),Y=Math.abs(F.bottom-F.top),N=Math.abs(A?F.right-F.left:Y),q=T[0][1]+Y,W=T[0][0]-N,_=T[0][0]+N;I&&(k.push([_,F.bottom]),z.push([1,0])),k.push([F.left,q]),z.push([0,1]),k.push([W,F.bottom]),z.push([1,0])}var H=K((x.clipHorizontalGuidelines||[]).map(function(t){return(0,i.jc)("".concat(t),R)}),(x.clipVerticalGuidelines||[]).map(function(t){return(0,i.jc)("".concat(t),S)}),S,R),j=[],X=[];if(I||A)j=[k[4][0],k[2][0]],X=[k[1][1],k[3][1]];else if(G){var L=[k[0],k[2],k[4],k[6]],V=[z[0],z[2],z[4],z[6]];j=L.filter(function(t,e){return V[e][0]}).map(function(t){return t[0]}),X=L.filter(function(t,e){return V[e][1]}).map(function(t){return t[1]})}else j=k.filter(function(t,e){return z[e][0]}).map(function(t){return t[0]}),X=k.filter(function(t,e){return z[e][1]}).map(function(t){return t[1]});for(var U=[0,0],$=0;$<2&&"break"!==function(t){var e=tM(H,x.clipTargetBounds&&{left:0,top:0,right:S,bottom:R},j,X,5),r=e.horizontal,n=e.vertical,a=r.offset,i=n.offset;if(r.isBound&&(U[1]+=a),n.isBound&&(U[0]+=i),!A&&!I||0!==z[0][0]||0!==z[0][1])return k.forEach(function(t,e){var r=z[e];r[0]&&(t[0]-=i),r[1]&&(t[1]-=a)}),"break";var o=et(T),s=o.bottom-o.top,u=A?o.right-o.left:s,l=n.isBound?Math.abs(i):0===n.snapIndex?-i:i,c=r.isBound?Math.abs(a):0===r.snapIndex?-a:a;u-=l,s-=c,I&&(u=s=tr(n,r)>0?s:u);var f=k[0];k[1][1]=f[1]-s,k[2][0]=f[0]+u,k[3][1]=f[1]+s,k[4][0]=f[0]-u}();++$);var Z=e6(t,d,T),Q="".concat(M,"(").concat(Z.join(y),")");if(E.clipPathState=Q,I||A)j=[k[4][0],k[2][0]],X=[k[1][1],k[3][1]];else if(G){var L=[k[0],k[2],k[4],k[6]];j=L.map(function(t){return t[0]}),X=L.map(function(t){return t[1]})}else j=k.map(function(t){return t[0]}),X=k.map(function(t){return t[1]});if(E.snapBoundInfos=tM(H,x.clipTargetBounds&&{left:0,top:0,right:S,bottom:R},j,X,1),h){var J=E.is3d,tt=E.allMatrix,te=U;s&&(te=[m[0]+U[0]-v[0],m[1]+U[1]-v[1]]),h.deltaOffset=(0,o.lw)(tt,[te[0],te[1],0,0],J?4:3)}return eb(t,"onClip",eh(t,e,{clipEventType:"changed",clipType:M,poses:T,clipStyle:Q,clipStyles:Z,distX:p,distY:g})),!0},dragControlEnd:function(t,e){this.unset(t);var r=e.isDrag,n=e.datas,a=e.isDouble,i=n.isLine,o=n.isClipStart,s=n.isControl;return!!o&&(eb(t,"onClipEnd",ev(t,e,{})),a&&(s?!function(t,e){var r=e.datas,n=r.clipPath,a=r.index,i=n.type,o=n.poses,s=n.splitter,u=o.map(function(t){return t.pos}),l=u.length;if("polygon"===i)o.splice(a,1),u.splice(a,1);else if("inset"!==i||a<8||(e2(o,u,a,8,l),l===o.length))return;var c=e6(t,n,u);eb(t,"onClip",eh(t,e,{clipEventType:"removed",clipType:i,poses:u,clipStyles:c,clipStyle:"".concat(i,"(").concat(c.join(s),")"),distX:0,distY:0}))}(t,e):i&&function(t,e){var r=tO(t,e),n=r[0],a=r[1],i=e.datas,o=i.clipPath,s=i.index,u=o.type,l=o.poses,c=o.splitter,f=l.map(function(t){return t.pos});if("polygon"===u)f.splice(s,0,[n,a]);else{if("inset"!==u)return;var d=eK.indexOf(s),p=e$.indexOf(s),g=l.length;if(e3(l,f,8,d,p,n,a,f[4][0],f[4][1],f[0][0],f[0][1]),g===l.length)return}var h=e6(t,o,f);eb(t,"onClip",eh(t,e,{clipEventType:"added",clipType:u,poses:f,clipStyles:h,clipStyle:"".concat(u,"(").concat(h.join(c),")"),distX:0,distY:0}))}(t,e)),a||r)},unset:function(t){t.state.clipPathState="",t.state.snapBoundInfos=null}};function re(t,e,r,n,a){void 0===n&&(n=[0,0]);var o,s=[];if(a)o=a;else{var u=ex(t);o=u&&u.borderRadius||""}return e1(o&&(a||"0px"!==o)?(0,i.Z3)(o):[],e,r,0,0,n)}function rr(t,e,r,n,a,i){var o=t.state,s=o.width,u=o.height,l=eJ(i,a,t.props.roundRelative,s,u),c=l.raws,f=l.styles,d=e4(a,c),p=d.horizontals,g=d.verticals,h=f.join(" ");o.borderRadiusState=h,eb(t,"onRound",eh(t,e,{horizontals:p,verticals:g,borderRadius:h,width:s,height:u,delta:n,dist:r}))}var rn={isPinch:!0,name:"beforeRenderable",props:{},events:{onBeforeRenderStart:"beforeRenderStart",onBeforeRender:"beforeRender",onBeforeRenderEnd:"beforeRenderEnd",onBeforeRenderGroupStart:"beforeRenderGroupStart",onBeforeRenderGroup:"beforeRenderGroup",onBeforeRenderGroupEnd:"beforeRenderGroupEnd"},dragRelation:"weak",setTransform:function(t,e){var r=t.state,n=r.is3d,a=r.target,s=r.targetMatrix,u=null==a?void 0:a.style.transform,l=n?"matrix3d(".concat(s.join(","),")"):"matrix(".concat((0,o.ZB)(s,!0),")"),c=u&&"none"!==u?u:l;e.datas.startTransforms=c===(n?"matrix3d(".concat((0,o.k8)(4)):"matrix(".concat((0,o.k8)(3),")"))||"matrix(1,0,0,1,0,0)"===c?[]:(0,i.Z3)(c)},resetTransform:function(t){t.datas.nextTransforms=t.datas.startTransforms,t.datas.nextTransformAppendedIndexes=[]},fillDragStartParams:function(t,e){return eh(t,e,{setTransform:function(t){e.datas.startTransforms=(0,i.cy)(t)?t:(0,i.Z3)(t)},isPinch:!!e.isPinch})},fillDragParams:function(t,e){return eh(t,e,{isPinch:!!e.isPinch})},dragStart:function(t,e){this.setTransform(t,e),eb(t,"onBeforeRenderStart",this.fillDragStartParams(t,e))},drag:function(t,e){this.resetTransform(e),eb(t,"onBeforeRender",eh(t,e,{isPinch:!!e.isPinch}))},dragEnd:function(t,e){eb(t,"onBeforeRenderEnd",eh(t,e,{isPinch:!!e.isPinch,isDrag:e.isDrag}))},dragGroupStart:function(t,e){var r=this;this.dragStart(t,e);var n=O(t,"beforeRenderable",e),a=t.moveables,i=n.map(function(t,e){var n=a[e];return r.setTransform(n,t),r.fillDragStartParams(n,t)});eb(t,"onBeforeRenderGroupStart",eh(t,e,{isPinch:!!e.isPinch,targets:t.props.targets,setTransform:function(){},events:i}))},dragGroup:function(t,e){var r=this;this.drag(t,e);var n=O(t,"beforeRenderable",e),a=t.moveables,i=n.map(function(t,e){var n=a[e];return r.resetTransform(t),r.fillDragParams(n,t)});eb(t,"onBeforeRenderGroup",eh(t,e,{isPinch:!!e.isPinch,targets:t.props.targets,events:i}))},dragGroupEnd:function(t,e){this.dragEnd(t,e),eb(t,"onBeforeRenderGroupEnd",eh(t,e,{isPinch:!!e.isPinch,isDrag:e.isDrag,targets:t.props.targets}))},dragControlStart:function(t,e){return this.dragStart(t,e)},dragControl:function(t,e){return this.drag(t,e)},dragControlEnd:function(t,e){return this.dragEnd(t,e)},dragGroupControlStart:function(t,e){return this.dragGroupStart(t,e)},dragGroupControl:function(t,e){return this.dragGroup(t,e)},dragGroupControlEnd:function(t,e){return this.dragGroupEnd(t,e)}},ra={name:"renderable",props:{},events:{onRenderStart:"renderStart",onRender:"render",onRenderEnd:"renderEnd",onRenderGroupStart:"renderGroupStart",onRenderGroup:"renderGroup",onRenderGroupEnd:"renderGroupEnd"},dragRelation:"weak",dragStart:function(t,e){eb(t,"onRenderStart",eh(t,e,{isPinch:!!e.isPinch}))},drag:function(t,e){eb(t,"onRender",this.fillDragParams(t,e))},dragAfter:function(t,e){if(e.resultCount)return this.drag(t,e)},dragEnd:function(t,e){eb(t,"onRenderEnd",this.fillDragEndParams(t,e))},dragGroupStart:function(t,e){eb(t,"onRenderGroupStart",eh(t,e,{isPinch:!!e.isPinch,targets:t.props.targets}))},dragGroup:function(t,e){var r=this,n=O(t,"beforeRenderable",e),a=t.moveables,i=n.map(function(t,e){var n=a[e];return r.fillDragParams(n,t)});eb(t,"onRenderGroup",eh(t,e,{isPinch:!!e.isPinch,targets:t.props.targets,transform:tL(e),events:i}))},dragGroupEnd:function(t,e){var r=this,n=O(t,"beforeRenderable",e),a=t.moveables,i=n.map(function(t,e){var n=a[e];return r.fillDragEndParams(n,t)});eb(t,"onRenderGroupEnd",eh(t,e,{isPinch:!!e.isPinch,isDrag:e.isDrag,targets:t.props.targets,events:i}))},dragControlStart:function(t,e){return this.dragStart(t,e)},dragControl:function(t,e){return this.drag(t,e)},dragControlAfter:function(t,e){return this.dragAfter(t,e)},dragControlEnd:function(t,e){return this.dragEnd(t,e)},dragGroupControlStart:function(t,e){return this.dragGroupStart(t,e)},dragGroupControl:function(t,e){return this.dragGroup(t,e)},dragGroupControlEnd:function(t,e){return this.dragGroupEnd(t,e)},fillDragParams:function(t,e){return eh(t,e,{isPinch:!!e.isPinch,transform:tL(e)})},fillDragEndParams:function(t,e){return eh(t,e,{isPinch:!!e.isPinch,isDrag:e.isDrag})}};function ri(t,e,r,n,a,i,o){var s,u="Start"===a,l=t.state.target,c=i.isRequest;if(!l||u&&n.indexOf("Control")>-1&&!c&&t.areaElement===i.inputEvent.target)return!1;var f="".concat(r).concat(n).concat(a),d="".concat(r).concat(n,"Condition"),p="End"===a,g="After"===a,h=u&&(!t.targetGesto||!t.controlGesto||!t.targetGesto.isFlag()||!t.controlGesto.isFlag());h&&t.updateRect(a,!0,!1),""!==a||c||eD(t.state,i);var v=b([],t[e],!0);if(c){var x=i.requestAble;v.some(function(t){return t.name===x})||v.push.apply(v,t.props.ables.filter(function(t){return t.name===x}))}if(!v.length)return!1;var E=b(b([rn],v,!0),[ra],!1).filter(function(t){return t[f]}),S=i.datas;h&&E.forEach(function(e){e.unset&&e.unset(t)});var R=i.inputEvent;p&&R&&(s=document.elementFromPoint(i.clientX,i.clientY)||R.target);var M=0,D=E.filter(function(e){var r=e.name,n=S[r]||(S[r]={});if(u&&(n.isEventStart=!e[d]||e[d](t,i)),n.isEventStart){var a=e[f](t,m(m({},i),{resultCount:M,datas:n,originalDatas:S,inputTarget:s}));return t._emitter.off(),u&&!1===a&&(n.isEventStart=!1),M+=+!!a,a}return!1}).length,y=!1;return u&&E.length&&!D&&(y=0===E.filter(function(t){var e=S[t.name];return!!e.isEventStart&&("strong"!==t.dragRelation||(e.isEventStart=!1,!1))}).length),(p||y)&&(t.state.gesto=null,t.moveables&&t.moveables.forEach(function(t){t.state.gesto=null})),h&&y&&E.forEach(function(e){e.unset&&e.unset(t)}),u&&!c&&D&&(null==i||i.preventDefault()),!t.isUnmounted&&!y&&((!u&&D&&!o||p)&&(t.updateRect(p?a:"",!0,!1),t.forceUpdate()),u||p||g||!D||o||ri(t,e,r,n,a+"After",i),!0)}function ro(t,e,r){var n=t.controlBox.getElement(),a=[];a.push(n),(!t.props.dragArea||t.props.dragTarget)&&a.push(e);var o=function(e){var r=e.inputEvent.target;return r===t.areaElement||!t.isMoveableElement(r)&&!t.controlBox.getElement().contains(r)||(0,i.nB)(r,"moveable-area")||(0,i.nB)(r,"moveable-padding")};return rs(t,a,"targetAbles",r,{dragStart:o,pinchStart:o})}function rs(t,e,r,n,a){void 0===a&&(a={});var i=t.props,o=i.pinchOutside,s={preventDefault:!1,container:window,pinchThreshold:i.pinchThreshold,pinchOutside:o},u=new d.A(e,s);return["drag","pinch"].forEach(function(e){["Start","","End"].forEach(function(i){u.on("".concat(e).concat(i),function(o){var s=o.eventType;if(a[s]&&!a[s](o))return void o.stop();ri(t,r,e,n,i,o)||o.stop()})})}),u}var ru=function(){function t(t,e,r){var n=this;this.target=t,this.moveable=e,this.eventName=r,this.ables=[],this._onEvent=function(t){var e=n.eventName,r=n.moveable;r.state.disableNativeEvent||n.ables.forEach(function(n){n[e](r,{inputEvent:t})})},t.addEventListener(r.toLowerCase(),this._onEvent)}var e=t.prototype;return e.setAbles=function(t){this.ables=t},e.destroy=function(){this.target.removeEventListener(this.eventName.toLowerCase(),this._onEvent),this.target=null,this.moveable=null},t}(),rl=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.state=m({container:null,gesto:null,renderPoses:[[0,0],[0,0],[0,0],[0,0]],disableNativeEvent:!1},eu(null)),e.enabledAbles=[],e.targetAbles=[],e.controlAbles=[],e.rotation=0,e.scale=[1,1],e.isUnmounted=!1,e.events={mouseEnter:null,mouseLeave:null},e._emitter=new g.A,e._prevTarget=null,e._prevDragArea=!1,e._observer=null,e._observerId=0,e.onPreventClick=function(t){t.stopPropagation(),(0,i.Nn)(window,"click",e.onPreventClick,!0)},e.checkUpdateRect=function(){if(!e.isDragging()){var t=e.props.parentMoveable;if(t)return void t.checkUpdateRect();(0,i.uU)(e._observerId),e._observerId=(0,i.xi)(function(){e.isDragging()||e.updateRect()})}},e}v(e,t);var r=e.prototype;return r.render=function(){var t=this.props,e=this.state,r=t.parentPosition,n=t.className,i=t.target,o=t.zoom,s=t.cspNonce,u=t.translateZ,l=t.cssStyled,c=t.portalContainer;this.checkUpdate(),this.updateRenderPoses();var d=r||{left:0,top:0},p=d.left,g=d.top,h=e.left,v=e.top,b=e.target,x=e.direction,E=t.targets,S=(E&&E.length||i)&&b,R=this.isDragging(),M={};return this.getEnabledAbles().forEach(function(t){M["data-able-".concat(t.name.toLowerCase())]=!0}),(0,f.createElement)(l,m({cspNonce:s,ref:(0,a.KR)(this,"controlBox"),className:"".concat(tQ("control-box",-1===x?"reverse":"",R?"dragging":"")," ").concat(n)},M,{onClick:this.onPreventClick,portalContainer:c,style:{position:"absolute",display:S?"block":"none",transform:"translate3d(".concat(h-p,"px, ").concat(v-g,"px, ").concat(u,")"),"--zoom":o,"--zoompx":"".concat(o,"px")}}),this.renderAbles(),this._renderLines())},r.componentDidMount=function(){this.isUnmounted=!1,this.controlBox.getElement();var t=this.props,e=t.parentMoveable,r=t.container,n=t.wrapperMoveable;this._updateTargets(),this._updateNativeEvents(),this._updateEvents(),r||e||n||this.updateRect("",!1,!0),this.updateCheckInput(),this._updateObserver(this.props)},r.componentDidUpdate=function(t){this._updateNativeEvents(),this._updateEvents(),this._updateTargets(),this.updateCheckInput(),this._updateObserver(t)},r.componentWillUnmount=function(){this.isUnmounted=!0,this._emitter.off(),eg(this,"targetGesto"),eg(this,"controlGesto");var t=this.events;for(var e in t){var r=t[e];r&&r.destroy()}},r.getContainer=function(){var t=this.props,e=t.parentMoveable,r=t.wrapperMoveable;return t.container||r&&r.getContainer()||e&&e.getContainer()||this.controlBox.getElement().parentElement},r.isMoveableElement=function(t){return t&&(t.getAttribute("class")||"").indexOf(R)>-1},r.dragStart=function(t){var e=this.targetGesto;return e&&!e.isFlag()&&e.triggerDragStart(t),this},r.hitTest=function(t){var e,r=this.state,n=r.target,a=r.pos1,i=r.pos2,o=r.pos3,s=r.pos4,u=r.targetClientRect;if(!n)return 0;if(t instanceof Element){var c=t.getBoundingClientRect();e={left:c.left,top:c.top,width:c.width,height:c.height}}else e=m({width:0,height:0},t);var f=e.left,d=e.top,p=e.width,g=e.height,h=(0,l.FV)([a,i,s,o],u),v=(0,l.tT)(h,[[f,d],[f+p,d],[f+p,d+g],[f,d+g]]),b=(0,l.B9)(h);return v&&b?Math.min(100,v/b*100):0},r.isInside=function(t,e){var r=this.state,n=r.target,a=r.pos1,i=r.pos2,o=r.pos3,s=r.pos4,u=r.targetClientRect;return!!n&&(0,l.FK)([t,e],(0,l.FV)([a,i,s,o],u))},r.updateRect=function(t,e,r){void 0===r&&(r=!0);var n=this.props,a=n.parentMoveable,i=this.state.target||this.props.target,o=this.getContainer(),s=a?a.props.rootContainer:n.rootContainer;this.updateState(eu(this.controlBox&&this.controlBox.getElement(),i,o,o,s||o),!a&&r)},r.isDragging=function(){return!!this.targetGesto&&this.targetGesto.isFlag()||!!this.controlGesto&&this.controlGesto.isFlag()},r.updateTarget=function(t){this.updateRect(t,!0)},r.getRect=function(){var t=this.state,e=ep(this.state),r=e[0],n=e[1],a=e[2],i=e[3],s=et(e),u=t.width,l=t.height,c=s.width,f=s.height,d=s.left,p=s.top,g=[t.left,t.top],h=(0,o.tY)(g,t.origin);return{width:c,height:f,left:d,top:p,pos1:r,pos2:n,pos3:a,pos4:i,offsetWidth:u,offsetHeight:l,beforeOrigin:(0,o.tY)(g,t.beforeOrigin),origin:h,transformOrigin:t.transformOrigin,rotation:this.getRotation()}},r.getManager=function(){return this},r.getRotation=function(){var t,e=this.state,r=e.pos1,n=e.pos2,a=e.direction;return t=(0,i.Mu)(r,n)/Math.PI*180,t=(t=a>=0?t:180-t)>=0?t:360+t},r.request=function(t,e,r){void 0===e&&(e={});var n=this.props,a=n.ables,i=n.groupable,o=a.filter(function(e){return e.name===t})[0];if(this.isDragging()||!o||!o.request)return{request:function(){return this},requestEnd:function(){return this}};var s=this,u=o.request(this),l=r||e.isInstant,c=u.isControl?"controlAbles":"targetAbles",f="".concat(i?"Group":"").concat(u.isControl?"Control":""),d={request:function(e){return ri(s,c,"drag",f,"",m(m({},u.request(e)),{requestAble:t,isRequest:!0}),l),this},requestEnd:function(){return ri(s,c,"drag",f,"End",m(m({},u.requestEnd()),{requestAble:t,isRequest:!0}),l),this}};return ri(s,c,"drag",f,"Start",m(m({},u.requestStart(e)),{requestAble:t,isRequest:!0}),l),l?d.request(e).requestEnd():d},r.destroy=function(){this.componentWillUnmount()},r.updateRenderPoses=function(){var t=this.state,e=this.props,r=t.originalBeforeOrigin,n=t.transformOrigin,a=t.allMatrix,i=t.is3d,s=t.pos1,u=t.pos2,l=t.pos3,c=t.pos4,f=t.left,d=t.top,p=e.padding||{},g=p.left,h=void 0===g?0:g,v=p.top,m=void 0===v?0:v,b=p.bottom,x=void 0===b?0:b,E=p.right,S=void 0===E?0:E,R=i?4:3,M=e.groupable?r:(0,o.tY)(r,[f,d]);t.renderPoses=[(0,o.tY)(s,ey(a,[-h,-m],n,M,R)),(0,o.tY)(u,ey(a,[S,-m],n,M,R)),(0,o.tY)(l,ey(a,[-h,x],n,M,R)),(0,o.tY)(c,ey(a,[S,x],n,M,R))]},r.checkUpdate=function(){var t=this.props,e=t.target,r=t.container,n=t.parentMoveable,a=this.state,i=a.target,o=a.container;if((i||e)&&(this.updateAbles(),!eS(i,e)||!eS(o,r))){var s=r||this.controlBox;s&&this.unsetAbles(),this.updateState({target:e,container:r}),!n&&s&&this.updateRect("End",!1,!1)}},r.triggerEvent=function(t,e){this._emitter.trigger(t,e);var r=this.props[t];return r&&r(e)},r.useCSS=function(t,e){var r=this.props.customStyledMap,n=t+e;return r[n]||(r[n]=(0,p.A)(t,e)),r[n]},r.unsetAbles=function(){var t=this;this.targetAbles.forEach(function(e){e.unset&&e.unset(t)})},r.updateAbles=function(t,e){void 0===t&&(t=this.props.ables),void 0===e&&(e="");var r=this.props,n=r.triggerAblesSimultaneously,a=t.filter(function(t){return t&&(t.always&&!1!==r[t.name]||r[t.name])}),i="drag".concat(e,"Start"),o="pinch".concat(e,"Start"),s="drag".concat(e,"ControlStart"),u=eE(a,[i,o],n),l=eE(a,[s],n);this.enabledAbles=a,this.targetAbles=u,this.controlAbles=l},r.updateState=function(t,e){if(e)this.isUnmounted||this.setState(t);else{var r=this.state;for(var n in t)r[n]=t[n]}},r.getEnabledAbles=function(){var t=this.props;return t.ables.filter(function(e){return e&&t[e.name]})},r.renderAbles=function(){var t,e,r,n,a=this,i=this.props.triggerAblesSimultaneously,o={createElement:f.createElement};return(t=eE(this.getEnabledAbles(),["render"],i).map(function(t){return(0,t.render)(a,o)||[]}).reduce(function(t,e){return t.concat(e)},[]).filter(function(t){return t}),e=function(t){return t.key},r=[],n={},t.forEach(function(a,i){var o=e(a,i,t),s=n[o];s||(s=[],n[o]=s,r.push(s)),s.push(a)}),r).map(function(t){return t[0]})},r.updateCheckInput=function(){this.targetGesto&&(this.targetGesto.options.checkInput=this.props.checkInput)},r._updateObserver=function(t){var e,r=this.props,n=r.target;if(!window.ResizeObserver||!n||!r.useResizeObserver){null==(e=this._observer)||e.disconnect();return}if(t.target!==n||!this._observer){var a=new ResizeObserver(this.checkUpdateRect);a.observe(n,{box:"border-box"}),this._observer=a}},r._updateEvents=function(){var t=this.controlBox.getElement(),e=this.targetAbles.length,r=this.controlAbles.length,n=this.props,a=n.dragTarget||n.target;(!e&&this.targetGesto||this._isTargetChanged(!0))&&(eg(this,"targetGesto"),this.updateState({gesto:null})),r||eg(this,"controlGesto"),a&&e&&!this.targetGesto&&(this.targetGesto=ro(this,a,"")),!this.controlGesto&&r&&(this.controlGesto=rs(this,t,"controlAbles","Control"))},r._updateTargets=function(){var t=this.props;this._prevTarget=t.dragTarget||t.target,this._prevDragArea=t.dragArea},r._renderLines=function(){var t=this.props,e=t.edge,r=t.zoom;if(t.hideDefaultLines)return[];var n=this.state.renderPoses,a={createElement:f.createElement};return[I(a,e?"n":"",n[0],n[1],r,0),I(a,e?"e":"",n[1],n[3],r,1),I(a,e?"w":"",n[0],n[2],r,2),I(a,e?"s":"",n[2],n[3],r,3)]},r._isTargetChanged=function(t){var e=this.props,r=e.dragTarget||e.target,n=this._prevTarget,a=this._prevDragArea,i=e.dragArea;return!i&&n!==r||(t||i)&&a!==i},r._updateNativeEvents=function(){var t=this,e=this.props.dragArea?this.areaElement:this.state.target,r=this.events,n=(0,i.xD)(r);if(this._isTargetChanged())for(var a in r){var o=r[a];o&&o.destroy(),r[a]=null}if(e){var s=this.enabledAbles;n.forEach(function(n){var a=eE(s,[n]),i=a.length>0,o=r[n];if(!i){o&&(o.destroy(),r[n]=null);return}o||(o=new ru(e,t,n),r[n]=o),o.setAbles(a)})}},e.defaultProps={target:null,dragTarget:null,container:null,rootContainer:null,origin:!0,edge:!1,parentMoveable:null,wrapperMoveable:null,parentPosition:null,portalContainer:null,useResizeObserver:!1,ables:[],pinchThreshold:20,dragArea:!1,passDragArea:!1,transformOrigin:"",className:"",zoom:1,triggerAblesSimultaneously:!1,padding:{},pinchOutside:!0,checkInput:!1,groupable:!1,hideDefaultLines:!1,cspNonce:"",translateZ:0,cssStyled:null,customStyledMap:{},props:{}},e}(f.PureComponent),rc={name:"groupable",props:{defaultGroupRotate:Number,defaultGroupOrigin:String,groupable:Boolean},events:{},render:function(t,e){var r=t.props.targets||[];t.moveables=[];var n=t.state,i={left:n.left,top:n.top},o=t.props;return r.map(function(r,n){return e.createElement(rl,{key:"moveable"+n,ref:(0,a.DY)(t,"moveables",n),target:r,origin:!1,cssStyled:o.cssStyled,customStyledMap:o.customStyledMap,useResizeObserver:o.useResizeObserver,parentMoveable:t,parentPosition:i})})}},rf=x("clickable",{props:{clickable:Boolean},events:{onClick:"click",onClickGroup:"clickGroup"},always:!0,dragRelation:"weak",dragStart:function(t,e){e.isRequest||(0,i.W2)(window,"click",t.onPreventClick,!0)},dragControlStart:function(t,e){this.dragStart(t,e)},dragGroupStart:function(t,e){this.dragStart(t,e),e.datas.inputTarget=e.inputEvent&&e.inputEvent.target},dragEnd:function(t,e){this.endEvent(t);var r=t.state.target,n=e.inputEvent,a=e.inputTarget,i=!t.isMoveableElement(a)&&t.controlBox.getElement().contains(a);if((!e.isDrag||i)&&this.unset(t),!(!n||!a||e.isDrag||t.isMoveableElement(a))&&!i){var o=r.contains(a);eb(t,"onClick",eh(t,e,{isDouble:e.isDouble,inputTarget:a,isTarget:r===a,containsTarget:o}))}},dragGroupEnd:function(t,e){this.endEvent(t);var r=e.inputEvent,n=e.inputTarget;if(!(!r||!n||e.isDrag||t.isMoveableElement(n))&&e.datas.inputTarget!==n){var a=t.props.targets,o=a.indexOf(n),s=o>-1,u=!1;-1===o&&(u=(o=(0,i.SL)(a,function(t){return t.contains(n)}))>-1),eb(t,"onClickGroup",eh(t,e,{isDouble:e.isDouble,targets:a,inputTarget:n,targetIndex:o,isTarget:s,containsTarget:u}))}},dragControlEnd:function(t,e){this.dragEnd(t,e)},dragGroupControlEnd:function(t,e){this.dragEnd(t,e)},endEvent:function(t){var e=this;(0,i.xi)(function(){e.unset(t)})},unset:function(t){(0,i.Nn)(window,"click",t.onPreventClick,!0)}});function rd(t){var e=t.originalDatas.draggable;return e||(t.originalDatas.draggable={},e=t.originalDatas.draggable),m(m({},t),{datas:e})}var rp=x("edgeDraggable",{dragControlCondition:function(t,e){if(!t.props.edgeDraggable||!e.inputEvent)return!1;var r=e.inputEvent.target;return(0,i.nB)(r,tQ("direction"))&&(0,i.nB)(r,tQ("line"))},dragControlStart:function(t,e){return tw.dragStart(t,rd(e))},dragControl:function(t,e){return tw.drag(t,rd(e))},dragControlEnd:function(t,e){return tw.dragEnd(t,rd(e))},dragGroupControlCondition:function(t,e){if(!t.props.edgeDraggable||!e.inputEvent)return!1;var r=e.inputEvent.target;return(0,i.nB)(r,tQ("direction"))&&(0,i.nB)(r,tQ("line"))},dragGroupControlStart:function(t,e){return tw.dragGroupStart(t,rd(e))},dragGroupControl:function(t,e){return tw.dragGroup(t,rd(e))},dragGroupControlEnd:function(t,e){return tw.dragGroupEnd(t,rd(e))},unset:function(t){return tw.unset(t)}}),rg={name:"individualGroupable",props:{individualGroupable:Boolean},events:{}},rh=[rn,eX,{name:"snappable",dragRelation:"strong",props:{snappable:[Boolean,Array],snapContainer:Object,snapDirections:[Boolean,Object],elementSnapDirections:[Boolean,Object],snapGap:Boolean,snapGridWidth:Number,snapGridHeight:Number,isDisplaySnapDigit:Boolean,isDisplayInnerSnapDigit:Boolean,snapDigit:Number,snapThreshold:Number,horizontalGuidelines:Array,verticalGuidelines:Array,elementGuidelines:Array,bounds:Object,innerBounds:Object,snapDistFormat:Function},events:{onSnap:"snap"},css:[":host {\n    --bounds-color: #d66;\n}\n.guideline {\n    pointer-events: none;\n    z-index: 2;\n}\n.guideline.bounds {\n    background: #d66;\n    background: var(--bounds-color);\n}\n.guideline-group {\n    position: absolute;\n    top: 0;\n    left: 0;\n}\n.guideline-group .size-value {\n    position: absolute;\n    color: #f55;\n    font-size: 12px;\n    font-weight: bold;\n}\n.guideline-group.horizontal .size-value {\n    transform-origin: 50% 100%;\n    transform: translateX(-50%);\n    left: 50%;\n    bottom: 5px;\n}\n.guideline-group.vertical .size-value {\n    transform-origin: 0% 50%;\n    top: 50%;\n    transform: translateY(-50%);\n    left: 5px;\n}\n.guideline.gap {\n    background: #f55;\n}\n.size-value.gap {\n    color: #f55;\n}\n"],render:function(t,e){var r,n,a,i,o,s=t.state,u=s.top,l=s.left,c=s.pos1,f=s.pos2,d=s.pos3,p=s.pos4,g=s.snapRenderInfo;if(!g||!L(t,""))return[];s.guidelines=$(t);var h=Math.min(c[0],f[0],d[0],p[0]),v=Math.min(c[1],f[1],d[1],p[1]),x=g.externalPoses||[],E=ep(t.state),S=[],R=[],M=[],D=[],y=[],C=et(E),B=C.width,G=C.height,z=C.top,w=C.left,O=C.bottom,P=C.right,T={left:w,right:P,top:z,bottom:O,center:(w+P)/2,middle:(z+O)/2},k=x.length>0,I=k?et(x):{};if(!g.request){if(g.direction&&y.push(function(t,e,r){var n=[];if(r[0]&&r[1])n=[r,[-r[0],r[1]],[r[0],-r[1]]].map(function(t){return tq(e,t)});else if(r[0]||r[1])t.props.keepRatio?n=[[-1,-1],[-1,1],[1,-1],[1,1],r].map(function(t){return tq(e,t)}):(n=tN(e,r)).length>1&&n.push([(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2]);else for(var a=[e[0],e[1],e[3],e[2],e[0]],i=0;i<4;++i)n.push(a[i]),n.push([(a[i][0]+a[i+1][0])/2,(a[i][1]+a[i+1][1])/2]);return Z(t,n.map(function(t){return t[0]}),n.map(function(t){return t[1]}),1)}(t,E,g.direction)),g.snap){var A=et(E);g.center&&(A.middle=(A.top+A.bottom)/2,A.center=(A.left+A.right)/2),y.push(J(t,A,1))}k&&(g.center&&(I.middle=(I.top+I.bottom)/2,I.center=(I.left+I.right)/2),y.push(J(t,I,1))),y.forEach(function(t){var e=t.vertical.posInfos,r=t.horizontal.posInfos;S.push.apply(S,e.filter(function(t){return t.guidelineInfos.some(function(t){return!t.guideline.hide})}).map(function(t){return{type:"snap",pos:t.pos}})),R.push.apply(R,r.filter(function(t){return t.guidelineInfos.some(function(t){return!t.guideline.hide})}).map(function(t){return{type:"snap",pos:t.pos}})),M.push.apply(M,tG(e)),D.push.apply(D,tG(r))})}tz(t,[w,P],[z,O],S,R),k&&tz(t,[I.left,I.right],[I.top,I.bottom],S,R,g.externalBounds);var F=b(b([],M,!0),D,!0),Y=F.filter(function(t){return t.element&&!t.gapRects}),N=F.filter(function(t){return t.gapRects});return eb(t,"onSnap",{guidelines:F.filter(function(t){return!t.element}),elements:Y,gaps:N},!0),b(b(b(b(b(b([],(r=[h,v],n=t.props.isDisplayInnerSnapDigit,a=[],["vertical","horizontal"].forEach(function(i){var o,s,u,l,c,f,d,p,g,h=Y.filter(function(t){return t.type===i}),v=+("vertical"===i),b=+!v,x=(c=+("vertical"!==i),f=+("vertical"===i),p=T[(d=c?j:X).start],g=T[d.end],(o=h,s=function(t){return t.pos[c]},u=[],l=[],o.forEach(function(t,e){var r=s(t,e,o),n=l.indexOf(r),a=u[n]||[];-1===n&&(l.push(r),u.push(a)),a.push(t)}),u).map(function(t){var e=[],r=[];return t.forEach(function(t){var a,o,s=t.element,u=t.elementRect.rect;if(u[d.end]<p)e.push(t);else if(g<u[d.start])r.push(t);else if(u[d.start]<=p&&g<=u[d.end]&&n){var l=t.pos,h={element:s,rect:m(m({},u),((a={})[d.end]=u[d.start],a))},v={element:s,rect:m(m({},u),((o={})[d.start]=u[d.end],o))},b=[0,0],x=[0,0];b[c]=l[c],b[f]=l[f],x[c]=l[c],x[f]=l[f]+t.size,e.push({type:i,pos:b,size:0,elementRect:h}),r.push({type:i,pos:x,size:0,elementRect:v})}}),e.sort(function(t,e){return e.pos[f]-t.pos[f]}),r.sort(function(t,e){return t.pos[f]-e.pos[f]}),{total:t,start:e,end:r,inner:[]}})),E=v?X:j,S=v?j:X,R=T[E.start],M=T[E.end];x.forEach(function(n){var o=n.total,s=n.start,u=n.end,l=n.inner,c=r[b]+o[0].pos[b]-T[S.start],f=T;s.forEach(function(n){var o=n.elementRect.rect,s=f[E.start]-o[E.end];if(s>0){var u=[0,0];u[v]=r[v]+f[E.start]-R-s,u[b]=c,a.push(tx(t,i,"dashed",a.length,s,u,n.className,e))}f=o}),f=T,u.forEach(function(n){var o=n.elementRect.rect,s=o[E.start]-f[E.end];if(s>0){var u=[0,0];u[v]=r[v]+f[E.end]-R,u[b]=c,a.push(tx(t,i,"dashed",a.length,s,u,n.className,e))}f=o}),l.forEach(function(n){var o=n.elementRect.rect,s=R-o[E.start],u=o[E.end]-M,l=[0,0],f=[0,0];l[v]=r[v]-s,l[b]=c,f[v]=r[v]+M-R,f[b]=c,a.push(tx(t,i,"dashed",a.length,s,l,n.className,e)),a.push(tx(t,i,"dashed",a.length,u,f,n.className,e))})})}),a),!0),(i=[h,v],o=[],["horizontal","vertical"].forEach(function(r){var n=N.filter(function(t){return t.type===r}),a=+("vertical"!==r),s=+!a,u=a?X:j,l=a?j:X,c=T[u.start],f=T[u.end],d=T[l.start],p=T[l.end];n.forEach(function(r){var n=r.gap,g=r.gapRects,h=r.className,v=Math.max.apply(Math,b([d],g.map(function(t){return t.rect[l.start]}),!1)),m=Math.min.apply(Math,b([p],g.map(function(t){return t.rect[l.end]}),!1)),x=(v+m)/2;v!==m&&x!==(d+p)/2&&g.forEach(function(r){var l=r.rect,p=[i[0],i[1]];if(l[u.end]<c)p[a]+=l[u.end]-c;else{if(!(f<l[u.start]))return;p[a]+=l[u.start]-c-n}p[s]+=x-d,o.push(tx(t,a?"vertical":"horizontal","gap",o.length,n,p,h,e))})})}),o),!0),tb(t,"horizontal",D,[l,u],T,e),!0),tb(t,"vertical",M,[l,u],T,e),!0),tm(t,"horizontal",R,h,u,B,0,e),!0),tm(t,"vertical",S,v,l,G,1,e),!0)},dragStart:function(t,e){t.state.snapRenderInfo={request:e.isRequest,snap:!0,center:!0},ty(t)},drag:function(t){t.state.guidelines=$(t)},pinchStart:function(t){this.unset(t)},dragEnd:function(t){this.unset(t)},dragControlCondition:function(t,e){return!!(eG(t,e)||W(t,e))||(!e.isRequest&&e.inputEvent?(0,i.nB)(e.inputEvent.target,tQ("snap-control")):void 0)},dragControlStart:function(t){t.state.snapRenderInfo=null,ty(t)},dragControl:function(t){this.drag(t)},dragControlEnd:function(t){this.unset(t)},dragGroupStart:function(t,e){this.dragStart(t,e)},dragGroup:function(t){this.drag(t)},dragGroupEnd:function(t){this.unset(t)},dragGroupControlStart:function(t){t.state.snapRenderInfo=null,ty(t)},dragGroupControl:function(t){this.drag(t)},dragGroupControlEnd:function(t){this.unset(t)},unset:function(t){var e=t.state;e.enableSnap=!1,e.guidelines=[],e.snapRenderInfo=null,e.elementRects=[]}},eT,tw,rp,{name:"rotatable",canPinch:!0,props:{rotatable:Boolean,rotationPosition:String,throttleRotate:Number,renderDirections:Object,rotationTarget:Object},events:{onRotateStart:"rotateStart",onBeforeRotate:"beforeRotate",onRotate:"rotate",onRotateEnd:"rotateEnd",onRotateGroupStart:"rotateGroupStart",onBeforeRotateGroup:"beforeRotateGroup",onRotateGroup:"rotateGroup",onRotateGroupEnd:"rotateGroupEnd"},css:[".rotation {\n            position: absolute;\n            height: 40px;\n            width: 1px;\n            transform-origin: 50% 100%;\n            height: calc(40px * var(--zoom));\n            top: auto;\n            left: 0;\n            bottom: 100%;\n            will-change: transform;\n        }\n        .rotation .rotation-line {\n            display: block;\n            width: 100%;\n            height: 100%;\n            transform-origin: 50% 50%;\n        }\n        .rotation .rotation-control {\n            border-color: #4af;\n            border-color: var(--moveable-color);\n            background:#fff;\n            cursor: alias;\n        }"],render:function(t,e){var r=t.props,n=r.rotatable,a=r.rotationPosition,i=r.zoom,o=r.renderDirections,s=t.state,u=s.renderPoses,l=s.direction;if(!n)return null;var c=function(t,e,r){var n=e[0],a=e[1],i=e[2],o=e[3];if("none"!==t){var s=(t||"top").split("-"),u=s[0],l=s[1],c=[n,a];"left"===u?c=[i,n]:"right"===u?c=[a,o]:"bottom"===u&&(c=[o,i]);var f=[(c[0][0]+c[1][0])/2,(c[0][1]+c[1][1])/2],d=es(c,r);if(l){var p="top"===l||"left"===l,g="bottom"===u||"left"===u;f=c[p&&!g||!p&&g?0:1]}return[f,d]}}(a,u,l),f=[];if(c){var d=c[0],p=c[1];f.push(e.createElement("div",{key:"rotation",className:tQ("rotation"),style:{transform:"translate(-50%) translate(".concat(d[0],"px, ").concat(d[1],"px) rotate(").concat(p,"rad)")}},e.createElement("div",{className:tQ("line rotation-line"),style:{transform:"scaleX(".concat(i,")")}}),e.createElement("div",{className:tQ("control rotation-control"),style:{transform:"translate(0.5px) scale(".concat(i,")")}})))}return o&&f.push.apply(f,k(t,[],e)),f},dragControlCondition:W,dragControlStart:function(t,e){var r=e.datas,n=e.clientX,a=e.clientY,i=e.parentRotate,o=e.parentFlag,s=e.isPinch,u=e.isRequest,l=t.state,c=l.target,f=l.left,d=l.top,p=l.origin,g=l.beforeOrigin,h=l.direction,v=l.beforeDirection,b=l.targetTransform,x=l.moveableClientRect;if(!u&&!c)return!1;var E=t.getRect();if(r.rect=E,r.transform=b,r.left=f,r.top=d,r.fixedPosition=t$(t,tK(t)),u||s||o){var S=i||0;r.beforeInfo={origin:E.beforeOrigin,prevDeg:S,defaultDeg:S,prevSnapDeg:0},r.afterInfo=m(m({},r.beforeInfo),{origin:E.origin}),r.absoluteInfo=m(m({},r.beforeInfo),{origin:E.origin,startValue:S})}else r.beforeInfo={origin:E.beforeOrigin},r.afterInfo={origin:E.origin},r.absoluteInfo={origin:E.origin,startValue:E.rotation},F(t,r.beforeInfo,n,a,g,x),F(t,r.afterInfo,n,a,p,x),F(t,r.absoluteInfo,n,a,p,x);r.direction=h,r.beforeDirection=v,r.startValue=0,r.datas={},tH(e,"rotate");var R=eh(t,e,m(m({set:function(t){r.startValue=t*Math.PI/180}},t_(e)),{dragStart:tw.dragStart(t,new w().dragStart([0,0],e))}));return r.isRotate=!1!==eb(t,"onRotateStart",R),t.state.snapRenderInfo={request:e.isRequest},!!r.isRotate&&R},dragControl:function(t,e){var r,n,a,i,s,u,l,c,f,d=e.datas,p=e.clientX,g=e.clientY,h=e.parentRotate,v=e.parentFlag,b=e.isPinch,x=e.groupDelta,E=d.beforeDirection,S=d.beforeInfo,R=d.afterInfo,M=d.absoluteInfo,D=d.isRotate,y=d.startValue,C=d.rect;if(D){tT(e,"rotate");var B=E*ee(e.datas.beforeTransform,[50,50],100,100).direction,G=t.props.parentMoveable,z=0,w=0,O=0,P=180/Math.PI*y,T=M.startValue,k=!1;if(!v&&"parentDist"in e){var I=e.parentDist;i=I,u=I,c=I}else b||v?(i=Y(h,E,S),u=Y(h,B,R),c=Y(h,B,M)):(i=N(p,g,E,S),u=N(p,g,B,R),c=N(p,g,B,M),k=!0);if(eb(t,"onBeforeRotate",eh(t,e,{beforeRotation:s=P+i,rotation:l=P+u,absoluteRotation:f=T+c,setRotation:function(t){i=u=t-P,c=u}})),z=(r=q(t,C,S,i,P,k))[0],i=r[1],s=r[2],w=(n=q(t,C,R,u,P,k))[0],u=n[1],l=n[2],O=(a=q(t,C,M,c,T,k))[0],c=a[1],f=a[2],O||w||z||G){var A,F,W,_,H,j=tk(d,"rotate(".concat(l,"deg)"),"rotate(".concat(u,"deg)")),X=(A=t,F=u,W=d.fixedPosition,_=d,H=tK(A),tU(A,"rotate(".concat(F,"deg)"),H,W,_)),L=(0,o.Rd)((0,o.tY)(x||[0,0],X),d.prevInverseDist||[0,0]);d.prevInverseDist=X,d.requestValue=null;var V=eh(t,e,m({delta:w,dist:u,rotate:l,rotation:l,beforeDist:i,beforeDelta:z,beforeRotate:s,beforeRotation:s,absoluteDist:c,absoluteDelta:O,absoluteRotate:f,absoluteRotation:f,isPinch:!!b},tV(t,j,L,b,e)));return eb(t,"onRotate",V),V}}},dragControlAfter:function(t,e){e.datas.requestValue},dragControlEnd:function(t,e){var r=e.datas;if(r.isRotate){r.isRotate=!1;var n=ev(t,e,{});return eb(t,"onRotateEnd",n),n}},dragGroupControlCondition:W,dragGroupControlStart:function(t,e){var r=e.datas,n=t.state,a=n.left,i=n.top,s=n.beforeOrigin,u=this.dragControlStart(t,e);if(!u)return!1;u.set(r.beforeDirection*t.rotation);var l=T(t,this,"dragControlStart",e,function(t,e){var r=t.state,n=r.left,u=r.top,l=r.beforeOrigin,c=(0,o.tY)((0,o.Rd)([n,u],[a,i]),(0,o.Rd)(l,s));return e.datas.groupClient=c,m(m({},e),{parentRotate:0})}),c=m(m({},u),{targets:t.props.targets,events:l});return r.isRotate=!1!==eb(t,"onRotateGroupStart",c),!!r.isRotate&&u},dragGroupControl:function(t,e){var r=e.datas;if(r.isRotate){em(t,"onBeforeRotate",function(r){eb(t,"onBeforeRotateGroup",eh(t,e,m(m({},r),{targets:t.props.targets})))});var n=this.dragControl(t,e);if(n){var a=r.beforeDirection,i=n.beforeDist,s=n.beforeDelta/180*Math.PI,u=T(t,this,"dragControl",e,function(t,e){var r=e.datas.groupClient,n=r[0],u=r[1],l=(0,o.e$)([n,u],s*a),c=l[0],f=l[1];return e.datas.groupClient=[c,f],m(m({},e),{parentRotate:i,groupDelta:[c-n,f-u]})});t.rotation=a*n.beforeRotation;var l=m({targets:t.props.targets,events:u,set:function(e){t.rotation=e},setGroupRotation:function(e){t.rotation=e}},n);return eb(t,"onRotateGroup",l),l}}},dragGroupControlEnd:function(t,e){var r=e.isDrag;if(e.datas.isRotate){this.dragControlEnd(t,e);var n=T(t,this,"dragControlEnd",e),a=ev(t,e,{targets:t.props.targets,events:n});return eb(t,"onRotateGroupEnd",a),r}},request:function(t){var e={},r=0,n=t.getRotation();return{isControl:!0,requestStart:function(){return{datas:e}},request:function(t){return"deltaRotate"in t?r+=t.deltaRotate:"rotate"in t&&(r=t.rotate-n),{datas:e,parentDist:r}},requestEnd:function(){return{datas:e,isDrag:!0}}}}},ek,{name:"scalable",ableGroup:"size",canPinch:!0,props:{scalable:Boolean,throttleScale:Number,renderDirections:String,keepRatio:Boolean},events:{onScaleStart:"scaleStart",onBeforeScale:"beforeScale",onScale:"scale",onScaleEnd:"scaleEnd",onScaleGroupStart:"scaleGroupStart",onBeforeScaleGroup:"beforeScaleGroup",onScaleGroup:"scaleGroup",onScaleGroupEnd:"scaleGroupEnd"},render:function(t,e){var r=t.props,n=r.resizable,a=r.scalable,i=r.edge;if(!n&&a)return i?A(t,e):k(t,y,e)},dragControlCondition:eG,dragControlStart:function(t,e){var r=e.datas,n=e.isPinch,a=e.inputEvent,o=e.parentDirection||(n?[0,0]:ef(a.target)),s=t.state,u=s.width,l=s.height,c=s.targetTransform,f=s.target,d=s.pos1,p=s.pos2,g=s.pos4;if(!o||!f)return!1;n||tP(t,e),r.datas={},r.transform=c,r.prevDist=[1,1],r.direction=o,r.startOffsetWidth=u,r.startOffsetHeight=l,r.startValue=[1,1];var h=(0,i.l$)(d,p),v=(0,i.l$)(p,g),b=!o[0]&&!o[1]||o[0]||!o[1];function x(t){r.ratio=t&&isFinite(t)?t:0}function E(t){r.fixedDirection=t,r.fixedPosition=tq(r.startPositions,t)}r.scaleWidth=h,r.scaleHeight=v,r.scaleXRatio=h/u,r.scaleYRatio=v/l,tH(e,"scale"),r.isWidth=b,r.startPositions=ep(t.state),r.setFixedDirection=E,x((0,i.l$)(d,p)/(0,i.l$)(p,g)),E([-o[0],-o[1]]);var S=eh(t,e,m(m({direction:o,set:function(t){r.startValue=t},setRatio:x,setFixedDirection:E},t_(e)),{dragStart:tw.dragStart(t,new w().dragStart([0,0],e))}));return!1!==eb(t,"onScaleStart",S)&&(r.isScale=!0,t.state.snapRenderInfo={request:e.isRequest,direction:o}),!!r.isScale&&S},dragControl:function(t,e){tT(e,"scale");var r,n,a=e.datas,s=e.parentKeepRatio,u=e.parentFlag,l=e.isPinch,c=e.dragClient,f=e.isRequest,d=a.prevDist,p=a.direction,g=a.startOffsetWidth,h=a.startOffsetHeight,v=a.isScale,b=a.startValue,x=a.isWidth,E=a.ratio;if(!v)return!1;var S=t.props,R=S.throttleScale,M=S.parentMoveable,D=p;p[0]||p[1]||(D=[1,1]);var y=E&&(null!=s?s:S.keepRatio)||!1,C=t.state;function B(){var t=eP(D,y,a,e),r=t.distWidth,n=t.distHeight,i=(g+r)/g,o=(h+n)/h;return i=D[0]||y?i*b[0]:b[0],o=D[1]||y?o*b[1]:b[1],0===i&&(i=(d[0]>0?1:-1)*1e-9),0===o&&(o=(d[1]>0?1:-1)*1e-9),[i,o]}var G=B();if(!l&&t.props.groupable){var z=(C.snapRenderInfo||{}).direction;(0,i.cy)(z)&&(z[0]||z[1])&&(C.snapRenderInfo={direction:p,request:e.isRequest})}eb(t,"onBeforeScale",eh(t,e,{scale:G,setFixedDirection:function(t){return a.setFixedDirection(t),G=B()},setScale:function(t){G=t}}));var w=[G[0]/b[0],G[1]/b[1]],O=c,P=[0,0];if(c||(O=!u&&l?tZ(t,[0,0]):a.fixedPosition),l||(P=function(t,e,r,n,a){if(!L(t,"scalable"))return[0,0];var i=a.startOffsetWidth,s=a.startOffsetHeight,u=a.fixedPosition,l=a.fixedDirection,c=a.is3d,f=tB(t,function(t,r){var n,f,d,p,g,h,v;return tC((n=a,f=(0,o.tY)(e,[t/i,r/s]),d=n.transformOrigin,p=n.offsetMatrix,g=n.is3d,h=n.targetMatrix,v=g?4:3,tW(p,(0,o.lw)(h,(0,o.kb)(f,v),v),d,v)),i,s,l,u,c)},i,s,r,u,n,a);return[f[0]/i,f[1]/s]}(t,w,p,f,a)),y){D[0]&&D[1]&&P[0]&&P[1]&&(Math.abs(P[0]*g)>Math.abs(P[1]*h)?P[1]=0:P[0]=0);var T=!P[0]&&!P[1];if(T&&(x?w[0]=(0,i.nF)(w[0]*b[0],R)/b[0]:w[1]=(0,i.nF)(w[1]*b[1],R)/b[1]),D[0]&&!D[1]||P[0]&&!P[1]||T&&x){w[0]+=P[0];var k=g*w[0]*b[0]/E;w[1]=k/h/b[1]}else if(!D[0]&&D[1]||!P[0]&&P[1]||T&&!x){w[1]+=P[1];var I=h*w[1]*b[1]*E;w[0]=I/g/b[0]}}else w[0]+=P[0],w[1]+=P[1],P[0]||(w[0]=(0,i.nF)(w[0]*b[0],R)/b[0]),P[1]||(w[1]=(0,i.nF)(w[1]*b[1],R)/b[1]);0===w[0]&&(w[0]=(d[0]>0?1:-1)*1e-9),0===w[1]&&(w[1]=(d[1]>0?1:-1)*1e-9);var A=[w[0]/d[0],w[1]/d[1]];G=[w[0]*b[0],w[1]*b[1]];var F=(r=a.fixedDirection,n=O,tU(t,"scale(".concat(w.join(", "),")"),r,n,a)),Y=(0,o.Rd)(F,a.prevInverseDist||[0,0]);if(a.prevDist=w,a.prevInverseDist=F,G[0]===d[0]&&G[1]===d[1]&&Y.every(function(t){return!t})&&!M)return!1;var N=tk(a,"scale(".concat(G.join(", "),")"),"scale(".concat(w.join(", "),")")),q=eh(t,e,m({offsetWidth:g,offsetHeight:h,direction:p,scale:G,dist:w,delta:A,isPinch:!!l},tV(t,N,Y,l,e)));return eb(t,"onScale",q),q},dragControlEnd:function(t,e){var r=e.datas,n=e.isDrag;return!!r.isScale&&(r.isScale=!1,eb(t,"onScaleEnd",ev(t,e,{})),n)},dragGroupControlCondition:eG,dragGroupControlStart:function(t,e){var r=e.datas,n=this.dragControlStart(t,e);if(!n)return!1;var a=O(t,"resizable",e);function i(e,n){var a=r.fixedDirection,i=r.fixedPosition,s=tq(n.datas.startPositions||ep(e.state),a),u=(0,o.Di)((0,o.XI)(-t.rotation/180*Math.PI,3),[s[0]-i[0],s[1]-i[1],1],3),l=u[0],c=u[1];return n.datas.originalX=l,n.datas.originalY=c,n}r.moveableScale=t.scale;var s=T(t,this,"dragControlStart",e,function(t,e){return i(t,e)}),u=function(t){n.setFixedDirection(t),s.forEach(function(e,r){e.setFixedDirection(t),i(e.moveable,a[r])})};r.setFixedDirection=u;var l=m(m({},n),{targets:t.props.targets,events:s,setFixedDirection:u});return r.isScale=!1!==eb(t,"onScaleGroupStart",l),!!r.isScale&&l},dragGroupControl:function(t,e){var r=e.datas;if(r.isScale){em(t,"onBeforeScale",function(r){eb(t,"onBeforeScaleGroup",eh(t,e,m(m({},r),{targets:t.props.targets})))});var n=this.dragControl(t,e);if(n){var a=r.moveableScale;t.scale=[n.scale[0]*a[0],n.scale[1]*a[1]];var i=t.props.keepRatio,s=n.dist,u=n.scale,l=r.fixedPosition,c=T(t,this,"dragControl",e,function(e,r){var n=(0,o.Di)((0,o.XI)(t.rotation/180*Math.PI,3),[r.datas.originalX*s[0],r.datas.originalY*s[1],1],3),a=n[0],c=n[1];return m(m({},r),{parentDist:null,parentScale:u,parentKeepRatio:i,dragClient:(0,o.tY)(l,[a,c])})}),f=m({targets:t.props.targets,events:c},n);return eb(t,"onScaleGroup",f),f}}},dragGroupControlEnd:function(t,e){var r=e.isDrag;if(e.datas.isScale){this.dragControlEnd(t,e);var n=T(t,this,"dragControlEnd",e),a=ev(t,e,{targets:t.props.targets,events:n});return eb(t,"onScaleGroupEnd",a),r}},request:function(){var t={},e=0,r=0;return{isControl:!0,requestStart:function(e){return{datas:t,parentDirection:e.direction||[1,1]}},request:function(n){return e+=n.deltaWidth,r+=n.deltaHeight,{datas:t,parentDist:[e,r],parentKeepRatio:n.keepRatio}},requestEnd:function(){return{datas:t,isDrag:!0}}}}},{name:"warpable",ableGroup:"size",props:{warpable:Boolean,renderDirections:Array},events:{onWarpStart:"warpStart",onWarp:"warp",onWarpEnd:"warpEnd"},render:function(t,e){var r=t.props,n=r.resizable,a=r.scalable,i=r.warpable,o=r.zoom;if(n||a||!i)return[];var s=t.state,u=s.pos1,l=s.pos2,c=s.pos3,f=s.pos4,d=eI(u,l),p=eI(l,u),g=eI(u,c),h=eI(c,u),v=eI(c,f),m=eI(f,c),x=eI(l,f),E=eI(f,l);return b([e.createElement("div",{className:tQ("line"),key:"middeLine1",style:ea(d,v,o)}),e.createElement("div",{className:tQ("line"),key:"middeLine2",style:ea(p,m,o)}),e.createElement("div",{className:tQ("line"),key:"middeLine3",style:ea(g,x,o)}),e.createElement("div",{className:tQ("line"),key:"middeLine4",style:ea(h,E,o)})],k(t,y,e),!0)},dragControlCondition:function(t,e){return!e.isRequest&&(0,i.nB)(e.inputEvent.target,tQ("direction"))},dragControlStart:function(t,e){var r=e.datas,n=e.inputEvent,a=t.props.target,i=ef(n.target);if(!i||!a)return!1;var s=t.state,u=s.transformOrigin,l=s.is3d,c=s.targetTransform,f=s.targetMatrix,d=s.width,p=s.height,g=s.left,h=s.top;r.datas={},r.targetTransform=c,r.warpTargetMatrix=l?f:(0,o._C)(f,3,4),r.targetInverseMatrix=(0,o.bn)((0,o.B8)(r.warpTargetMatrix,4),3,4),r.direction=i,r.left=g,r.top=h,r.poses=[[0,0],[d,0],[0,p],[d,p]].map(function(t){return(0,o.Rd)(t,u)}),r.nextPoses=r.poses.map(function(t){var e=t[0],n=t[1];return(0,o.Di)(r.warpTargetMatrix,[e,n,0,1],4)}),r.startValue=(0,o.k8)(4),r.prevMatrix=(0,o.k8)(4),r.absolutePoses=ep(s),r.posIndexes=tY(i),tP(t,e),tH(e,"matrix3d"),s.snapRenderInfo={request:e.isRequest,direction:i};var v=eh(t,e,m({set:function(t){r.startValue=t}},t_(e)));return!1!==eb(t,"onWarpStart",v)&&(r.isWarp=!0),r.isWarp},dragControl:function(t,e){var r=e.datas,n=e.isRequest,a=e.distX,i=e.distY,s=r.targetInverseMatrix,u=r.prevMatrix,l=r.isWarp,c=r.startValue,f=r.poses,d=r.posIndexes,p=r.absolutePoses;if(!l)return!1;if(tT(e,"matrix3d"),L(t,"warpable")){var g=d.map(function(t){return p[t]});g.length>1&&g.push([(g[0][0]+g[1][0])/2,(g[0][1]+g[1][1])/2]);var h=tR(t,n,{horizontal:g.map(function(t){return t[1]+i}),vertical:g.map(function(t){return t[0]+a})}),v=h.horizontal,m=h.vertical;i-=v.offset,a-=m.offset}var b=tF({datas:r,distX:a,distY:i},!0),x=r.nextPoses.slice();if(d.forEach(function(t){x[t]=(0,o.tY)(x[t],b)}),!D.every(function(t){var e,r,n,a,i;return e=t.map(function(t){return f[t]}),r=t.map(function(t){return x[t]}),n=eA(e[0],e[1],e[2]),a=eA(r[0],r[1],r[2]),(!(n>=(i=Math.PI))||!(a<=i))&&(!(n<=i)||!(a>=i))}))return!1;var E=(0,o.qL)(f[0],f[2],f[1],f[3],x[0],x[2],x[1],x[3]);if(!E.length)return!1;var S=tI(r,(0,o.lw)(s,E,4),!0),R=(0,o.lw)((0,o.B8)(u,4),S,4);r.prevMatrix=S;var M=(0,o.lw)(c,S,4),y=tk(r,"matrix3d(".concat(M.join(", "),")"),"matrix3d(".concat(S.join(", "),")"));return tX(e,y),eb(t,"onWarp",eh(t,e,{delta:R,matrix:M,dist:S,multiply:o.lw,transform:y})),!0},dragControlEnd:function(t,e){var r=e.datas,n=e.isDrag;return!!r.isWarp&&(r.isWarp=!1,eb(t,"onWarpEnd",ev(t,e,{})),n)}},{name:"scrollable",canPinch:!0,props:{scrollable:Boolean,scrollContainer:Object,scrollThreshold:Number,getScrollPosition:Function},events:{onScroll:"scroll",onScrollGroup:"scrollGroup"},dragRelation:"strong",dragStart:function(t,e){var r=t.props.scrollContainer,n=void 0===r?t.getContainer():r,a=new c.A,i=ez(n,!0);e.datas.dragScroll=a;var o=e.isControl?"controlGesto":"targetGesto",s=e.targets;a.on("scroll",function(r){var n=eh(t,e,{scrollContainer:r.container,direction:r.direction});s&&(n.targets=s),eb(t,s?"onScrollGroup":"onScroll",n)}).on("move",function(r){var n=r.offsetX,a=r.offsetY;t[o].scrollBy(n,a,e.inputEvent,!1)}),a.dragStart(e,{container:i})},checkScroll:function(t,e){var r=e.datas.dragScroll;if(r){var n=t.props,a=n.scrollContainer,i=void 0===a?t.getContainer():a,o=n.scrollThreshold,s=n.getScrollPosition,u=void 0===s?ej:s;return r.drag(e,{container:i,threshold:void 0===o?0:o,getScrollPosition:function(t){return u({scrollContainer:t.container,direction:t.direction})}}),!0}},drag:function(t,e){return this.checkScroll(t,e)},dragEnd:function(t,e){e.datas.dragScroll.dragEnd(),e.datas.dragScroll=null},dragControlStart:function(t,e){return this.dragStart(t,m(m({},e),{isControl:!0}))},dragControl:function(t,e){return this.drag(t,e)},dragControlEnd:function(t,e){return this.dragEnd(t,e)},dragGroupStart:function(t,e){return this.dragStart(t,m(m({},e),{targets:t.props.targets}))},dragGroup:function(t,e){return this.drag(t,m(m({},e),{targets:t.props.targets}))},dragGroupEnd:function(t,e){return this.dragEnd(t,m(m({},e),{targets:t.props.targets}))},dragGroupControlStart:function(t,e){return this.dragStart(t,m(m({},e),{targets:t.props.targets,isControl:!0}))},dragGroupContro:function(t,e){return this.drag(t,m(m({},e),{targets:t.props.targets}))},dragGroupControEnd:function(t,e){return this.dragEnd(t,m(m({},e),{targets:t.props.targets}))}},eL,eH,{name:"originDraggable",props:{originDraggable:Boolean,originRelative:Boolean},events:{onDragOriginStart:"dragOriginStart",onDragOrigin:"dragOrigin",onDragOriginEnd:"dragOriginEnd"},css:[":host[data-able-origindraggable] .control.origin {\n    pointer-events: auto;\n}"],dragControlCondition:function(t,e){return e.isRequest?"originDraggable"===e.requestAble:(0,i.nB)(e.inputEvent.target,tQ("origin"))},dragControlStart:function(t,e){var r=e.datas;tP(t,e);var n=eh(t,e,{dragStart:tw.dragStart(t,new w().dragStart([0,0],e))}),a=eb(t,"onDragOriginStart",n);return(r.startOrigin=t.state.transformOrigin,r.startTargetOrigin=t.state.targetOrigin,r.prevOrigin=[0,0],r.isDragOrigin=!0,!1===a)?(r.isDragOrigin=!1,!1):n},dragControl:function(t,e){var r=e.datas,n=e.isPinch,a=e.isRequest;if(!r.isDragOrigin)return!1;var i=tF(e),s=i[0],u=i[1],l=t.state,c=l.width,f=l.height,d=l.offsetMatrix,p=l.targetMatrix,g=l.is3d,h=t.props.originRelative,v=void 0===h||h,m=g?4:3,b=[s,u];if(a){var x=e.distOrigin;(x[0]||x[1])&&(b=x)}var E=(0,o.tY)(r.startOrigin,b),S=(0,o.tY)(r.startTargetOrigin,b),R=(0,o.Rd)(b,r.prevOrigin),M=tW(d,p,E,m),D=t.getRect(),y=et(t9(M,c,f,m)),C=[D.left-y.left,D.top-y.top];r.prevOrigin=b;var B=[eC(S[0],c,v),eC(S[1],f,v)].join(" "),G=eh(t,e,{width:c,height:f,origin:E,dist:b,delta:R,transformOrigin:B,drag:tw.drag(t,z(e,t.state,C,!!n,!1))});return eb(t,"onDragOrigin",G),G},dragControlEnd:function(t,e){return!!e.datas.isDragOrigin&&(eb(t,"onDragOriginEnd",ev(t,e,{})),!0)},dragGroupControlCondition:function(t,e){return this.dragControlCondition(t,e)},dragGroupControlStart:function(t,e){return!!this.dragControlStart(t,e)},dragGroupControl:function(t,e){var r=this.dragControl(t,e);return!!r&&(t.transformOrigin=r.transformOrigin,!0)},request:function(t){var e={},r=t.getRect(),n=0,a=0,i=r.transformOrigin,o=[0,0];return{isControl:!0,requestStart:function(){return{datas:e}},request:function(t){return"deltaOrigin"in t?(o[0]+=t.deltaOrigin[0],o[1]+=t.deltaOrigin[1]):"origin"in t?(o[0]=t.origin[0]-i[0],o[1]=t.origin[1]-i[1]):("x"in t?n=t.x-r.left:"deltaX"in t&&(n+=t.deltaX),"y"in t?a=t.y-r.top:"deltaY"in t&&(a+=t.deltaY)),{datas:e,distX:n,distY:a,distOrigin:o}},requestEnd:function(){return{datas:e,isDrag:!0}}}}},rt,{name:"roundable",props:{roundable:Boolean,roundRelative:Boolean,minRoundControls:Array,maxRoundControls:Array,roundClickable:Boolean},events:{onRoundStart:"roundStart",onRound:"round",onRoundEnd:"roundEnd"},css:[".control.border-radius {\n    background: #d66;\n    cursor: pointer;\n}",":host[data-able-roundable] .line.direction {\n    cursor: pointer;\n}"],render:function(t,e){var r=t.state,n=r.target,a=r.width,i=r.height,s=r.allMatrix,u=r.is3d,l=r.left,c=r.top,f=r.borderRadiusState,d=t.props,p=d.minRoundControls,g=d.maxRoundControls,h=void 0===g?[4,4]:g,v=d.zoom;if(!n)return null;var m=u?4:3,b=re(n,a,i,void 0===p?[0,0]:p,f);if(!b)return null;var x=0,E=0;return b.map(function(t,r){E+=Math.abs(t.horizontal),x+=Math.abs(t.vertical);var n=(0,o.Rd)(t7(s,t.pos,m),[l,c]),a=t.vertical?x<=h[1]:E<=h[0];return e.createElement("div",{key:"borderRadiusControl".concat(r),className:tQ("control","border-radius"),"data-radius-index":r,style:{display:a?"block":"none",transform:"translate(".concat(n[0],"px, ").concat(n[1],"px) scale(").concat(v,")")}})})},dragControlCondition:function(t,e){if(!e.inputEvent||e.isRequest)return!1;var r=e.inputEvent.target.getAttribute("class")||"";return r.indexOf("border-radius")>-1||r.indexOf("moveable-line")>-1&&r.indexOf("moveable-direction")>-1},dragControlStart:function(t,e){var r=e.inputEvent,n=e.datas,a=r.target,i=a.getAttribute("class")||"",o=i.indexOf("border-radius")>-1,s=i.indexOf("moveable-line")>-1&&i.indexOf("moveable-direction")>-1,u=o?parseInt(a.getAttribute("data-radius-index"),10):-1,l=s?parseInt(a.getAttribute("data-line-index"),10):-1;if(!o&&!s||!1===eb(t,"onRoundStart",eh(t,e,{})))return!1;n.lineIndex=l,n.controlIndex=u,n.isControl=o,n.isLine=s,tP(t,e);var c=t.props,f=c.roundRelative,d=c.minRoundControls,p=t.state,g=p.target,h=p.width,v=p.height;n.isRound=!0,n.prevDist=[0,0];var m=re(g,h,v,void 0===d?[0,0]:d)||[];return n.controlPoses=m,p.borderRadiusState=eJ(m.map(function(t){return t.pos}),m,f,h,v).styles.join(" "),!0},dragControl:function(t,e){var r=e.datas;if(!r.isRound||!r.isControl||!r.controlPoses.length)return!1;var n=r.controlIndex,a=r.controlPoses,i=tF(e),s=i[0],u=i[1],l=[s,u],c=(0,o.Rd)(l,r.prevDist),f=t.props.maxRoundControls,d=void 0===f?[4,4]:f,p=t.state,g=p.width,h=p.height,v=a[n],m=v.vertical,b=v.horizontal,x=a.map(function(t){var e=t.horizontal,r=t.vertical,n=[e*b*l[0],r*m*l[1]];if(e){if(1===d[0])return n;else if(d[0]<4&&e!==b)return n}else if(0===d[1])return n[1]=r*b*l[0]/g*h,n;else if(m){if(1===d[1])return n;else if(d[1]<4&&r!==m)return n}return[0,0]});x[n]=l;var E=a.map(function(t,e){return(0,o.tY)(t.pos,x[e])});return r.prevDist=[s,u],rr(t,e,l,c,a,E),!0},dragControlEnd:function(t,e){var r=t.state;r.borderRadiusState="";var n=e.datas,a=e.isDouble;if(!n.isRound)return!1;var i=r.width,o=r.height,s=n.isControl,u=n.controlIndex,l=n.isLine,c=n.lineIndex,f=n.controlPoses,d=f.map(function(t){return t.pos}),p=d.length,g=t.props.roundClickable;if(a&&(void 0===g||g)){if(s)e2(f,d,u,0);else if(l){var h,v,m,b,x,E,S,R=tO(t,e),M=R[0],D=R[1];v=(h=e4(f)).horizontals,m=h.verticals,b=v.length,x=m.length,E=-1,S=-1,0===c?0===b?E=0:1===b&&(E=1):3===c&&(b<=2?E=2:b<=3&&(E=3)),2===c?0===x?S=0:x<4&&(S=3):1===c&&(x<=1?S=1:x<=2&&(S=2)),e3(f,d,0,E,S,M,D,i,o)}p!==f.length&&rr(t,e,[0,0],[0,0],f,d)}return eb(t,"onRoundEnd",ev(t,e,{})),r.borderRadiusState="",!0},unset:function(t){t.state.borderRadiusState=""}},rc,rg,rf,e_,ra],rv=rh.reduce(function(t,e){return m(m({},t),"events"in e?e.events:{})},{}),rm=rh.reduce(function(t,e){return m(m({},t),e.props)},{});function rb(t,e){return Math.max.apply(Math,t.map(function(t){var r=t[0],n=t[1],a=t[2],i=t[3];return Math.max(r[e],n[e],a[e],i[e])}))}function rx(t,e){return Math.min.apply(Math,t.map(function(t){var r=t[0],n=t[1],a=t[2],i=t[3];return Math.min(r[e],n[e],a[e],i[e])}))}Object.keys(function(t){var e={};for(var r in t)e[t[r]]=r;return e}(rv)),Object.keys(rm);var rE=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.differ=new u.A,e.moveables=[],e.transformOrigin="50% 50%",e}v(e,t);var r=e.prototype;return r.checkUpdate=function(){this.updateAbles()},r.updateRect=function(t,e,r){if(void 0===r&&(r=!0),this.controlBox){this.moveables.forEach(function(e){e.updateRect(t,!1,!1)});var n=this.state,a=this.props,s=n.target||a.target;(!e||""!==t&&a.updateGroup)&&(this.rotation=a.defaultGroupRotate,this.transformOrigin=a.defaultGroupOrigin||"50% 50%",this.scale=[1,1]);var u=this.rotation,c=this.scale,f=function(t,e){if(!t.length)return[0,0,0,0];var r=t.map(function(t){return ep(t.state)}),n=1e10,a=1e10,s=0,u=0,l=(0,i.nF)(e,1e-7);if(l%90){var c=l/180*Math.PI,f=Math.tan(c),d=-1/f,p=[-1e10,1e10],g=[-1e10,1e10];r.forEach(function(t){t.forEach(function(t){var e=t[1]-f*t[0],r=t[1]-d*t[0];p[0]=Math.max(p[0],e),p[1]=Math.min(p[1],e),g[0]=Math.max(g[0],r),g[1]=Math.min(g[1],r)})}),p.forEach(function(t){g.forEach(function(e){var r=(e-t)/(f-d);n=Math.min(n,r),a=Math.min(a,f*r+t)})});var h=r.map(function(t){var e=t[0],r=t[1],n=t[2],a=t[3];return[(0,o.e$)(e,-c),(0,o.e$)(r,-c),(0,o.e$)(n,-c),(0,o.e$)(a,-c)]});s=rb(h,0)-rx(h,0),u=rb(h,1)-rx(h,1)}else if(n=rx(r,0),a=rx(r,1),s=rb(r,0)-n,u=rb(r,1)-a,l%180){var v=s;s=u,u=v}return[n,a,s,u]}(this.moveables,u),d=f[0],p=f[1],g=f[2],h=f[3],v="rotate(".concat(u,"deg) scale(").concat(c[0]>=0?1:-1,", ").concat(c[1]>=0?1:-1,")");s.style.cssText+="left:0px;top:0px; transform-origin: ".concat(this.transformOrigin,"; width:").concat(g,"px; height:").concat(h,"px;")+"transform:".concat(v),n.width=g,n.height=h;var b=this.getContainer(),x=eu(this.controlBox.getElement(),s,this.controlBox.getElement(),this.getContainer(),this.props.rootContainer||b),E=[x.left,x.top],S=ep(x),R=S[0],M=S[1],D=S[2],y=S[3],C=(0,l.Qk)([R,M,D,y]),B=[C.minX,C.minY];x.pos1=(0,o.Rd)(R,B),x.pos2=(0,o.Rd)(M,B),x.pos3=(0,o.Rd)(D,B),x.pos4=(0,o.Rd)(y,B),x.left=d-x.left+B[0],x.top=p-x.top+B[1],x.origin=(0,o.Rd)((0,o.tY)(E,x.origin),B),x.beforeOrigin=(0,o.Rd)((0,o.tY)(E,x.beforeOrigin),B),x.originalBeforeOrigin=(0,o.tY)(E,x.originalBeforeOrigin);var G=x.targetClientRect,z=c[0]*c[1]>0?1:-1;G.top+=x.top-n.top,G.left+=x.left-n.left,s.style.transform="translate(".concat(-B[0],"px, ").concat(-B[1],"px) ").concat(v),this.updateState(m(m({},x),{direction:z,beforeDirection:z}),r)}},r.getRect=function(){return m(m({},t.prototype.getRect.call(this)),{children:this.moveables.map(function(t){return t.getRect()})})},r.triggerEvent=function(e,r,n){if(n||e.indexOf("Group")>-1)return t.prototype.triggerEvent.call(this,e,r);this._emitter.trigger(e,r)},r.updateAbles=function(){t.prototype.updateAbles.call(this,b(b([],this.props.ables,!0),[rc],!1),"Group")},r._updateTargets=function(){t.prototype._updateTargets.call(this),this._prevTarget=this.props.dragTarget||this.areaElement},r._updateEvents=function(){var t=this.state,e=this.props,r=this._prevTarget,n=e.dragTarget||this.areaElement;r!==n&&(eg(this,"targetGesto"),eg(this,"controlGesto"),t.target=null),t.target||(t.target=this.areaElement,this.controlBox.getElement().style.display="block",this.targetGesto=ro(this,n,"Group"),this.controlGesto=rs(this,this.controlBox.getElement(),"controlAbles","GroupControl"));var a=!eS(t.container,e.container);a&&(t.container=e.container);var i=this.differ.update(e.targets),o=i.added,s=i.changed,u=i.removed;(a||o.length||s.length||u.length)&&this.updateRect()},r._updateObserver=function(){},e.defaultProps=m(m({},rl.defaultProps),{transformOrigin:["50%","50%"],groupable:!0,dragArea:!0,keepRatio:!0,targets:[],defaultGroupRotate:0,defaultGroupOrigin:"50% 50%"}),e}(rl),rS=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.moveables=[],e}v(e,t);var r=e.prototype;return r.render=function(){var t=this,e=this.props,r=e.cspNonce,n=e.cssStyled,i=e.targets;return(0,f.createElement)(n,{cspNonce:r,ref:(0,a.KR)(this,"controlBox"),className:tQ("control-box")},i.map(function(e,r){return(0,f.createElement)(rl,m({key:"moveable"+r,ref:(0,a.DY)(t,"moveables",r)},t.props,{target:e,wrapperMoveable:t}))}))},r.componentDidUpdate=function(){},r.updateRect=function(t,e,r){void 0===r&&(r=!0),this.moveables.forEach(function(n){n.updateRect(t,e,r)})},r.getRect=function(){return m(m({},t.prototype.getRect.call(this)),{children:this.moveables.map(function(t){return t.getRect()})})},r.request=function(){return{request:function(){return this},requestEnd:function(){return this}}},r.dragStart=function(){return this},r.hitTest=function(){return 0},r.isInside=function(){return!1},r.isDragging=function(){return!1},r.updateRenderPoses=function(){},r.checkUpdate=function(){},r.triggerEvent=function(){},r.updateAbles=function(){},r._updateEvents=function(){},r._updateObserver=function(){},e}(rl),rR=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.refTargets=[],e.selectorMap={},e}v(e,t);var r=e.prototype;return e.makeStyled=function(){var t={};this.getTotalAbles().forEach(function(e){var r=e.css;r&&r.forEach(function(e){t[e]=!0})});var e=(0,i.xD)(t).join("\n");this.defaultStyled=(0,p.A)("div",(0,a.SS)(R,M+e))},e.getTotalAbles=function(){return b([eX,rc,rg,e_],this.defaultAbles,!0)},r.render=function(){var t,e,r,n=this.constructor;n.defaultStyled||n.makeStyled();var o=this.props,s=o.ables,u=o.props,l=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)0>e.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(r[n[a]]=t[n[a]]);return r}(o,["ables","props"]),c=(t=this._updateRefs(!0),e=this.selectorMap,r=[],t.forEach(function(t){if(t){if((0,i.Kg)(t)){e[t]&&r.push.apply(r,e[t]);return}r.push(t)}}),r),d=c.length>1,p=b(b([],n.getTotalAbles(),!0),s||[],!0),g=m(m(m({},l),u||{}),{ables:p,cssStyled:n.defaultStyled,customStyledMap:n.customStyledMap});return d?l.individualGroupable?(0,f.createElement)(rS,m({key:"individual-group",ref:(0,a.KR)(this,"moveable")},g,{target:null,targets:c})):(0,f.createElement)(rE,m({key:"group",ref:(0,a.KR)(this,"moveable")},g,{target:null,targets:c})):(0,f.createElement)(rl,m({key:"single",ref:(0,a.KR)(this,"moveable")},g,{target:c[0]}))},r.componentDidMount=function(){this._updateRefs()},r.componentDidUpdate=function(){this._updateRefs()},r.componentWillUnmount=function(){this.selectorMap={},this.refTargets=[]},r.getManager=function(){return this.moveable},r._updateRefs=function(t){var e=this.refTargets,r=ew(this.props.target||this.props.targets),n="undefined"!=typeof document,a=e.length!==r.length||e.some(function(t,e){var n=r[e];if((t||n)&&t!==n)return!0;return!1}),o=this.selectorMap,s={};return this.refTargets.forEach(function(t){(0,i.Kg)(t)&&(o[t]?s[t]=o[t]:n&&(a=!0,s[t]=[].slice.call(document.querySelectorAll(t))))}),this.refTargets=r,this.selectorMap=s,!t&&a&&this.forceUpdate(),r},e.defaultAbles=[],e.customStyledMap={},e.defaultStyled=null,!function(t,e,r,n){var a,i=arguments.length,o=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,r,n);else for(var s=t.length-1;s>=0;s--)(a=t[s])&&(o=(i<3?a(o):i>3?a(e,r,o):a(e,r))||o);i>3&&o&&Object.defineProperty(e,r,o)}([(0,a.RI)(G)],e.prototype,"moveable",void 0),e}(f.PureComponent);function rM(t){var e;return(e=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return v(e,t),e}(rR)).defaultAbles=t,e}}}]);
//# sourceMappingURL=55fae009-544b926b02cd168a.js.map