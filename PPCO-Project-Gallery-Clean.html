<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPCO Project Gallery</title>
    <meta name="description" content="Explore our comprehensive portfolio of construction and engineering projects across Saudi Arabia, showcasing our expertise in various sectors including NEOM, SPARK, and Aramco facilities.">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Space+Mono:wght@400;700&family=Syne:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* CSS Variables */
        :root {
            --primary-color: #273A73;
            --text-color: #333F70;
            --text-muted: #333f7080;
            --background-color: #ffffff;
            --accent-color: #344d99;
            --border-radius: 8px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --heading-font: 'Space Mono', monospace;
            --body-font: 'Syne', sans-serif;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--background-color);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            z-index: 1000;
            border-bottom: 1px solid rgba(39, 58, 115, 0.1);
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            height: 40px;
        }

        .nav-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            transition: background-color 0.3s ease;
        }

        .nav-link:hover {
            background-color: rgba(39, 58, 115, 0.1);
        }

        /* Main Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Hero Section */
        .hero {
            margin-top: 80px;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-family: var(--heading-font);
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.2rem;
            color: var(--text-muted);
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid var(--primary-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--accent-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-secondary {
            background-color: transparent;
            color: var(--primary-color);
        }

        .btn-secondary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* Project Sections */
        .section {
            padding: 4rem 0;
        }

        .section h2 {
            font-family: var(--heading-font);
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            text-align: center;
        }

        .section-description {
            text-align: center;
            font-size: 1.1rem;
            color: var(--text-muted);
            max-width: 800px;
            margin: 0 auto 3rem;
        }

        /* Project Grid */
        .project-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .project-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .project-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .project-content {
            padding: 1.5rem;
        }

        .project-title {
            font-family: var(--heading-font);
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .project-description {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        /* Gallery Grid */
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius);
            aspect-ratio: 1;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover {
            transform: scale(1.05);
        }

        .gallery-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .section h2 {
                font-size: 2rem;
            }

            .button-group {
                flex-direction: column;
                align-items: center;
            }

            .project-grid {
                grid-template-columns: 1fr;
            }

            .gallery-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }

        /* Utility Classes */
        .text-center {
            text-align: center;
        }

        .mb-2 {
            margin-bottom: 2rem;
        }

        .mt-4 {
            margin-top: 4rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <img src="./PPCO Project Gallery_files/cropped-Untitled-design-1-1.png" alt="PPCO Logo" class="logo">
            <a href="ALWAYS ONE STEP AHEAD.html" class="nav-link">Home</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>PPCO Project Gallery</h1>
            <p>Explore our comprehensive portfolio of construction and engineering projects across Saudi Arabia, showcasing our expertise in various sectors including NEOM, SPARK, and Aramco facilities.</p>
            <div class="button-group">
                <a href="#" class="btn btn-primary">Download Profile</a>
                <a href="#" class="btn btn-secondary">Watch Profile Video</a>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container">
        <!-- NEOM Projects Section -->
        <section class="section">
            <h2>NEOM Projects</h2>
            <p class="section-description">Pioneering the future of sustainable development in NEOM with cutting-edge construction and engineering solutions.</p>
            
            <div class="project-grid">
                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/H7GpNBdxXHUvFs8sUHJ8K.png" alt="NEOM Project 1" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">NEOM Infrastructure Development</h3>
                        <p class="project-description">Advanced infrastructure development supporting NEOM's vision for the future.</p>
                    </div>
                </div>
                
                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/My17zFx_RWKlMs5WuxHL0.png" alt="NEOM Project 2" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">Sustainable Construction</h3>
                        <p class="project-description">Eco-friendly construction practices aligned with NEOM's sustainability goals.</p>
                    </div>
                </div>
                
                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/NGtwlA79s8wHluHOVjcq8.png" alt="NEOM Project 3" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">Smart City Integration</h3>
                        <p class="project-description">Integrating smart technologies into urban development projects.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- SPARK Projects Section -->
        <section class="section">
            <h2>SPARK Projects</h2>
            <p class="section-description">Delivering innovative solutions for SPARK development projects with precision and excellence.</p>

            <div class="project-grid">
                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/OIhguXXGVNzWhHI_DcsyY.png" alt="SPARK Project 1" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">SPARK Commercial Complex</h3>
                        <p class="project-description">Modern commercial development featuring state-of-the-art facilities.</p>
                    </div>
                </div>

                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/VlA-saI4hi-euhRQHGwio.png" alt="SPARK Project 2" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">Residential Development</h3>
                        <p class="project-description">High-quality residential units designed for modern living.</p>
                    </div>
                </div>

                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/WHx3ApX2yu59RUssQZJqu.png" alt="SPARK Project 3" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">Mixed-Use Development</h3>
                        <p class="project-description">Integrated development combining commercial and residential spaces.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Aramco Projects Section -->
        <section class="section">
            <h2>Aramco Projects</h2>
            <p class="section-description">Supporting Saudi Aramco's operations with specialized construction and engineering services.</p>

            <div class="project-grid">
                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/EKx7eQQh8O_eA2OrgRoMa.png" alt="Aramco Project 1" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">Industrial Facilities</h3>
                        <p class="project-description">Specialized industrial construction for oil and gas operations.</p>
                    </div>
                </div>

                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/ASTdOnfodDlGaM7V23zxW.png" alt="Aramco Project 2" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">Infrastructure Support</h3>
                        <p class="project-description">Critical infrastructure supporting Aramco's operational excellence.</p>
                    </div>
                </div>

                <div class="project-card">
                    <img src="./PPCO Project Gallery_files/_D5-1ZstgdZgNksGa0kzB.png" alt="Aramco Project 3" class="project-image">
                    <div class="project-content">
                        <h3 class="project-title">Maintenance & Upgrades</h3>
                        <p class="project-description">Ongoing maintenance and facility upgrades for optimal performance.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Project Gallery Section -->
        <section class="section">
            <h2>Project Gallery</h2>
            <p class="section-description">A visual showcase of our completed projects across various sectors.</p>

            <div class="gallery-grid">
                <div class="gallery-item">
                    <img src="./PPCO Project Gallery_files/aWuo3xjCya6YiMia7fGgu.png" alt="Gallery Image 1" class="gallery-image">
                </div>
                <div class="gallery-item">
                    <img src="./PPCO Project Gallery_files/awAvTY_wkaH-XFkKaG34_.png" alt="Gallery Image 2" class="gallery-image">
                </div>
                <div class="gallery-item">
                    <img src="./PPCO Project Gallery_files/iL6_P8rTXcSn-2MGozw2I.png" alt="Gallery Image 3" class="gallery-image">
                </div>
                <div class="gallery-item">
                    <img src="./PPCO Project Gallery_files/-0XXq1IbaucaY3-Tq4q-C.png" alt="Gallery Image 4" class="gallery-image">
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="section text-center">
            <h2>Ready to Start Your Project?</h2>
            <p class="section-description">Contact us today to discuss how PPCO can bring your construction and engineering vision to life.</p>
            <div class="button-group mt-4">
                <a href="#" class="btn btn-primary">Get In Touch</a>
                <a href="#" class="btn btn-secondary">Request Quote</a>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer style="background-color: var(--primary-color); color: white; text-align: center; padding: 2rem 0; margin-top: 4rem;">
        <div class="container">
            <p>&copy; 2024 PPCO Contracting. All rights reserved.</p>
            <p style="margin-top: 0.5rem; opacity: 0.8;">Premier contracting in Saudi Arabia since 2006</p>
        </div>
    </footer>

    <!-- JavaScript for Enhanced Functionality -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Gallery image click handler (can be extended for lightbox functionality)
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.addEventListener('click', function() {
                // Add lightbox or modal functionality here
                console.log('Gallery item clicked:', this);
            });
        });

        // Add scroll effect to navbar
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections for animation
        document.querySelectorAll('.section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });
    </script>
</body>
</html>
